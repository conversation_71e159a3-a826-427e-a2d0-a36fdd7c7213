<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Rata kredytu hipotecznego kalkulator</title>
    <meta name="description" content="Rata kredytu hipotecznego kalkulator - oblicz miesięczną ratę kredytu mieszkaniowego, sprawdź wpływ oprocentowania i okresu na wysokość raty kredytu hipotecznego.">
    <meta name="keywords" content="rata kredytu hipotecznego kalkulator, miesięczna rata kredytu, oblicz ratę hipoteczną, wys<PERSON><PERSON><PERSON> raty kredytu, rata kredytu mieszkaniowego">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/rata-kredytu-hipotecznego-kalkulator.html" itemprop="item"><span itemprop="name">rata kredytu hipotecznego kalkulator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Rata kredytu hipotecznego kalkulator</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz miesięczną ratę kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/rata-kredytu-hipotecznego-kalkulator.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Cena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita cena nieruchomości, którą chcesz kupić. Na podstawie tej kwoty zostanie obliczona kwota kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="chouseprice" id="chouseprice" value="600000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota własnych środków, którą wpłacasz przy zakupie nieruchomości. Wyższy wkład własny oznacza niższą ratę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czas spłaty kredytu w latach. Dłuższy okres oznacza niższą miesięczną ratę, ale wyższy całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu hipotecznego. Sprawdź aktualne stawki w różnych bankach.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.2" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ raty kredytu</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype" onchange="updateRateType();">
                                        <option value="equal" selected="">Rata równa (annuitetowa)</option>
                                        <option value="decreasing">Rata malejąca</option>
                                        <option value="seasonal">Rata sezonowa</option>
                                        <option value="graduated">Rata rosnąca</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="seasonaldetails" style="display:none;">
                                <td align="right">Miesiące wyższej raty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesiące w roku, w których płacisz wyższą ratę (np. po otrzymaniu premii rocznej).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cseasonalmonths" id="cseasonalmonths" value="2" class="inhalf"></td>
                                <td>miesięcy</td>
                            </tr>
                            <tr id="graduateddetails" style="display:none;">
                                <td align="right">Wzrost raty rocznie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Procentowy wzrost raty każdego roku w przypadku raty rosnącej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cgraduatedincrease" id="cgraduatedincrease" value="3" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddstress" class="cbcontainer">
                                        <input type="checkbox" name="caddstress" id="caddstress" value="1" checked="" onclick="cshstress();">
                                        <span class="cbmark"></span>
                                        <b><span id="cstressdesc">Test warunków skrajnych</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cstress" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Scenariusze stresowe</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wzrost oprocentowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Symulacja wzrostu oprocentowania o określoną liczbę punktów procentowych.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cstressrate" id="cstressrate" value="2.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Spadek dochodów <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Symulacja spadku dochodów o określony procent (np. utrata pracy przez jednego z kredytobiorców).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cstressincome" id="cstressincome" value="30" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Dodatkowe wydatki <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe miesięczne wydatki (np. dziecko, choroba, inne zobowiązania).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cstressexpenses" id="cstressexpenses" value="1500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="ccomparebanks" class="cbcontainer">
                                        <input type="checkbox" name="ccomparebanks" id="ccomparebanks" value="1" onclick="cshbanks();">
                                        <span class="cbmark"></span>
                                        <b><span id="cbanksdesc">Porównaj oferty banków</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cbanks" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">PKO BP - oprocentowanie</td>
                                                    <td align="right"><input type="text" name="cpkorate" id="cpkorate" value="7.45" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">ING - oprocentowanie</td>
                                                    <td align="right"><input type="text" name="cingrate" id="cingrate" value="7.25" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">mBank - oprocentowanie</td>
                                                    <td align="right"><input type="text" name="cmbankrate" id="cmbankrate" value="7.35" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Santander - oprocentowanie</td>
                                                    <td align="right"><input type="text" name="csantanderrate" id="csantanderrate" value="7.55" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz ratę kredytu" onclick="calculatePaymentRate();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 3 542 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('UmF0YSBrcmVkeXR1IGhpcG90ZWN6bmVnbyBrYWxrdWxhdG9y', 0, 'UmF0YSBrcmVkeXR1IGhpcG90ZWN6bmVnbyBrYWxrdWxhdG9y', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MyA1NDIgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Podstawowy</b></td>
                                        <td align="right"><b>Stresowy</b></td>
                                        <td align="right"><b>Różnica</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>3 542 zł</b></td>
                                        <td align="right"><b>4 285 zł</b></td>
                                        <td align="right"><b>+743 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu</td>
                                        <td align="right">480 000 zł</td>
                                        <td align="right">480 000 zł</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr>
                                        <td>Oprocentowanie</td>
                                        <td align="right">7.2%</td>
                                        <td align="right">9.2%</td>
                                        <td align="right">+2.0 p.p.</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">1 062 600 zł</td>
                                        <td align="right">1 285 500 zł</td>
                                        <td align="right">+222 900 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">582 600 zł</td>
                                        <td align="right">805 500 zł</td>
                                        <td align="right">+222 900 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza raty kredytu:</h3>
                                            <p><strong>Typ raty:</strong> Równa (annuitetowa)</p>
                                            <p><strong>Udział w dochodach:</strong> 24.6% (rekomendowane max 35%)</p>
                                            <p><strong>Bezpieczeństwo finansowe:</strong> ✓ Rata w bezpiecznych granicach</p>
                                            <p><strong>Test stresowy:</strong> ⚠️ Wzrost raty o 21% w scenariuszu kryzysowym</p>
                                            <p><strong>Rekomendacja:</strong> Rata bezpieczna, ale przygotuj się na możliwe wzrosty</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie rat w różnych bankach</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Bank</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3200 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">3500 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">3800 zł</text>
                    
                    <!-- ING -->
                    <rect x="60" y="95" width="40" height="50" fill="#ff6600" opacity="0.8"></rect>
                    <text x="80" y="155" class="mcllabelx">ING</text>
                    <text x="80" y="90" class="mcllabelx" style="fill:#000;">3485 zł</text>
                    
                    <!-- mBank -->
                    <rect x="120" y="88" width="40" height="57" fill="#00a651" opacity="0.8"></rect>
                    <text x="140" y="155" class="mcllabelx">mBank</text>
                    <text x="140" y="83" class="mcllabelx" style="fill:#000;">3528 zł</text>
                    
                    <!-- Bazowy -->
                    <rect x="180" y="92" width="40" height="53" fill="#45b7d1" opacity="0.8"></rect>
                    <text x="200" y="155" class="mcllabelx">Bazowy</text>
                    <text x="200" y="87" class="mcllabelx" style="fill:#000;">3542 zł</text>
                    
                    <!-- PKO BP -->
                    <rect x="240" y="82" width="40" height="63" fill="#dc143c" opacity="0.8"></rect>
                    <text x="260" y="155" class="mcllabelx">PKO</text>
                    <text x="260" y="77" class="mcllabelx" style="fill:#000;">3598 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Porównanie banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Test warunków skrajnych</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Scenariusz</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Ocena ryzyka</th>
                        </tr>
                        <tr>
                            <td>Podstawowy</td>
                            <td>7.2%</td>
                            <td>3 542 zł</td>
                            <td>1 062 600 zł</td>
                            <td>✓ Niskie</td>
                        </tr>
                        <tr>
                            <td>Wzrost stóp +1%</td>
                            <td>8.2%</td>
                            <td>3 825 zł</td>
                            <td>1 147 500 zł</td>
                            <td>⚠️ Średnie</td>
                        </tr>
                        <tr>
                            <td>Wzrost stóp +2%</td>
                            <td>9.2%</td>
                            <td>4 115 zł</td>
                            <td>1 234 500 zł</td>
                            <td>⚠️ Wysokie</td>
                        </tr>
                        <tr>
                            <td>Kryzys (-30% dochodów)</td>
                            <td>7.2%</td>
                            <td>3 542 zł</td>
                            <td>1 062 600 zł</td>
                            <td>❌ Bardzo wysokie</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Rata kredytu hipotecznego kalkulator - kompleksowa analiza miesięcznych rat</h2>
            <p>Rata kredytu hipotecznego to najważniejszy element planowania budżetu przy zakupie nieruchomości. Nasz kalkulator raty kredytu hipotecznego nie tylko oblicza miesięczną ratę, ale także przeprowadza testy warunków skrajnych i porównuje oferty różnych banków, pomagając Ci podjąć świadomą decyzję finansową.</p>

            <h3>Rodzaje rat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Rata równa (annuitetowa):</h4>
                <ul>
                    <li><strong>Stała wysokość</strong> - przez cały okres kredytowania</li>
                    <li><strong>Przewidywalność</strong> - łatwe planowanie budżetu domowego</li>
                    <li><strong>Zmienny udział</strong> - początkowo więcej odsetek, później więcej kapitału</li>
                    <li><strong>Najpopularniejsza</strong> - wybierana przez 95% kredytobiorców</li>
                    <li><strong>Stabilność finansowa</strong> - brak niespodzianek w budżecie</li>
                </ul>

                <h4>Rata malejąca:</h4>
                <ul>
                    <li><strong>Zmniejszająca się</strong> - rata maleje z każdym miesiącem</li>
                    <li><strong>Równa spłata kapitału</strong> - stała kwota kapitału każdego miesiąca</li>
                    <li><strong>Malejące odsetki</strong> - odsetki liczone od malejącego salda</li>
                    <li><strong>Wyższa na początku</strong> - większe obciążenie w pierwszych latach</li>
                    <li><strong>Niższy całkowity koszt</strong> - mniejsze odsetki łącznie</li>
                    <li><strong>Wymaga wyższej zdolności</strong> - bank sprawdza możliwość spłaty pierwszych rat</li>
                </ul>

                <h4>Rata sezonowa:</h4>
                <ul>
                    <li><strong>Zmienna w roku</strong> - wyższe raty w określonych miesiącach</li>
                    <li><strong>Dostosowana do dochodów</strong> - wyższe raty po premii rocznej</li>
                    <li><strong>Elastyczność</strong> - możliwość dopasowania do cyklu zarobków</li>
                    <li><strong>Rzadko oferowana</strong> - tylko niektóre banki</li>
                    <li><strong>Wymaga dyscypliny</strong> - oszczędzanie na wyższe raty</li>
                </ul>

                <h4>Rata rosnąca:</h4>
                <ul>
                    <li><strong>Wzrastająca w czasie</strong> - rata rośnie co roku</li>
                    <li><strong>Niższa na początku</strong> - łatwiejszy start</li>
                    <li><strong>Dostosowana do inflacji</strong> - wzrost wraz z dochodami</li>
                    <li><strong>Ryzykowna</strong> - wymaga pewności wzrostu dochodów</li>
                    <li><strong>Rzadko stosowana</strong> - w Polsce mało popularna</li>
                </ul>
            </div>

            <h3>Test warunków skrajnych - dlaczego jest ważny?</h3>
            <div style="margin: 15px 0;">
                <h4>Scenariusze kryzysowe:</h4>
                <ul>
                    <li><strong>Wzrost stóp procentowych</strong> - NBP może podnieść stopy o 2-3 p.p.</li>
                    <li><strong>Utrata pracy</strong> - jeden z kredytobiorców traci źródło dochodu</li>
                    <li><strong>Choroba</strong> - dodatkowe wydatki na leczenie</li>
                    <li><strong>Dziecko</strong> - zwiększone wydatki, zmniejszone dochody</li>
                    <li><strong>Rozwód</strong> - podział majątku i dochodów</li>
                    <li><strong>Kryzys gospodarczy</strong> - obniżenie wynagrodzeń</li>
                </ul>

                <h4>Jak przygotować się na kryzysy:</h4>
                <ul>
                    <li><strong>Rezerwa finansowa</strong> - 6-12 miesięcznych wydatków</li>
                    <li><strong>Ubezpieczenie spłaty</strong> - ochrona przed utratą dochodów</li>
                    <li><strong>Konserwatywne planowanie</strong> - nie wykorzystuj całej zdolności</li>
                    <li><strong>Elastyczne zatrudnienie</strong> - możliwość dodatkowych zarobków</li>
                    <li><strong>Nadpłaty w dobrych czasach</strong> - zmniejszenie salda kredytu</li>
                </ul>
            </div>

            <h3>Porównanie ofert bankowych:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Bank</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Miesięczna rata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Całkowity koszt</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oszczędność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>ING Bank Śląski</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.25%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>3 485 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 045 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">17 100 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">mBank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.35%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 528 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 058 400 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4 200 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bazowy (7.2%)</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.20%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 542 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 062 600 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">PKO Bank Polski</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 598 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 079 400 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-16 800 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Santander Bank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.55%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 642 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 092 600 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-30 000 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Czynniki wpływające na wysokość raty:</h3>
            <ul>
                <li><strong>Kwota kredytu</strong> - bezpośrednio proporcjonalna do raty</li>
                <li><strong>Okres kredytowania</strong> - dłuższy okres = niższa rata</li>
                <li><strong>Oprocentowanie</strong> - każdy 1% to około 10% różnicy w racie</li>
                <li><strong>Typ raty</strong> - annuitetowa vs malejąca</li>
                <li><strong>Wkład własny</strong> - wyższy wkład = niższa rata</li>
                <li><strong>Profil klienta</strong> - marża zależy od ryzyka</li>
                <li><strong>Warunki rynkowe</strong> - stopy NBP, konkurencja banków</li>
            </ul>

            <h3>Optymalizacja wysokości raty:</h3>
            <div style="margin: 15px 0;">
                <h4>Sposoby obniżenia raty:</h4>
                <ul>
                    <li><strong>Zwiększ wkład własny</strong> - każde 10% to około 10% niższa rata</li>
                    <li><strong>Wydłuż okres kredytowania</strong> - 30 lat zamiast 20 lat</li>
                    <li><strong>Negocjuj oprocentowanie</strong> - porównaj oferty banków</li>
                    <li><strong>Popraw profil kredytowy</strong> - wyższe dochody, lepsza historia</li>
                    <li><strong>Rozważ współkredytobiorcę</strong> - łączenie dochodów</li>
                    <li><strong>Wybierz odpowiedni moment</strong> - niskie stopy procentowe</li>
                </ul>

                <h4>Kiedy nie warto obniżać raty:</h4>
                <ul>
                    <li><strong>Kosztem zbyt długiego okresu</strong> - przepłacanie odsetek</li>
                    <li><strong>Wykorzystaniem całej zdolności</strong> - brak rezerwy</li>
                    <li><strong>Rezygnacją z ubezpieczeń</strong> - zwiększenie ryzyka</li>
                    <li><strong>Wyborem najtańszego banku</strong> - jakość obsługi</li>
                </ul>
            </div>

            <h3>Bezpieczne planowanie raty kredytu:</h3>
            <ul>
                <li><strong>Zasada 35%</strong> - rata nie powinna przekraczać 35% dochodów netto</li>
                <li><strong>Rezerwa 20%</strong> - nie wykorzystuj całej zdolności kredytowej</li>
                <li><strong>Test stresowy</strong> - sprawdź ratę przy wyższym oprocentowaniu</li>
                <li><strong>Planowanie przyszłości</strong> - dzieci, emerytura, zmiany zawodowe</li>
                <li><strong>Elastyczność</strong> - możliwość nadpłat i zawieszenia rat</li>
                <li><strong>Ubezpieczenia</strong> - ochrona przed utratą dochodów</li>
                <li><strong>Rezerwa finansowa</strong> - 6-12 miesięcznych wydatków</li>
            </ul>

            <h3>Błędy przy planowaniu raty:</h3>
            <ul>
                <li><strong>Skupienie tylko na racie</strong> - ignorowanie całkowitego kosztu</li>
                <li><strong>Maksymalne wykorzystanie zdolności</strong> - brak bufora bezpieczeństwa</li>
                <li><strong>Ignorowanie inflacji</strong> - rata może być łatwiejsza w przyszłości</li>
                <li><strong>Brak testów stresowych</strong> - nieprzygotowanie na kryzysy</li>
                <li><strong>Wybór najtańszej oferty</strong> - bez analizy warunków</li>
                <li><strong>Nieuwzględnienie kosztów dodatkowych</strong> - podatki, ubezpieczenia</li>
                <li><strong>Planowanie na obecne dochody</strong> - bez uwzględnienia zmian</li>
            </ul>

            <h3>Porady ekspertów:</h3>
            <ul>
                <li>Zawsze przeprowadź test warunków skrajnych przed podjęciem decyzji</li>
                <li>Porównaj oferty minimum 3-4 banków</li>
                <li>Negocjuj nie tylko oprocentowanie, ale też inne warunki</li>
                <li>Zachowaj rezerwę finansową na nieprzewidziane wydatki</li>
                <li>Rozważ ubezpieczenie spłaty kredytu</li>
                <li>Planuj nadpłaty w okresach wyższych dochodów</li>
                <li>Monitoruj zmiany stóp procentowych i ofert bankowych</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/szczegolowy-kalkulator-kredytu-hipotecznego.html">Szczegółowy kalkulator</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">Wcześniejsza spłata</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Aktualne stawki banków</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Najlepsze oferty:</strong><br>
                        ING: <a href="#" onclick="return setRate('7.25');">7.25%</a><br>
                        mBank: <a href="#" onclick="return setRate('7.35');">7.35%</a><br>
                        PKO BP: <a href="#" onclick="return setRate('7.45');">7.45%</a><br>
                        Santander: <a href="#" onclick="return setRate('7.55');">7.55%</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Rata kredytu hipotecznego kalkulator - oblicz miesięczną ratę kredytu mieszkaniowego i sprawdź wpływ różnych parametrów na wysokość raty.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateRateType() {
    var type = document.getElementById('cpaymenttype').value;
    var seasonal = document.getElementById('seasonaldetails');
    var graduated = document.getElementById('graduateddetails');
    
    seasonal.style.display = (type === 'seasonal') ? 'table-row' : 'none';
    graduated.style.display = (type === 'graduated') ? 'table-row' : 'none';
}

function cshstress() {
    var checkbox = document.getElementById('caddstress');
    var stressDiv = document.getElementById('cstress');
    if (checkbox.checked) {
        stressDiv.style.display = 'block';
    } else {
        stressDiv.style.display = 'none';
    }
}

function cshbanks() {
    var checkbox = document.getElementById('ccomparebanks');
    var banksDiv = document.getElementById('cbanks');
    if (checkbox.checked) {
        banksDiv.style.display = 'block';
    } else {
        banksDiv.style.display = 'none';
    }
}

function setRate(rate) {
    document.getElementById('cinterestrate').value = rate;
    return false;
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Test warunków skrajnych &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Porównanie banków</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Porównanie banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Test warunków skrajnych</a>';
    }
    return false;
}
</script>

<script>
function calculatePaymentRate() {
    // 获取输入值
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算总成本
    var totalPayments = monthlyPayment * numPayments;
    var totalInterest = totalPayments - loanAmount;

    // 计算年度成本
    var yearlyPayments = monthlyPayment * 12;
    var yearlyInterest = totalInterest / loanTerm;

    // 更新显示
    updatePaymentRateResults(loanAmount, monthlyPayment, totalPayments, totalInterest,
                           yearlyPayments, yearlyInterest, loanTerm);
}

function updatePaymentRateResults(loanAmount, monthlyPayment, totalPayments, totalInterest,
                                yearlyPayments, yearlyInterest, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Rata kredytu: &nbsp; ' + formatNumber(monthlyPayment) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 3) {
                switch(i) {
                    case 2: // Miesięczna rata
                        cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        cells[2].innerHTML = '<b>' + formatNumber(totalPayments) + ' zł</b>';
                        break;
                    case 3: // Kwota kredytu
                        cells[2].innerHTML = formatNumber(loanAmount) + ' zł';
                        break;
                    case 4: // Odsetki łącznie
                        cells[2].innerHTML = formatNumber(totalInterest) + ' zł';
                        break;
                    case 5: // Roczne płatności
                        cells[1].innerHTML = formatNumber(yearlyPayments) + ' zł';
                        break;
                    case 6: // Roczne odsetki
                        cells[1].innerHTML = formatNumber(yearlyInterest) + ' zł';
                        break;
                }
            }
        }
    }

    // 更新szczegóły raty
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var interestPercentage = (totalInterest / loanAmount) * 100;
        detailsSection.innerHTML =
            '<h3>Szczegóły raty kredytu:</h3>' +
            '<p><strong>Miesięczna rata:</strong> ' + formatNumber(monthlyPayment) + ' zł</p>' +
            '<p><strong>Łączne spłaty:</strong> ' + formatNumber(totalPayments) + ' zł</p>' +
            '<p><strong>Łączne odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + interestPercentage.toFixed(1) + '% kwoty kredytu)</p>' +
            '<p><strong>Okres spłaty:</strong> ' + loanTerm + ' lat (' + (loanTerm * 12) + ' miesięcy)</p>' +
            '<p><strong>Średnie roczne odsetki:</strong> ' + formatNumber(yearlyInterest) + ' zł</p>';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    calculatePaymentRate();
}

function saveCalResult() {
    alert('Wyniki kalkulatora raty kredytu zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePaymentRate();
};
</script>

</body>
</html>
