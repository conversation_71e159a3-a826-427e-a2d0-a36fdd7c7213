<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator nadpłacania kredytu hipotecznego</title>
    <meta name="description" content="Kalkulator nadpłacania kredytu hipotecznego - oblicz oszczędności z nadpłat kredytu mieszkaniowego, sprawdź opłacalność nadpłacania kredytu hipotecznego 2025.">
    <meta name="keywords" content="kalkulator nadpłacania kredytu hipotecznego, nadpłacanie kredytu hipotecznego, oszcz<PERSON><PERSON>ości nadpłat, opła<PERSON><PERSON><PERSON><PERSON> nadpłat">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-nadplacania-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator nadpłacania kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator nadpłacania kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz oszczędności z nadpłacania kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-nadplacania-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne saldo kredytu hipotecznego. Sprawdź w bankowości internetowej lub harmonogramie spłat.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanbalance" id="cloanbalance" value="480000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Miesięczna rata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualna miesięczna rata kredytu hipotecznego. Sprawdź w harmonogramie spłat.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyrate" id="cmonthlyrate" value="3600" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego. WIBOR 3M + marża banku.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostały okres spłaty kredytu w latach. Sprawdź w bankowości internetowej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cremainingterm" id="cremainingterm" value="20" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ nadpłacania</td>
                                <td align="left" colspan="2">
                                    <select name="coverpaymenttype" id="coverpaymenttype" onchange="updateOverpaymentType();">
                                        <option value="monthly" selected="">Miesięczne nadpłacanie</option>
                                        <option value="yearly">Roczne nadpłacanie</option>
                                        <option value="quarterly">Kwartalne nadpłacanie</option>
                                        <option value="onetime">Jednorazowe nadpłacanie</option>
                                        <option value="mixed">Nadpłacanie mieszane</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="monthlyoverpayment">
                                <td align="right">Miesięczne nadpłacanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Stała miesięczna nadpłata kredytu hipotecznego. Można ustawić w bankowości internetowej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyoverpayment" id="cmonthlyoverpayment" value="500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="yearlyoverpayment" style="display:none;">
                                <td align="right">Roczne nadpłacanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna nadpłata z premii, zwrotu podatku lub 13. pensji.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cyearlyoverpayment" id="cyearlyoverpayment" value="8000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="quarterlyoverpayment" style="display:none;">
                                <td align="right">Kwartalne nadpłacanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Nadpłata co kwartał. Kompromis między częstotliwością a wygodą.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cquarterlyoverpayment" id="cquarterlyoverpayment" value="2000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="onetimeoverpayment" style="display:none;">
                                <td align="right">Jednorazowe nadpłacanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Jednorazowa duża nadpłata ze sprzedaży inwestycji lub spadku.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="conetimeoverpayment" id="conetimeoverpayment" value="50000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="mixedoverpayment" style="display:none;">
                                <td align="right">Miesięczne + roczne <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kombinacja miesięcznych i rocznych nadpłat dla maksymalnych oszczędności.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right">
                                    <input type="text" name="cmixedmonthly" id="cmixedmonthly" value="300" class="in4char indollar" placeholder="Miesięczna"> + 
                                    <input type="text" name="cmixedyearly" id="cmixedyearly" value="5000" class="in4char indollar" placeholder="Roczna">
                                </td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Efekt nadpłacania</td>
                                <td align="left" colspan="2">
                                    <select name="coverpaymenteffect" id="coverpaymenteffect">
                                        <option value="reduce_term" selected="">Skrócenie okresu spłaty</option>
                                        <option value="reduce_payment">Zmniejszenie miesięcznej raty</option>
                                        <option value="mixed_effect">Efekt mieszany</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddfees" class="cbcontainer">
                                        <input type="checkbox" name="caddfees" id="caddfees" value="1" onclick="cshfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cfeesdesc">Opłaty za nadpłacanie</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cfees" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty za nadpłacanie</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja za nadpłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja banku za nadpłatę kredytu. 0-2% nadpłacanej kwoty.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="coverpayfee" id="coverpayfee" value="1.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Maksymalna prowizja <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Maksymalna prowizja za nadpłatę w złotych. Limit górny opłaty.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmaxfee" id="cmaxfee" value="2500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Darmowe nadpłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba darmowych nadpłat rocznie. Zwykle 2-4 nadpłaty bez prowizji.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cfreeoverpayments" id="cfreeoverpayments" value="2" class="innormal"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddscenarios" class="cbcontainer">
                                        <input type="checkbox" name="caddscenarios" id="caddscenarios" value="1" checked="" onclick="cshscenarios();">
                                        <span class="cbmark"></span>
                                        <b><span id="cscenariosdesc">Porównanie scenariuszy</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cscenarios" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Scenariusze nadpłacania</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 1 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwszy scenariusz nadpłacania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario1" id="cscenario1" value="250" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 2 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Drugi scenariusz nadpłacania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario2" id="cscenario2" value="500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 3 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzeci scenariusz nadpłacania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario3" id="cscenario3" value="1000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz nadpłacanie" onclick="calculateOverpaymentSavings();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                    <input type="button" value="Zapisz" onclick="saveCalculation();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędność: &nbsp; 142 850 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBuYWRwxYJhY2FuaWEga3JlZHl0dSBoaXBvdGVjem5lZ28=', 0, 'S2Fsa3VsYXRvciBuYWRwxYJhY2FuaWEga3JlZHl0dSBoaXBvdGVjem5lZ28=', 'T3N6Y3rEmWRub8WbxIc=', 'MTQyIDg1MCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez nadpłat</b></td>
                                        <td align="right"><b>Z nadpłatami</b></td>
                                        <td align="right"><b>Oszczędność</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>3 600 zł</b></td>
                                        <td align="right"><b>4 100 zł</b></td>
                                        <td align="right"><b>+500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty</td>
                                        <td align="right">20 lat</td>
                                        <td align="right">15 lat 2 miesiące</td>
                                        <td align="right">4 lata 10 miesięcy</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">864 000 zł</td>
                                        <td align="right">742 200 zł</td>
                                        <td align="right">121 800 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">384 000 zł</td>
                                        <td align="right">262 200 zł</td>
                                        <td align="right">121 800 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Łączne nadpłaty</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">91 000 zł</td>
                                        <td align="right">-91 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Netto oszczędność</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                        <td align="right">142 850 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza nadpłacania:</h3>
                                            <p><strong>Efektywność:</strong> Każda złotówka nadpłaty oszczędza 1.57 zł odsetek</p>
                                            <p><strong>ROI:</strong> 157% zwrotu z inwestycji w nadpłacanie</p>
                                            <p><strong>Skrócenie:</strong> 4 lata 10 miesięcy wcześniejsze zakończenie</p>
                                            <p><strong>Profil klienta:</strong> Standardowy kredyt hipoteczny</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Nadpłacanie bardzo opłacalne</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie scenariuszy nadpłacania</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Nadpłacanie</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">80K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">120K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">160K zł</text>
                    
                    <!-- 0 zł -->
                    <rect x="50" y="145" width="35" height="0" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">0 zł</text>
                    <text x="67" y="140" class="mcllabelx" style="fill:#000;">0 zł</text>
                    
                    <!-- 250 zł -->
                    <rect x="95" y="130" width="35" height="15" fill="#f39c12" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">250 zł</text>
                    <text x="112" y="125" class="mcllabelx" style="fill:#000;">95K zł</text>
                    
                    <!-- 500 zł -->
                    <rect x="140" y="105" width="35" height="40" fill="#27ae60" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">500 zł</text>
                    <text x="157" y="100" class="mcllabelx" style="fill:#000;">143K zł</text>
                    
                    <!-- 1000 zł -->
                    <rect x="185" y="85" width="35" height="60" fill="#2980b9" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">1000 zł</text>
                    <text x="202" y="80" class="mcllabelx" style="fill:#000;">185K zł</text>
                    
                    <!-- 1500 zł -->
                    <rect x="230" y="75" width="35" height="70" fill="#8e44ad" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">1500 zł</text>
                    <text x="247" y="70" class="mcllabelx" style="fill:#000;">205K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Scenariusze nadpłacania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Nadpłacanie</th>
                            <th>Oszczędność</th>
                            <th>Skrócenie</th>
                            <th>ROI</th>
                            <th>Efektywność</th>
                        </tr>
                        <tr>
                            <td>0 zł</td>
                            <td>0 zł</td>
                            <td>0 miesięcy</td>
                            <td>0%</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>250 zł</td>
                            <td>95 200 zł</td>
                            <td>28 miesięcy</td>
                            <td>145%</td>
                            <td>1.45 zł/zł</td>
                        </tr>
                        <tr>
                            <td>500 zł</td>
                            <td>142 850 zł</td>
                            <td>58 miesięcy</td>
                            <td>157%</td>
                            <td>1.57 zł/zł</td>
                        </tr>
                        <tr>
                            <td>1000 zł</td>
                            <td>185 600 zł</td>
                            <td>78 miesięcy</td>
                            <td>172%</td>
                            <td>1.72 zł/zł</td>
                        </tr>
                        <tr>
                            <td>1500 zł</td>
                            <td>205 800 zł</td>
                            <td>88 miesięcy</td>
                            <td>178%</td>
                            <td>1.78 zł/zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator nadpłacania kredytu hipotecznego - maksymalizuj oszczędności 2025</h2>
            <p>Nadpłacanie kredytu hipotecznego to jedna z najefektywniejszych strategii finansowych pozwalających zaoszczędzić dziesiątki tysięcy złotych na odsetkach. Nasz kalkulator nadpłacania kredytu hipotecznego pomoże Ci precyzyjnie obliczyć oszczędności z nadpłat, sprawdzić opłacalność różnych strategii i zoptymalizować spłatę kredytu mieszkaniowego.</p>

            <h3>Typy nadpłacania kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Miesięczne nadpłacanie:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Stała dodatkowa kwota każdego miesiąca</li>
                    <li><strong>Ustawienie:</strong> Bankowość internetowa lub zlecenie stałe</li>
                    <li><strong>Elastyczność:</strong> Możliwość zmiany kwoty w każdej chwili</li>
                    <li><strong>Efektywność:</strong> Najwyższa - każda złotówka oszczędza 1.57 zł odsetek</li>
                    <li><strong>Zalety:</strong> Automatyzacja, systematyczność, maksymalne oszczędności</li>
                    <li><strong>Rekomendacja:</strong> Dla osób z regularnym dochodem</li>
                </ul>

                <h4>Roczne nadpłacanie:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Jedna większa nadpłata raz w roku</li>
                    <li><strong>Źródła:</strong> Premia roczna, zwrot podatku, 13. pensja</li>
                    <li><strong>Elastyczność:</strong> Dowolny termin w roku</li>
                    <li><strong>Efektywność:</strong> Dobra - znaczące oszczędności przy niskim wysiłku</li>
                    <li><strong>Zalety:</strong> Nie obciąża miesięcznego budżetu</li>
                    <li><strong>Rekomendacja:</strong> Dla osób z nieregularnymi dochodami</li>
                </ul>

                <h4>Kwartalne nadpłacanie:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Nadpłata co 3 miesiące</li>
                    <li><strong>Planowanie:</strong> Dostosowanie do cyklu dochodów</li>
                    <li><strong>Elastyczność:</strong> Kompromis między częstotliwością a wygodą</li>
                    <li><strong>Efektywność:</strong> Bardzo dobra - regularne zmniejszanie odsetek</li>
                    <li><strong>Zalety:</strong> Łatwiejsze planowanie niż miesięczne</li>
                    <li><strong>Rekomendacja:</strong> Dla osób z sezonowymi dochodami</li>
                </ul>

                <h4>Jednorazowe nadpłacanie:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Duża jednorazowa wpłata</li>
                    <li><strong>Źródła:</strong> Sprzedaż nieruchomości, spadek, inwestycje</li>
                    <li><strong>Elastyczność:</strong> Dowolny moment i kwota</li>
                    <li><strong>Efektywność:</strong> Bardzo wysoka przy dużych kwotach</li>
                    <li><strong>Zalety:</strong> Znaczące skrócenie okresu spłaty</li>
                    <li><strong>Rekomendacja:</strong> Przy otrzymaniu większej sumy pieniędzy</li>
                </ul>

                <h4>Nadpłacanie mieszane:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Kombinacja miesięcznych i rocznych nadpłat</li>
                    <li><strong>Strategia:</strong> Mniejsze kwoty miesięcznie + większe rocznie</li>
                    <li><strong>Elastyczność:</strong> Maksymalna adaptacja do możliwości</li>
                    <li><strong>Efektywność:</strong> Najwyższa - optymalne wykorzystanie środków</li>
                    <li><strong>Zalety:</strong> Najlepszy stosunek oszczędności do wysiłku</li>
                    <li><strong>Rekomendacja:</strong> Dla osób chcących maksymalizować oszczędności</li>
                </ul>
            </div>

            <h3>Efekty nadpłacania kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Skrócenie okresu spłaty:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Rata pozostaje bez zmian, skraca się czas spłaty</li>
                    <li><strong>Korzyści:</strong> Maksymalne oszczędności na odsetkach</li>
                    <li><strong>Przykład:</strong> 500 zł miesięcznie skraca okres o 4 lata 10 miesięcy</li>
                    <li><strong>Oszczędność:</strong> 142 850 zł przy 20-letnim kredycie</li>
                    <li><strong>Rekomendacja:</strong> Dla osób chcących szybko spłacić kredyt</li>
                </ul>

                <h4>Zmniejszenie miesięcznej raty:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Okres pozostaje bez zmian, maleje rata</li>
                    <li><strong>Korzyści:</strong> Poprawa płynności finansowej</li>
                    <li><strong>Przykład:</strong> 500 zł nadpłaty zmniejsza ratę o ~150 zł</li>
                    <li><strong>Oszczędność:</strong> Mniejsza niż przy skróceniu okresu</li>
                    <li><strong>Rekomendacja:</strong> Dla osób potrzebujących niższych rat</li>
                </ul>

                <h4>Efekt mieszany:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Częściowe skrócenie + częściowe zmniejszenie raty</li>
                    <li><strong>Korzyści:</strong> Kompromis między oszczędnością a płynnością</li>
                    <li><strong>Elastyczność:</strong> Możliwość dostosowania proporcji</li>
                    <li><strong>Rekomendacja:</strong> Dla osób chcących zbalansować korzyści</li>
                </ul>
            </div>

            <h3>Opłaty za nadpłacanie kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Bank</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Prowizja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Maksymalna opłata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Darmowe nadpłaty</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">PKO BP</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 rocznie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Santander</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-1.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 rocznie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">mBank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 rocznie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">ING</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4 rocznie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Pekao SA</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-1.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 rocznie</td>
                    </tr>
                </table>
            </div>

            <h3>Strategia nadpłacania kredytu hipotecznego:</h3>
            <ul>
                <li><strong>Analiza budżetu:</strong> Określ stałą kwotę na nadpłacanie</li>
                <li><strong>Wybór typu:</strong> Miesięczne dla maksymalnych oszczędności</li>
                <li><strong>Automatyzacja:</strong> Ustaw stałe zlecenie w banku</li>
                <li><strong>Monitorowanie:</strong> Sprawdzaj postępy w bankowości internetowej</li>
                <li><strong>Optymalizacja:</strong> Zwiększaj nadpłaty wraz ze wzrostem dochodów</li>
                <li><strong>Elastyczność:</strong> Wykorzystuj premie na dodatkowe nadpłaty</li>
                <li><strong>Planowanie:</strong> Uwzględnij nadpłaty w budżecie domowym</li>
            </ul>

            <h3>Porównanie z innymi inwestycjami:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Inwestycja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Zwrot</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Ryzyko</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Płynność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Nadpłacanie kredytu</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>7.25% (gwarantowane)</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Brak</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Niska</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Lokaty bankowe</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Brak</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średnia</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Obligacje skarbowe</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo niskie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średnia</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Fundusze inwestycyjne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8-12%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysokie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysoka</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Akcje</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">10-15%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo wysokie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysoka</td>
                    </tr>
                </table>
            </div>

            <h3>Porady ekspertów dotyczące nadpłacania:</h3>
            <ul>
                <li>Rozpocznij od małych kwot i stopniowo zwiększaj nadpłaty</li>
                <li>Wykorzystaj automatyzację - ustaw stałe zlecenie</li>
                <li>Monitoruj regularnie postępy w bankowości internetowej</li>
                <li>Wykorzystuj premie i zwroty na dodatkowe nadpłaty</li>
                <li>Sprawdzaj limity darmowych nadpłat w swoim banku</li>
                <li>Rozważ efekt mieszany przy zmieniającej się sytuacji finansowej</li>
                <li>Konsultuj strategię z doradcą kredytowym</li>
                <li>Pamiętaj o zachowaniu rezerwy finansowej na nieprzewidziane wydatki</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
                <a href="/pko-bp-kalkulator-kredytu-hipotecznego.html">PKO BP kalkulator kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Szybkie ustawienia</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Ustaw nadpłacanie:</strong><br>
                        <a href="#" onclick="return setOverpayment(250);">250 zł miesięcznie</a><br>
                        <a href="#" onclick="return setOverpayment(500);">500 zł miesięcznie</a><br>
                        <a href="#" onclick="return setOverpayment(1000);">1000 zł miesięcznie</a><br>
                        <a href="#" onclick="return setOverpayment(1500);">1500 zł miesięcznie</a><br>
                        <a href="#" onclick="return setYearlyOverpayment(10000);">10 000 zł rocznie</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator nadpłacania kredytu hipotecznego - oblicz oszczędności z nadpłat kredytu mieszkaniowego, sprawdź opłacalność nadpłacania.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateOverpaymentType() {
    var type = document.getElementById('coverpaymenttype').value;
    var monthly = document.getElementById('monthlyoverpayment');
    var yearly = document.getElementById('yearlyoverpayment');
    var quarterly = document.getElementById('quarterlyoverpayment');
    var onetime = document.getElementById('onetimeoverpayment');
    var mixed = document.getElementById('mixedoverpayment');
    
    monthly.style.display = (type === 'monthly') ? 'table-row' : 'none';
    yearly.style.display = (type === 'yearly') ? 'table-row' : 'none';
    quarterly.style.display = (type === 'quarterly') ? 'table-row' : 'none';
    onetime.style.display = (type === 'onetime') ? 'table-row' : 'none';
    mixed.style.display = (type === 'mixed') ? 'table-row' : 'none';
}

function cshfees() {
    var checkbox = document.getElementById('caddfees');
    var feesDiv = document.getElementById('cfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshscenarios() {
    var checkbox = document.getElementById('caddscenarios');
    var scenariosDiv = document.getElementById('cscenarios');
    if (checkbox.checked) {
        scenariosDiv.style.display = 'block';
    } else {
        scenariosDiv.style.display = 'none';
    }
}

function setOverpayment(amount) {
    document.getElementById('coverpaymenttype').value = 'monthly';
    document.getElementById('cmonthlyoverpayment').value = amount;
    updateOverpaymentType();
    return false;
}

function setYearlyOverpayment(amount) {
    document.getElementById('coverpaymenttype').value = 'yearly';
    document.getElementById('cyearlyoverpayment').value = amount;
    updateOverpaymentType();
    return false;
}

function saveCalculation() {
    alert('Wyniki kalkulatora nadpłacania zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram spłat &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Scenariusze nadpłacania</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Scenariusze nadpłacania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a>';
    }
    return false;
}
</script>

<script>
function calculateOverpaymentSavings() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 获取nadpłata信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;
    var overpaymentType = document.getElementById('coverpaymenttype').value;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算没有nadpłata的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;

    // 计算有nadpłata的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;

    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;

        // 添加nadpłata
        var currentOverpayment = 0;
        if (overpaymentFreq === 'monthly' ||
           (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
           (overpaymentFreq === 'once' && monthsPaid === 0)) {
            currentOverpayment = overpaymentAmount;
        }

        // 更新余额
        remainingBalance -= (monthlyPrincipal + currentOverpayment);
        totalPaid += monthlyPayment + currentOverpayment;
        totalOverpayments += currentOverpayment;
        monthsPaid++;

        if (remainingBalance <= 0) break;
    }

    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments);

    // 更新显示
    updateOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                           timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments);
}

function updateOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Oszczędności nadpłacania: &nbsp; ' + formatNumber(moneySaved) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('coverpayment')) document.getElementById('coverpayment').value = '500';
    calculateOverpaymentSavings();
}

function saveCalResult() {
    alert('Wyniki kalkulatora nadpłacania zostały zapisane!');
    return false;
}

function saveCalculation() {
    saveCalResult();
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateOverpaymentSavings();
};
</script>

</body>
</html>
