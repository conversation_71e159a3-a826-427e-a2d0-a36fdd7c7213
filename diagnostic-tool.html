<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Calculator Diagnostic Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .result { padding: 10px; margin: 10px 0; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .file-list { max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; }
        .file-item { padding: 5px; margin: 2px 0; background: #f0f0f0; cursor: pointer; }
        .file-item:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>Calculator Diagnostic Tool</h1>
    
    <div class="test-section">
        <h2>Step 1: Basic JavaScript Test</h2>
        <button onclick="testBasicJS()">Test Basic JavaScript</button>
        <div id="jsTest" class="result">Click to test basic JavaScript functionality</div>
    </div>
    
    <div class="test-section">
        <h2>Step 2: DOM Element Test</h2>
        <button onclick="testDOMElements()">Test DOM Elements</button>
        <div id="domTest" class="result">Click to test DOM element access</div>
    </div>
    
    <div class="test-section">
        <h2>Step 3: Calculator Function Test</h2>
        <button onclick="testCalculatorFunction()">Test Calculator Function</button>
        <div id="calcTest" class="result">Click to test calculator function</div>
    </div>
    
    <div class="test-section">
        <h2>Step 4: Result Display Test</h2>
        <button onclick="testResultDisplay()">Test Result Display</button>
        <div id="displayTest" class="result">Click to test result display</div>
    </div>
    
    <div class="test-section">
        <h2>Step 5: Common Issues Check</h2>
        <button onclick="checkCommonIssues()">Check Common Issues</button>
        <div id="issuesTest" class="result">Click to check for common issues</div>
    </div>

    <script>
        function testBasicJS() {
            try {
                // Test basic JavaScript operations
                var testNumber = 500000;
                var testPercent = 20;
                var result = testNumber * (testPercent / 100);
                
                document.getElementById('jsTest').innerHTML = 
                    '<div class="success">✅ Basic JavaScript Working</div>' +
                    '<p>Test calculation: ' + testNumber + ' * ' + testPercent + '% = ' + result + '</p>' +
                    '<p>Math.pow test: ' + Math.pow(1.07, 12).toFixed(4) + '</p>' +
                    '<p>Number formatting: ' + result.toLocaleString('pl-PL') + '</p>';
            } catch (error) {
                document.getElementById('jsTest').innerHTML = 
                    '<div class="error">❌ JavaScript Error: ' + error.message + '</div>';
            }
        }
        
        function testDOMElements() {
            var results = [];
            var testElements = [
                'chouseprice', 'cdownpayment', 'cdownpaymentunit', 
                'cloanterm', 'cinterestrate'
            ];
            
            // Create test elements if they don't exist
            testElements.forEach(function(id) {
                if (!document.getElementById(id)) {
                    var input = document.createElement('input');
                    input.id = id;
                    input.type = 'text';
                    input.value = getDefaultValue(id);
                    input.style.display = 'none';
                    document.body.appendChild(input);
                    results.push('🔧 Created missing element: ' + id);
                } else {
                    results.push('✅ Found element: ' + id);
                }
            });
            
            // Test h2result element
            var h2result = document.querySelector('.h2result');
            if (!h2result) {
                var h2 = document.createElement('h2');
                h2.className = 'h2result';
                h2.innerHTML = 'Test Result: 0 zł';
                h2.style.display = 'none';
                document.body.appendChild(h2);
                results.push('🔧 Created missing h2result element');
            } else {
                results.push('✅ Found h2result element');
            }
            
            document.getElementById('domTest').innerHTML = 
                '<div class="success">DOM Elements Test Complete</div>' +
                '<ul><li>' + results.join('</li><li>') + '</li></ul>';
        }
        
        function getDefaultValue(id) {
            switch(id) {
                case 'chouseprice': return '500000';
                case 'cdownpayment': return '20';
                case 'cdownpaymentunit': return 'p';
                case 'cloanterm': return '25';
                case 'cinterestrate': return '7.25';
                default: return '0';
            }
        }
        
        function testCalculatorFunction() {
            try {
                // Simulate calculator function
                var housePrice = 500000;
                var downPayment = 20;
                var downPaymentUnit = 'p';
                var loanTerm = 25;
                var interestRate = 7.25;
                
                // Calculate down payment amount
                var downPaymentAmount;
                if (downPaymentUnit === 'p') {
                    downPaymentAmount = housePrice * (downPayment / 100);
                } else {
                    downPaymentAmount = downPayment;
                }
                
                var loanAmount = housePrice - downPaymentAmount;
                
                // Calculate monthly payment
                var monthlyRate = interestRate / 100 / 12;
                var numPayments = loanTerm * 12;
                var monthlyPayment = 0;
                
                if (monthlyRate > 0) {
                    monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                    (Math.pow(1 + monthlyRate, numPayments) - 1);
                } else {
                    monthlyPayment = loanAmount / numPayments;
                }
                
                var totalPayment = monthlyPayment * numPayments;
                var totalInterest = totalPayment - loanAmount;
                
                document.getElementById('calcTest').innerHTML = 
                    '<div class="success">✅ Calculator Function Working</div>' +
                    '<p><strong>Input Values:</strong></p>' +
                    '<ul>' +
                    '<li>House Price: ' + housePrice.toLocaleString('pl-PL') + ' zł</li>' +
                    '<li>Down Payment: ' + downPayment + '% (' + downPaymentAmount.toLocaleString('pl-PL') + ' zł)</li>' +
                    '<li>Loan Amount: ' + loanAmount.toLocaleString('pl-PL') + ' zł</li>' +
                    '<li>Interest Rate: ' + interestRate + '%</li>' +
                    '<li>Loan Term: ' + loanTerm + ' years</li>' +
                    '</ul>' +
                    '<p><strong>Calculated Results:</strong></p>' +
                    '<ul>' +
                    '<li>Monthly Payment: ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł</li>' +
                    '<li>Total Payment: ' + Math.round(totalPayment).toLocaleString('pl-PL') + ' zł</li>' +
                    '<li>Total Interest: ' + Math.round(totalInterest).toLocaleString('pl-PL') + ' zł</li>' +
                    '</ul>';
                    
            } catch (error) {
                document.getElementById('calcTest').innerHTML = 
                    '<div class="error">❌ Calculator Function Error: ' + error.message + '</div>';
            }
        }
        
        function testResultDisplay() {
            try {
                var testResult = 2847;
                
                // Test h2result update
                var h2result = document.querySelector('.h2result');
                if (h2result) {
                    var originalContent = h2result.innerHTML;
                    h2result.innerHTML = 'Test Result: ' + testResult.toLocaleString('pl-PL') + ' zł';
                    
                    setTimeout(function() {
                        h2result.innerHTML = originalContent;
                    }, 3000);
                    
                    document.getElementById('displayTest').innerHTML = 
                        '<div class="success">✅ Result Display Working</div>' +
                        '<p>Successfully updated h2result element</p>' +
                        '<p>Check the page - the result should show "Test Result: ' + testResult.toLocaleString('pl-PL') + ' zł" for 3 seconds</p>';
                } else {
                    document.getElementById('displayTest').innerHTML = 
                        '<div class="warning">⚠️ No h2result element found</div>' +
                        '<p>The page may not have the correct result display structure</p>';
                }
                
            } catch (error) {
                document.getElementById('displayTest').innerHTML = 
                    '<div class="error">❌ Result Display Error: ' + error.message + '</div>';
            }
        }
        
        function checkCommonIssues() {
            var issues = [];
            var warnings = [];
            var fixes = [];
            
            // Check for common function names
            var commonFunctions = [
                'calculateMortgage', 'calculateINGMortgage', 'calculateMBankMortgage',
                'calculatePKOBPMortgage', 'calculateSantanderMortgage'
            ];
            
            commonFunctions.forEach(function(funcName) {
                if (typeof window[funcName] === 'function') {
                    fixes.push('✅ Found function: ' + funcName);
                } else {
                    issues.push('❌ Missing function: ' + funcName);
                }
            });
            
            // Check for common elements
            var commonElements = [
                'chouseprice', 'cdownpayment', 'cloanterm', 'cinterestrate'
            ];
            
            commonElements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    fixes.push('✅ Found element: ' + id + ' (value: ' + element.value + ')');
                } else {
                    issues.push('❌ Missing element: ' + id);
                }
            });
            
            // Check for result display elements
            if (document.querySelector('.h2result')) {
                fixes.push('✅ Found result display element (.h2result)');
            } else {
                issues.push('❌ Missing result display element (.h2result)');
            }
            
            // Check for form
            if (document.querySelector('form[name="calform"]')) {
                fixes.push('✅ Found calculator form');
            } else {
                warnings.push('⚠️ No calculator form found');
            }
            
            var resultHTML = '';
            
            if (issues.length > 0) {
                resultHTML += '<div class="error"><h4>❌ Issues Found:</h4><ul><li>' + issues.join('</li><li>') + '</li></ul></div>';
            }
            
            if (warnings.length > 0) {
                resultHTML += '<div class="warning"><h4>⚠️ Warnings:</h4><ul><li>' + warnings.join('</li><li>') + '</li></ul></div>';
            }
            
            if (fixes.length > 0) {
                resultHTML += '<div class="success"><h4>✅ Working Elements:</h4><ul><li>' + fixes.join('</li><li>') + '</li></ul></div>';
            }
            
            if (issues.length === 0 && warnings.length === 0) {
                resultHTML = '<div class="success">✅ No major issues found! The calculator should be working.</div>';
            }
            
            document.getElementById('issuesTest').innerHTML = resultHTML;
        }
        
        // Auto-run basic tests on page load
        window.onload = function() {
            console.log('Diagnostic tool loaded');
            testBasicJS();
        };
    </script>
</body>
</html>
