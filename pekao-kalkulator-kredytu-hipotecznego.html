<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Pekao kalkulator kredytu hipotecznego</title>
	<meta name="description" content="Pekao kalkulator kredytu hipotecznego - oblicz ratę kredytu mieszkaniowego w Banku Pekao SA, sprawdź oprocentowanie i koszty kredytu hipotecznego Pekao 2025.">
	<meta name="keywords" content="pekao kalkulator kredytu hipotecznego, bank pekao kredyt hipoteczny, pekao sa kredyt mieszkaniowy, oprocentowanie pekao">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/pekao-kalkulator-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">pekao kalkulator kredytu hipotecznego</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Pekao kalkulator kredytu hipotecznego</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w Banku Pekao SA"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/pekao-kalkulator-kredytu-hipotecznego.html">
<table align="center">
<tbody><tr><td align="right">Wartość nieruchomości</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Minimalne 10% w Banku Pekao SA. Wyższy wkład = niższe oprocentowanie.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Maksymalnie 35 lat w Banku Pekao SA. Dłuższy okres = niższa rata, ale wyższe odsetki.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie w Banku Pekao SA. WIBOR 3M + marża banku.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.15" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Profil klienta Pekao</td><td align="left" colspan="2"><select name="cpekaoprofile" id="cpekaoprofile"><option value="new" selected="">Nowy klient</option><option value="existing">Obecny klient Pekao</option><option value="premium">Pekao Premium</option><option value="private">Pekao Private Banking</option><option value="young">Pekao dla młodych</option></select></td></tr>
<tr><td align="right">Program kredytowy Pekao</td><td align="left" colspan="2"><select name="cpekaoprogram" id="cpekaoprogram"><option value="standard" selected="">Kredyt hipoteczny standardowy</option><option value="first">Pierwszy kredyt (dla młodych)</option><option value="family">Kredyt rodzinny na start</option><option value="green">Kredyt ekologiczny</option><option value="investment">Kredyt inwestycyjny</option></select></td></tr>
<tr><td align="right">Waluta kredytu</td><td align="left" colspan="2"><select name="ccurrency" id="ccurrency"><option value="pln" selected="">PLN (złoty)</option><option value="eur">EUR (euro)</option><option value="chf">CHF (frank szwajcarski)</option></select></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Koszty i opłaty Banku Pekao SA</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Opłaty w Banku Pekao SA</td></tr>
	<tr><td align="right">Prowizja Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja za udzielenie kredytu w Banku Pekao SA. Zazwyczaj 1-3% kwoty kredytu.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" id="cprovisionunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obowiązkowe ubezpieczenie nieruchomości w Banku Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1200" class="innormal indollar"></td><td><select name="chomeinsunit" id="chomeinsunit"><option value="z" selected="">zł/rok</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Opcjonalne ubezpieczenie spłaty kredytu w Banku Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.40" class="innormal inpct"></td><td><select name="cpmiunit" id="cpmiunit"><option value="p" selected="">%</option><option value="z">zł/rok</option></select></td></tr>
	<tr><td align="right">Prowadzenie rachunku Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w Banku Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="25" class="innormal indollar"></td><td><select name="choaunit" id="choaunit"><option value="z" selected="">zł/miesiąc</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę współpracującego z Bankiem Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cvaluation" id="cvaluation" value="2500" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td align="right">Inne koszty Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: notariusz, wpis do księgi wieczystej, itp.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="5000" class="innormal indollar"></td><td>zł</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Programy Pekao</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Dostępne programy w Banku Pekao SA</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Kredyt standardowy</td><td><a href="#" onclick="setPekaoProgram('standard');return false;">Marża od 2.5%</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Pierwszy kredyt</td><td><a href="#" onclick="setPekaoProgram('first');return false;">Marża od 2.2%</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Kredyt rodzinny</td><td><a href="#" onclick="setPekaoProgram('family');return false;">Marża od 2.3%</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Kredyt ekologiczny</td><td><a href="#" onclick="setPekaoProgram('green');return false;">Marża od 2.1%</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Pekao Premium</td><td><a href="#" onclick="setPekaoProgram('premium');return false;">Marża od 2.0%</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz kredyt Pekao" onclick="calculatePekaoMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Kredyt Pekao: &nbsp; 3 520 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('UGVrYW8ga2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 0, 'UGVrYW8ga2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 'S3JlZHl0IFBlbGFv', 'MyA1MjAgenXFgg==');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Łącznie</b></td><td align="right"><b>Szczegóły</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu Pekao</b></td><td align="right"><b>3 520 zł</b></td><td align="right"><b>1 056 000 zł</b></td><td align="right"><b>25 lat</b></td></tr>
<tr><td>Kwota kredytu</td><td align="right">-</td><td align="right">400 000 zł</td><td align="right">80% LTV</td></tr>
<tr><td>Odsetki łącznie</td><td align="right">-</td><td align="right">656 000 zł</td><td align="right">164% kwoty</td></tr>
<tr><td>Prowizja Pekao</td><td align="right">-</td><td align="right">8 000 zł</td><td align="right">2.0%</td></tr>
<tr><td>Ubezpieczenie nieruchomości</td><td align="right">100 zł</td><td align="right">30 000 zł</td><td align="right">Obowiązkowe</td></tr>
<tr><td>Ubezpieczenie spłaty</td><td align="right">133 zł</td><td align="right">40 000 zł</td><td align="right">Opcjonalne</td></tr>
<tr><td>Prowadzenie rachunku</td><td align="right">25 zł</td><td align="right">7 500 zł</td><td align="right">Pekao</td></tr>
<tr><td>Wycena + inne koszty</td><td align="right">-</td><td align="right">7 500 zł</td><td align="right">Jednorazowo</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Szczegóły kredytu w Banku Pekao SA:</h3>
<p><strong>RRSO Pekao:</strong> 8.25% (z wszystkimi opłatami Banku Pekao SA)</p>
<p><strong>Status klienta:</strong> Nowy klient Banku Pekao SA</p>
<p><strong>Program kredytowy:</strong> Kredyt hipoteczny standardowy Pekao</p>
<p><strong>Całkowity koszt Pekao:</strong> 1 149 000 zł (kredyt + wszystkie opłaty)</p>
<p><strong>Przepłacone odsetki:</strong> 656 000 zł (164% kwoty kredytu)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Oferta kredytowa Banku Pekao SA</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Programy kredytowe Pekao</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">2.0%</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">2.5%</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">3.0%</text>

<!-- Standardowy -->
<rect x="50" y="105" width="35" height="40" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Standardowy</text>
<text x="67" y="100" class="mcllabelx" style="fill:#000;">2.5%</text>

<!-- Pierwszy kredyt -->
<rect x="95" y="115" width="35" height="30" fill="#2ecc71" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Pierwszy</text>
<text x="112" y="110" class="mcllabelx" style="fill:#000;">2.2%</text>

<!-- Rodzinny -->
<rect x="140" y="110" width="35" height="35" fill="#e74c3c" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Rodzinny</text>
<text x="157" y="105" class="mcllabelx" style="fill:#000;">2.3%</text>

<!-- Ekologiczny -->
<rect x="185" y="125" width="35" height="20" fill="#27ae60" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Ekologiczny</text>
<text x="202" y="120" class="mcllabelx" style="fill:#000;">2.1%</text>

<!-- Premium -->
<rect x="230" y="135" width="35" height="10" fill="#9b59b6" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Premium</text>
<text x="247" y="130" class="mcllabelx" style="fill:#000;">2.0%</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Programy Pekao &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie marż</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Program Pekao</th>
<th>Marża</th>
<th>Warunki</th>
<th>Dla kogo</th>
<th>Rata</th>
</tr>
<tr>
<td>Standardowy</td>
<td>2.5%</td>
<td>Standardowe</td>
<td>Wszyscy</td>
<td>3 520 zł</td>
</tr>
<tr>
<td>Pierwszy kredyt</td>
<td>2.2%</td>
<td>Pierwszy kredyt</td>
<td>Młodzi</td>
<td>3 450 zł</td>
</tr>
<tr>
<td>Rodzinny</td>
<td>2.3%</td>
<td>Z dopłatami</td>
<td>Rodziny</td>
<td>3 485 zł</td>
</tr>
<tr>
<td>Ekologiczny</td>
<td>2.1%</td>
<td>Dom energooszczędny</td>
<td>Eko-świadomi</td>
<td>3 420 zł</td>
</tr>
<tr>
<td>Premium</td>
<td>2.0%</td>
<td>Klient Premium</td>
<td>VIP</td>
<td>3 385 zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Pekao kalkulator kredytu hipotecznego - Bank Pekao SA kredyt mieszkaniowy 2025</h2>
            <p>Pekao kalkulator kredytu hipotecznego to specjalistyczne narzędzie do obliczania rat kredytu mieszkaniowego w Banku Pekao SA. Nasz kalkulator uwzględnia wszystkie programy kredytowe Pekao, marże preferencyjne dla różnych grup klientów oraz pełne koszty kredytu hipotecznego w Banku Pekao SA.</p>

            <h3>Programy kredytowe w Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Kredyt hipoteczny standardowy Pekao:</h4>
                <ul>
                    <li><strong>Marża:</strong> Od 2.5% w skali roku</li>
                    <li><strong>Kwota:</strong> Do 2 000 000 zł</li>
                    <li><strong>LTV:</strong> Do 90% wartości nieruchomości</li>
                    <li><strong>Okres:</strong> Do 35 lat</li>
                    <li><strong>Wkład własny:</strong> Minimum 10%</li>
                    <li><strong>Dla kogo:</strong> Wszyscy klienci Banku Pekao SA</li>
                    <li><strong>Warunki:</strong> Standardowe wymagania dochodowe</li>
                </ul>

                <h4>Pierwszy kredyt Pekao (dla młodych):</h4>
                <ul>
                    <li><strong>Marża:</strong> Od 2.2% w skali roku (preferencyjna)</li>
                    <li><strong>Kwota:</strong> Do 1 500 000 zł</li>
                    <li><strong>LTV:</strong> Do 90% wartości nieruchomości</li>
                    <li><strong>Wiek:</strong> Do 35 lat</li>
                    <li><strong>Warunek:</strong> Pierwszy kredyt hipoteczny</li>
                    <li><strong>Dla kogo:</strong> Młodzi klienci Banku Pekao SA</li>
                    <li><strong>Dodatkowe korzyści:</strong> Niższa prowizja, preferencyjne ubezpieczenia</li>
                </ul>

                <h4>Kredyt rodzinny na start Pekao:</h4>
                <ul>
                    <li><strong>Marża:</strong> Od 2.3% w skali roku</li>
                    <li><strong>Kwota:</strong> Do 2 000 000 zł</li>
                    <li><strong>LTV:</strong> Do 90% wartości nieruchomości</li>
                    <li><strong>Warunek:</strong> Rodzina z dziećmi</li>
                    <li><strong>Dopłaty:</strong> Możliwość skorzystania z programów rządowych</li>
                    <li><strong>Dla kogo:</strong> Rodziny z dziećmi</li>
                    <li><strong>Dodatkowe korzyści:</strong> Wsparcie w programach rodzinnych</li>
                </ul>

                <h4>Kredyt ekologiczny Pekao:</h4>
                <ul>
                    <li><strong>Marża:</strong> Od 2.1% w skali roku (najniższa)</li>
                    <li><strong>Kwota:</strong> Do 2 000 000 zł</li>
                    <li><strong>LTV:</strong> Do 90% wartości nieruchomości</li>
                    <li><strong>Warunek:</strong> Dom energooszczędny (klasa A lub B)</li>
                    <li><strong>Certyfikaty:</strong> Wymagane certyfikaty energetyczne</li>
                    <li><strong>Dla kogo:</strong> Nabywcy domów ekologicznych</li>
                    <li><strong>Dodatkowe korzyści:</strong> Najniższa marża w ofercie Pekao</li>
                </ul>

                <h4>Kredyt inwestycyjny Pekao:</h4>
                <ul>
                    <li><strong>Marża:</strong> Od 2.7% w skali roku</li>
                    <li><strong>Kwota:</strong> Do 5 000 000 zł</li>
                    <li><strong>LTV:</strong> Do 80% wartości nieruchomości</li>
                    <li><strong>Cel:</strong> Zakup nieruchomości na wynajem</li>
                    <li><strong>Warunki:</strong> Wyższe wymagania dochodowe</li>
                    <li><strong>Dla kogo:</strong> Inwestorzy nieruchomościowi</li>
                    <li><strong>Dodatkowe korzyści:</strong> Wyższe kwoty, elastyczne warunki</li>
                </ul>
            </div>

            <h3>Profile klientów Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Nowy klient Banku Pekao SA:</h4>
                <ul>
                    <li><strong>Marża:</strong> Standardowa według cennika</li>
                    <li><strong>Warunki:</strong> Podstawowe wymagania</li>
                    <li><strong>Dokumenty:</strong> Standardowy pakiet dokumentów</li>
                    <li><strong>Proces:</strong> Standardowa ścieżka weryfikacji</li>
                </ul>

                <h4>Obecny klient Banku Pekao SA:</h4>
                <ul>
                    <li><strong>Marża:</strong> Preferencyjna (-0.1%)</li>
                    <li><strong>Warunki:</strong> Uproszczona weryfikacja</li>
                    <li><strong>Historia:</strong> Pozytywna historia współpracy</li>
                    <li><strong>Proces:</strong> Przyspieszona ścieżka</li>
                </ul>

                <h4>Klient Pekao Premium:</h4>
                <ul>
                    <li><strong>Marża:</strong> Bardzo preferencyjna (-0.3%)</li>
                    <li><strong>Warunki:</strong> Pakiet Premium</li>
                    <li><strong>Obsługa:</strong> Dedykowany doradca</li>
                    <li><strong>Korzyści:</strong> Dodatkowe usługi i produkty</li>
                </ul>

                <h4>Pekao Private Banking:</h4>
                <ul>
                    <li><strong>Marża:</strong> Najlepsza (-0.5%)</li>
                    <li><strong>Warunki:</strong> Indywidualne negocjacje</li>
                    <li><strong>Obsługa:</strong> Private Banker</li>
                    <li><strong>Minimalne aktywa:</strong> 1 000 000 zł</li>
                </ul>

                <h4>Pekao dla młodych:</h4>
                <ul>
                    <li><strong>Marża:</strong> Promocyjna (-0.2%)</li>
                    <li><strong>Wiek:</strong> Do 26 lat</li>
                    <li><strong>Warunki:</strong> Specjalne programy dla młodych</li>
                    <li><strong>Korzyści:</strong> Bezpłatne produkty bankowe</li>
                </ul>
            </div>

            <h3>Koszty i opłaty w Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Prowizja za udzielenie kredytu:</h4>
                <ul>
                    <li><strong>Standardowa:</strong> 2.0% kwoty kredytu</li>
                    <li><strong>Pierwszy kredyt:</strong> 1.5% kwoty kredytu</li>
                    <li><strong>Pekao Premium:</strong> 1.0% kwoty kredytu</li>
                    <li><strong>Private Banking:</strong> 0.5% kwoty kredytu</li>
                    <li><strong>Minimum:</strong> 3 000 zł</li>
                    <li><strong>Maksimum:</strong> 50 000 zł</li>
                </ul>

                <h4>Ubezpieczenia obowiązkowe:</h4>
                <ul>
                    <li><strong>Ubezpieczenie nieruchomości:</strong> 1 200 zł/rok (średnio)</li>
                    <li><strong>Zakres:</strong> Od ognia i innych zdarzeń losowych</li>
                    <li><strong>Suma ubezpieczenia:</strong> Wartość odtworzeniowa</li>
                    <li><strong>Ubezpieczyciel:</strong> Pekao TU SA lub inny akceptowany</li>
                </ul>

                <h4>Ubezpieczenia opcjonalne:</h4>
                <ul>
                    <li><strong>Ubezpieczenie spłaty kredytu:</strong> 0.40% kwoty kredytu rocznie</li>
                    <li><strong>Zakres:</strong> Śmierć, niezdolność do pracy</li>
                    <li><strong>Wiek:</strong> Do 65 lat</li>
                    <li><strong>Korzyści:</strong> Spokój i bezpieczeństwo rodziny</li>
                </ul>

                <h4>Opłaty eksploatacyjne:</h4>
                <ul>
                    <li><strong>Prowadzenie rachunku kredytowego:</strong> 25 zł/miesiąc</li>
                    <li><strong>Pekao Premium:</strong> 0 zł/miesiąc</li>
                    <li><strong>Private Banking:</strong> 0 zł/miesiąc</li>
                    <li><strong>Wyciągi:</strong> Elektroniczne bezpłatne</li>
                </ul>

                <h4>Koszty dodatkowe:</h4>
                <ul>
                    <li><strong>Wycena nieruchomości:</strong> 2 500 zł (średnio)</li>
                    <li><strong>Notariusz:</strong> 3 000-5 000 zł</li>
                    <li><strong>Wpis do księgi wieczystej:</strong> 200 zł</li>
                    <li><strong>Ubezpieczenie tytułu prawnego:</strong> 0.5% kwoty kredytu</li>
                </ul>
            </div>

            <h3>Warunki kredytu hipotecznego w Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Wymagania podstawowe:</h4>
                <ul>
                    <li><strong>Wiek:</strong> 18-75 lat (na koniec okresu kredytowania)</li>
                    <li><strong>Dochody:</strong> Stałe i udokumentowane</li>
                    <li><strong>Zatrudnienie:</strong> Minimum 6 miesięcy w obecnym miejscu</li>
                    <li><strong>Historia kredytowa:</strong> Pozytywna w BIK</li>
                    <li><strong>Wkład własny:</strong> Minimum 10% wartości nieruchomości</li>
                </ul>

                <h4>Dokumenty wymagane:</h4>
                <ul>
                    <li><strong>Wniosek kredytowy:</strong> Wypełniony i podpisany</li>
                    <li><strong>Dokumenty tożsamości:</strong> Dowód osobisty, PESEL</li>
                    <li><strong>Zaświadczenia o dochodach:</strong> PIT, zaświadczenia z pracy</li>
                    <li><strong>Dokumenty nieruchomości:</strong> Akt notarialny, księga wieczysta</li>
                    <li><strong>Wycena nieruchomości:</strong> Przez rzeczoznawcę</li>
                </ul>

                <h4>Proces uzyskania kredytu:</h4>
                <ul>
                    <li><strong>Krok 1:</strong> Wstępna analiza zdolności kredytowej</li>
                    <li><strong>Krok 2:</strong> Złożenie wniosku i dokumentów</li>
                    <li><strong>Krok 3:</strong> Weryfikacja i ocena wniosku</li>
                    <li><strong>Krok 4:</strong> Wycena nieruchomości</li>
                    <li><strong>Krok 5:</strong> Decyzja kredytowa</li>
                    <li><strong>Krok 6:</strong> Podpisanie umowy u notariusza</li>
                    <li><strong>Krok 7:</strong> Uruchomienie kredytu</li>
                </ul>
            </div>

            <h3>Porównanie z konkurencją:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Bank</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Marża od</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Prowizja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">LTV max</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Okres max</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Bank Pekao SA</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.5-2.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">90%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35 lat</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">PKO BP</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.0-2.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">90%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35 lat</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">mBank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-2.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">90%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35 lat</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Santander</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.3%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.0-3.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">90%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30 lat</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">ING</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.4%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-2.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">90%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35 lat</td>
                    </tr>
                </table>
            </div>

            <h3>Zalety kredytu hipotecznego w Banku Pekao SA:</h3>
            <ul>
                <li><strong>Konkurencyjne oprocentowanie:</strong> Marża od 2.0% dla klientów Premium</li>
                <li><strong>Szeroka oferta programów:</strong> 5 różnych programów kredytowych</li>
                <li><strong>Wysokie LTV:</strong> Do 90% wartości nieruchomości</li>
                <li><strong>Długi okres:</strong> Do 35 lat spłaty</li>
                <li><strong>Elastyczne warunki:</strong> Możliwość nadpłat bez prowizji</li>
                <li><strong>Doświadczenie:</strong> Ponad 90 lat na rynku polskim</li>
                <li><strong>Sieć placówek:</strong> Ponad 800 oddziałów w całej Polsce</li>
                <li><strong>Obsługa online:</strong> Pełna obsługa przez internet i aplikację</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Programy Pekao</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Wybierz program Pekao:</strong><br>
<a href="#" onclick="return setPekaoProgram('standard');">Kredyt standardowy</a><br>
<a href="#" onclick="return setPekaoProgram('first');">Pierwszy kredyt</a><br>
<a href="#" onclick="return setPekaoProgram('family');">Kredyt rodzinny</a><br>
<a href="#" onclick="return setPekaoProgram('green');">Kredyt ekologiczny</a><br>
<a href="#" onclick="return setPekaoProgram('premium');">Pekao Premium</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Pekao kalkulator kredytu hipotecznego - oblicz ratę kredytu mieszkaniowego w Banku Pekao SA.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculatePekaoMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 获取Pekao特殊信息
    var pekaoProfile = document.getElementById('cpekaoprofile').value;
    var pekaoProgram = document.getElementById('cpekaoprogram').value;
    var currency = document.getElementById('ccurrency').value;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 调整利率基于Pekao程序
    var adjustedRate = adjustPekaoRate(interestRate, pekaoProfile, pekaoProgram);
    
    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取Pekao费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + 
                   costs.valuation + costs.otherCosts;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updatePekaoResults(loanAmount, monthlyPayment, totalInterest, costs, 
                      totalCost, monthlyTotal, rrso, loanTerm, pekaoProfile, pekaoProgram);
}

function adjustPekaoRate(baseRate, profile, program) {
    var adjustment = 0;
    
    // 基于客户档案调整
    switch(profile) {
        case 'premium': adjustment -= 0.3; break;
        case 'private': adjustment -= 0.5; break;
        case 'young': adjustment -= 0.2; break;
        case 'existing': adjustment -= 0.1; break;
    }
    
    // 基于程序调整
    switch(program) {
        case 'first': adjustment -= 0.3; break;
        case 'family': adjustment -= 0.2; break;
        case 'green': adjustment -= 0.4; break;
        case 'investment': adjustment += 0.2; break;
    }
    
    return Math.max(baseRate + adjustment, 2.0); // 最低2%
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;
    
    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updatePekaoResults(loanAmount, monthlyPayment, totalInterest, costs, 
                           totalCost, monthlyTotal, rrso, loanTerm, pekaoProfile, pekaoProgram) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Kredyt Pekao: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新szczegóły kredytu Pekao
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML = 
            '<h3>Szczegóły kredytu w Banku Pekao SA:</h3>' +
            '<p><strong>RRSO Pekao:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi opłatami Banku Pekao SA)</p>' +
            '<p><strong>Status klienta:</strong> ' + getPekaoClientStatus(pekaoProfile) + '</p>' +
            '<p><strong>Program kredytowy:</strong> ' + getPekaoProgramName(pekaoProgram) + '</p>' +
            '<p><strong>Całkowity koszt Pekao:</strong> ' + formatNumber(totalCost) + ' zł (kredyt + wszystkie opłaty)</p>' +
            '<p><strong>Przepłacone odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + Math.round((totalInterest/loanAmount)*100) + '% kwoty kredytu)</p>';
    }
}

function getPekaoClientStatus(profile) {
    switch(profile) {
        case 'premium': return 'Klient Pekao Premium (marża preferencyjna)';
        case 'private': return 'Pekao Private Banking (najlepsza marża)';
        case 'young': return 'Pekao dla młodych (marża promocyjna)';
        case 'existing': return 'Obecny klient Pekao (marża preferencyjna)';
        default: return 'Nowy klient Banku Pekao SA';
    }
}

function getPekaoProgramName(program) {
    switch(program) {
        case 'first': return 'Pierwszy kredyt Pekao (dla młodych)';
        case 'family': return 'Kredyt rodzinny na start Pekao';
        case 'green': return 'Kredyt ekologiczny Pekao (energooszczędny)';
        case 'investment': return 'Kredyt inwestycyjny Pekao';
        default: return 'Kredyt hipoteczny standardowy Pekao';
    }
}

function setPekaoProgram(program) {
    document.getElementById('cpekaoprogram').value = program;
    
    // 调整利率基于程序
    var baseRate = 7.15;
    switch(program) {
        case 'first': 
            document.getElementById('cinterestrate').value = '6.85';
            document.getElementById('cpekaoprofile').value = 'young';
            break;
        case 'family': 
            document.getElementById('cinterestrate').value = '6.95';
            break;
        case 'green': 
            document.getElementById('cinterestrate').value = '6.75';
            break;
        case 'premium': 
            document.getElementById('cinterestrate').value = '6.65';
            document.getElementById('cpekaoprofile').value = 'premium';
            break;
        default: 
            document.getElementById('cinterestrate').value = '7.15';
            break;
    }
    
    calculatePekaoMortgage();
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Koszty i opłaty Banku Pekao SA';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Koszty i opłaty Banku Pekao SA';
    }
    calculatePekaoMortgage();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Programy Pekao</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Programy Pekao</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.15';
    document.getElementById('cpekaoprofile').value = 'new';
    document.getElementById('cpekaoprogram').value = 'standard';
    calculatePekaoMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora Pekao zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie marż &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Programy Pekao</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Programy Pekao &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie marż</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePekaoMortgage();
};
</script>

</body></html>
