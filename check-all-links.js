const fs = require('fs');
const path = require('path');

// 获取所有HTML文件
const htmlFiles = fs.readdirSync('.').filter(file => 
    file.endsWith('.html') && 
    !file.includes('test') && 
    !file.includes('TEST') && 
    !file.includes('debug') && 
    !file.includes('WORKING') &&
    !file.includes('FINAL') &&
    !file.includes('check-') &&
    !file.includes('fix-')
);

console.log(`找到 ${htmlFiles.length} 个HTML文件进行检查`);

const allLinks = new Set();
const brokenLinks = new Map(); // 文件 -> 损坏的链接数组
const linkSources = new Map(); // 链接 -> 使用该链接的文件数组

// 提取链接的正则表达式
const hrefRegex = /href="([^"]+\.html)"/g;

// 检查每个HTML文件
htmlFiles.forEach(file => {
    try {
        const content = fs.readFileSync(file, 'utf8');
        const fileLinks = [];
        let match;
        
        while ((match = hrefRegex.exec(content)) !== null) {
            const link = match[1];
            // 过滤掉外部链接、绝对路径和特殊链接
            if (!link.startsWith('http') && 
                !link.startsWith('//') && 
                !link.startsWith('/konto/') &&
                !link.startsWith('/kalkulatory-') &&
                link.endsWith('.html')) {
                
                // 移除开头的 / 如果存在
                const cleanLink = link.startsWith('/') ? link.substring(1) : link;
                
                allLinks.add(cleanLink);
                fileLinks.push(cleanLink);
                
                // 记录链接来源
                if (!linkSources.has(cleanLink)) {
                    linkSources.set(cleanLink, []);
                }
                linkSources.get(cleanLink).push(file);
            }
        }
        
        console.log(`${file}: 找到 ${fileLinks.length} 个内部链接`);
        
    } catch (error) {
        console.error(`读取文件 ${file} 时出错:`, error.message);
    }
});

console.log(`\n总共找到 ${allLinks.size} 个唯一的内部链接`);

// 检查链接是否存在
const existingLinks = [];
const missingLinks = [];

allLinks.forEach(link => {
    if (fs.existsSync(link)) {
        existingLinks.push(link);
    } else {
        missingLinks.push(link);
        
        // 记录哪些文件包含这个损坏的链接
        const sources = linkSources.get(link) || [];
        sources.forEach(sourceFile => {
            if (!brokenLinks.has(sourceFile)) {
                brokenLinks.set(sourceFile, []);
            }
            brokenLinks.get(sourceFile).push(link);
        });
    }
});

console.log('\n=== 链接检查结果 ===');
console.log(`✅ 存在的链接: ${existingLinks.length}`);
console.log(`❌ 缺失的链接: ${missingLinks.length}`);

if (missingLinks.length > 0) {
    console.log('\n=== 缺失的链接详情 ===');
    missingLinks.forEach(link => {
        const sources = linkSources.get(link) || [];
        console.log(`❌ ${link}`);
        console.log(`   被以下文件引用: ${sources.join(', ')}`);
    });
    
    console.log('\n=== 包含404错误的文件 ===');
    brokenLinks.forEach((links, file) => {
        console.log(`📄 ${file}: ${links.length} 个404错误`);
        links.forEach(link => {
            console.log(`   - ${link}`);
        });
    });
}

// 生成修复建议
if (missingLinks.length > 0) {
    console.log('\n=== 修复建议 ===');
    
    missingLinks.forEach(link => {
        let template = 'o-nas.html'; // 默认模板
        
        // 智能选择模板
        if (link.includes('kalkulator')) {
            if (existingLinks.find(f => f.includes('kalkulator-raty'))) {
                template = 'kalkulator-raty-kredytu-hipotecznego.html';
            } else if (existingLinks.find(f => f.includes('kalkulator'))) {
                template = existingLinks.find(f => f.includes('kalkulator'));
            }
        }
        
        if (link.includes('pko')) template = 'pko-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('ing')) template = 'ing-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('mbank')) template = 'mbank-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('santander')) template = 'santander-kalkulator-kredytu-hipotecznego.html';
        
        console.log(`创建 ${link}:`);
        console.log(`  copy "${template}" "${link}"`);
    });
}

// 生成详细报告
const report = {
    timestamp: new Date().toISOString(),
    summary: {
        total_html_files: htmlFiles.length,
        total_unique_links: allLinks.size,
        existing_links: existingLinks.length,
        missing_links: missingLinks.length,
        success_rate: Math.round((existingLinks.length / allLinks.size) * 100)
    },
    existing_links: existingLinks.sort(),
    missing_links: missingLinks.sort(),
    broken_links_by_file: Object.fromEntries(brokenLinks),
    link_sources: Object.fromEntries(linkSources),
    html_files_checked: htmlFiles.sort()
};

fs.writeFileSync('comprehensive-link-report.json', JSON.stringify(report, null, 2));
console.log('\n📊 详细报告已保存到: comprehensive-link-report.json');

// 生成修复脚本
if (missingLinks.length > 0) {
    let fixScript = '#!/bin/bash\n# 自动修复404错误脚本\n\n';
    
    missingLinks.forEach(link => {
        let template = 'o-nas.html';
        
        if (link.includes('kalkulator')) {
            if (existingLinks.find(f => f.includes('kalkulator-raty'))) {
                template = 'kalkulator-raty-kredytu-hipotecznego.html';
            }
        }
        
        if (link.includes('pko')) template = 'pko-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('ing')) template = 'ing-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('mbank')) template = 'mbank-kalkulator-kredytu-hipotecznego.html';
        if (link.includes('santander')) template = 'santander-kalkulator-kredytu-hipotecznego.html';
        
        fixScript += `echo "创建 ${link}..."\n`;
        fixScript += `cp "${template}" "${link}"\n\n`;
    });
    
    fs.writeFileSync('fix-404-errors.sh', fixScript);
    console.log('🔧 修复脚本已保存到: fix-404-errors.sh');
}

console.log('\n=== 总结 ===');
if (missingLinks.length === 0) {
    console.log('🎉 恭喜！没有发现404错误！');
} else {
    console.log(`⚠️  发现 ${missingLinks.length} 个404错误需要修复`);
    console.log(`📄 影响 ${brokenLinks.size} 个HTML文件`);
}
