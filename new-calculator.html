<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Kalkulator kredytu hipotecznego</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #fff; }
        .container { max-width: 1000px; margin: 0 auto; }
        .left { float: left; width: 45%; }
        .right { float: right; width: 45%; }
        .clear { clear: both; }
        table { width: 100%; border-collapse: collapse; }
        td { padding: 8px; border: 1px solid #ddd; }
        input, select { padding: 5px; width: 100px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result-title { font-size: 20px; font-weight: bold; color: #333; margin: 10px 0; }
        .result-table { margin-top: 10px; }
        .result-table td { text-align: right; }
        .highlight { background: #f0f0f0; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kalkulator kredytu hipotecznego</h1>
        
        <div class="left">
            <h2>Dane kredytu</h2>
            <table>
                <tr>
                    <td>Cena nieruchomości:</td>
                    <td><input type="number" id="price" value="500000"> zł</td>
                </tr>
                <tr>
                    <td>Wkład własny:</td>
                    <td>
                        <input type="number" id="down" value="20">
                        <select id="downUnit">
                            <option value="percent">%</option>
                            <option value="amount">zł</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>Okres kredytowania:</td>
                    <td><input type="number" id="years" value="25"> lat</td>
                </tr>
                <tr>
                    <td>Oprocentowanie:</td>
                    <td><input type="number" id="rate" value="7.25" step="0.01"> %</td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">
                        <button onclick="calculate()">Oblicz</button>
                        <button onclick="reset()">Wyczyść</button>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="right">
            <div class="result-title" id="resultTitle">Miesięczna rata: 0 zł</div>
            
            <table class="result-table">
                <tr>
                    <td></td>
                    <td><strong>Miesięcznie</strong></td>
                    <td><strong>Łącznie</strong></td>
                </tr>
                <tr class="highlight">
                    <td><strong>Rata kredytu</strong></td>
                    <td id="monthlyPayment"><strong>0 zł</strong></td>
                    <td id="totalPayment"><strong>0 zł</strong></td>
                </tr>
                <tr>
                    <td>Podatek od nieruchomości</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>Ubezpieczenie domu</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>Ubezpieczenie kredytu</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>Opłaty administracyjne</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr class="highlight">
                    <td><strong>Łącznie</strong></td>
                    <td id="monthlyTotal"><strong>0 zł</strong></td>
                    <td id="totalCost"><strong>0 zł</strong></td>
                </tr>
            </table>
            
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6;">
                <h3>Szczegóły kredytu:</h3>
                <div id="details">Wprowadź dane i kliknij "Oblicz"</div>
            </div>
        </div>
        
        <div class="clear"></div>
    </div>

    <script>
        function calculate() {
            // Pobierz wartości
            var price = parseFloat(document.getElementById('price').value) || 0;
            var down = parseFloat(document.getElementById('down').value) || 0;
            var downUnit = document.getElementById('downUnit').value;
            var years = parseFloat(document.getElementById('years').value) || 0;
            var rate = parseFloat(document.getElementById('rate').value) || 0;
            
            // Oblicz wkład własny
            var downAmount = (downUnit === 'percent') ? price * (down / 100) : down;
            var loanAmount = price - downAmount;
            
            // Oblicz ratę miesięczną
            var monthlyRate = rate / 100 / 12;
            var numPayments = years * 12;
            var monthlyPayment;
            
            if (monthlyRate > 0) {
                monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1);
            } else {
                monthlyPayment = loanAmount / numPayments;
            }
            
            var totalPayment = monthlyPayment * numPayments;
            var totalInterest = totalPayment - loanAmount;
            
            // Formatuj liczby
            function formatMoney(amount) {
                return Math.round(amount).toLocaleString('pl-PL') + ' zł';
            }
            
            // Aktualizuj wyniki
            document.getElementById('resultTitle').innerHTML = 'Miesięczna rata: ' + formatMoney(monthlyPayment);
            document.getElementById('monthlyPayment').innerHTML = '<strong>' + formatMoney(monthlyPayment) + '</strong>';
            document.getElementById('totalPayment').innerHTML = '<strong>' + formatMoney(totalPayment) + '</strong>';
            document.getElementById('monthlyTotal').innerHTML = '<strong>' + formatMoney(monthlyPayment) + '</strong>';
            document.getElementById('totalCost').innerHTML = '<strong>' + formatMoney(totalPayment) + '</strong>';
            
            // Aktualizuj szczegóły
            var details = 
                '<p><strong>Kwota kredytu:</strong> ' + formatMoney(loanAmount) + '</p>' +
                '<p><strong>Wkład własny:</strong> ' + formatMoney(downAmount) + ' (' + down + (downUnit === 'percent' ? '%' : ' zł') + ')</p>' +
                '<p><strong>Okres kredytowania:</strong> ' + years + ' lat (' + numPayments + ' miesięcy)</p>' +
                '<p><strong>Oprocentowanie:</strong> ' + rate + '% rocznie</p>' +
                '<p><strong>Przepłacone odsetki:</strong> ' + formatMoney(totalInterest) + '</p>' +
                '<p><strong>Stosunek odsetek do kwoty kredytu:</strong> ' + Math.round((totalInterest / loanAmount) * 100) + '%</p>';
            
            document.getElementById('details').innerHTML = details;
        }
        
        function reset() {
            document.getElementById('price').value = '500000';
            document.getElementById('down').value = '20';
            document.getElementById('downUnit').value = 'percent';
            document.getElementById('years').value = '25';
            document.getElementById('rate').value = '7.25';
            
            document.getElementById('resultTitle').innerHTML = 'Miesięczna rata: 0 zł';
            document.getElementById('monthlyPayment').innerHTML = '<strong>0 zł</strong>';
            document.getElementById('totalPayment').innerHTML = '<strong>0 zł</strong>';
            document.getElementById('monthlyTotal').innerHTML = '<strong>0 zł</strong>';
            document.getElementById('totalCost').innerHTML = '<strong>0 zł</strong>';
            document.getElementById('details').innerHTML = 'Wprowadź dane i kliknij "Oblicz"';
        }
        
        // Automatyczne obliczenie przy załadowaniu strony
        window.onload = function() {
            calculate();
        };
    </script>
</body>
</html>
