<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Debug Original Structure</title>
	<meta name="description" content="Debug test">
	<style>
		body { font-family: Arial, sans-serif; margin: 20px; }
		.clefthalf { float: left; width: 45%; }
		.crighthalf { float: right; width: 45%; }
		.h2result { color: #333; font-size: 18px; margin: 10px 0; }
		table { border-collapse: collapse; width: 100%; }
		td { padding: 5px; border: 1px solid #ddd; }
		input, select { padding: 5px; margin: 2px; }
		input[type="button"] { background: #007bff; color: white; border: none; padding: 10px 15px; cursor: pointer; }
		#clear { clear: both; }
		.debug { background: #ffffcc; padding: 10px; margin: 10px 0; border: 1px solid #ffcc00; }
	</style>
</head><body>

<h1>Debug Original Structure</h1>

<div class="debug">
    <h3>Debug Information:</h3>
    <div id="debug">Page loading...</div>
</div>

<div class="clefthalf">
<form name="calform">
<table align="center">
<tbody><tr><td align="right">Cena nieruchomości</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000"></td><td>zł</td></tr>
<tr><td align="right">Wkład własny</td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20"></td>
<td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania</td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie</td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25"></td><td>%</td></tr>
<tr><td colspan="3" align="center">
<input type="button" name="x" value="Oblicz" onclick="calculateMortgage();">
<input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
</td></tr>
</tbody></table>
</form>
</div>

<div class="crighthalf">
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Miesięczna rata: &nbsp; 0 zł</h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
<tr><td>Podatek od nieruchomości</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie domu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie kredytu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Opłaty administracyjne</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr bgcolor="#dddddd"><td><b>Łącznie</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<script>
function calculateMortgage() {
    var debug = document.getElementById('debug');
    debug.innerHTML += '<br>🔄 calculateMortgage called at ' + new Date().toLocaleTimeString();
    
    try {
        // Test element access
        var h = document.getElementById('chouseprice');
        var d = document.getElementById('cdownpayment');
        var u = document.getElementById('cdownpaymentunit');
        var t = document.getElementById('cloanterm');
        var r = document.getElementById('cinterestrate');
        
        debug.innerHTML += '<br>📋 Element access test:';
        debug.innerHTML += '<br>  chouseprice: ' + (h ? 'FOUND' : 'NOT FOUND');
        debug.innerHTML += '<br>  cdownpayment: ' + (d ? 'FOUND' : 'NOT FOUND');
        debug.innerHTML += '<br>  cdownpaymentunit: ' + (u ? 'FOUND' : 'NOT FOUND');
        debug.innerHTML += '<br>  cloanterm: ' + (t ? 'FOUND' : 'NOT FOUND');
        debug.innerHTML += '<br>  cinterestrate: ' + (r ? 'FOUND' : 'NOT FOUND');
        
        if (!h || !d || !u || !t || !r) {
            debug.innerHTML += '<br>❌ ERROR: Some input elements not found!';
            return;
        }
        
        // Get values
        var housePrice = parseFloat(h.value);
        var downPayment = parseFloat(d.value);
        var downPaymentUnit = u.value;
        var loanTerm = parseFloat(t.value);
        var interestRate = parseFloat(r.value);
        
        debug.innerHTML += '<br>📊 Input values:';
        debug.innerHTML += '<br>  House Price: ' + housePrice;
        debug.innerHTML += '<br>  Down Payment: ' + downPayment + ' ' + downPaymentUnit;
        debug.innerHTML += '<br>  Loan Term: ' + loanTerm + ' years';
        debug.innerHTML += '<br>  Interest Rate: ' + interestRate + '%';
        
        // Calculate
        var downPaymentAmount = (downPaymentUnit === 'p') ? housePrice * (downPayment / 100) : downPayment;
        var loanAmount = housePrice - downPaymentAmount;
        var monthlyRate = interestRate / 100 / 12;
        var numPayments = loanTerm * 12;
        var monthlyPayment = (monthlyRate > 0) ? 
            loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1) : 
            loanAmount / numPayments;
        
        debug.innerHTML += '<br>💰 Calculations:';
        debug.innerHTML += '<br>  Down Payment Amount: ' + Math.round(downPaymentAmount).toLocaleString('pl-PL') + ' zł';
        debug.innerHTML += '<br>  Loan Amount: ' + Math.round(loanAmount).toLocaleString('pl-PL') + ' zł';
        debug.innerHTML += '<br>  Monthly Payment: ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
        
        // Test h2result element
        var h2result = document.querySelector('.h2result');
        debug.innerHTML += '<br>🔍 Looking for .h2result element: ' + (h2result ? 'FOUND' : 'NOT FOUND');
        
        if (h2result) {
            var newContent = 'Miesięczna rata: &nbsp; ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
            h2result.innerHTML = newContent;
            debug.innerHTML += '<br>✅ Updated h2result to: ' + newContent;
        }
        
        // Test table element
        var tableRow = document.querySelector('.crighthalf table table tbody tr:nth-child(2)');
        debug.innerHTML += '<br>🔍 Looking for table row: ' + (tableRow ? 'FOUND' : 'NOT FOUND');
        
        if (tableRow) {
            debug.innerHTML += '<br>📋 Table row has ' + tableRow.cells.length + ' cells';
            if (tableRow.cells.length >= 3) {
                tableRow.cells[1].innerHTML = '<b>' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł</b>';
                tableRow.cells[2].innerHTML = '<b>' + Math.round(monthlyPayment * numPayments).toLocaleString('pl-PL') + ' zł</b>';
                debug.innerHTML += '<br>✅ Updated table cells';
            } else {
                debug.innerHTML += '<br>❌ Table row does not have enough cells';
            }
        }
        
        debug.innerHTML += '<br>🎉 Function completed successfully!';
        
    } catch (error) {
        debug.innerHTML += '<br>❌ ERROR: ' + error.message;
        debug.innerHTML += '<br>Stack: ' + error.stack;
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.25';
    
    var h2result = document.querySelector('.h2result');
    if (h2result) {
        h2result.innerHTML = 'Miesięczna rata: &nbsp; 0 zł';
    }
    
    document.getElementById('debug').innerHTML = 'Form cleared. Click "Oblicz" to calculate.';
}

// Test on page load
window.onload = function() {
    var debug = document.getElementById('debug');
    debug.innerHTML = '🟢 Page loaded successfully. JavaScript is working.';
    debug.innerHTML += '<br>📋 Testing element access on page load...';
    
    var elements = ['chouseprice', 'cdownpayment', 'cdownpaymentunit', 'cloanterm', 'cinterestrate'];
    elements.forEach(function(id) {
        var element = document.getElementById(id);
        debug.innerHTML += '<br>  ' + id + ': ' + (element ? 'FOUND (value: ' + element.value + ')' : 'NOT FOUND');
    });
    
    var h2result = document.querySelector('.h2result');
    debug.innerHTML += '<br>  .h2result: ' + (h2result ? 'FOUND' : 'NOT FOUND');
    
    var tableRow = document.querySelector('.crighthalf table table tbody tr:nth-child(2)');
    debug.innerHTML += '<br>  table row: ' + (tableRow ? 'FOUND' : 'NOT FOUND');
    
    debug.innerHTML += '<br><br>🎯 Ready to test! Click "Oblicz" button.';
};
</script>

</body></html>
