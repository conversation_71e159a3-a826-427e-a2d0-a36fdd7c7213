const fs = require('fs');
const path = require('path');

// 读取index.html文件
const indexPath = 'index.html';
const indexContent = fs.readFileSync(indexPath, 'utf8');

// 提取所有href链接
const hrefRegex = /href="([^"]+\.html)"/g;
const links = [];
let match;

while ((match = hrefRegex.exec(indexContent)) !== null) {
    const link = match[1];
    // 过滤掉外部链接和以/开头的链接
    if (!link.startsWith('http') && !link.startsWith('/') && link.endsWith('.html')) {
        links.push(link);
    }
}

// 去重
const uniqueLinks = [...new Set(links)];

console.log('=== 在index.html中找到的链接 ===');
uniqueLinks.forEach(link => console.log(link));

console.log('\n=== 检查文件是否存在 ===');
const missingFiles = [];
const existingFiles = [];

uniqueLinks.forEach(link => {
    if (fs.existsSync(link)) {
        console.log(`✅ ${link} - 存在`);
        existingFiles.push(link);
    } else {
        console.log(`❌ ${link} - 缺失`);
        missingFiles.push(link);
    }
});

console.log('\n=== 总结 ===');
console.log(`总链接数: ${uniqueLinks.length}`);
console.log(`存在文件: ${existingFiles.length}`);
console.log(`缺失文件: ${missingFiles.length}`);

if (missingFiles.length > 0) {
    console.log('\n=== 缺失的文件列表 ===');
    missingFiles.forEach(file => console.log(`- ${file}`));
    
    // 生成创建命令
    console.log('\n=== 建议的修复命令 ===');
    missingFiles.forEach(file => {
        // 找到最相似的现有文件作为模板
        let template = 'o-nas.html'; // 默认模板
        
        if (file.includes('kalkulator')) {
            if (existingFiles.find(f => f.includes('kalkulator-raty'))) {
                template = 'kalkulator-raty-kredytu-hipotecznego.html';
            } else if (existingFiles.find(f => f.includes('kalkulator'))) {
                template = existingFiles.find(f => f.includes('kalkulator'));
            }
        }
        
        console.log(`copy "${template}" "${file}"`);
    });
}

// 生成JSON报告
const report = {
    timestamp: new Date().toISOString(),
    totalLinks: uniqueLinks.length,
    existingFiles: existingFiles,
    missingFiles: missingFiles,
    successRate: Math.round((existingFiles.length / uniqueLinks.length) * 100)
};

fs.writeFileSync('link-check-report.json', JSON.stringify(report, null, 2));
console.log('\n报告已保存到: link-check-report.json');
