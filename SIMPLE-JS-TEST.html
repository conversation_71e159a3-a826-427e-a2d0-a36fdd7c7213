<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JavaScript测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .big-button { padding: 30px 60px; font-size: 24px; background: #28a745; color: white; border: none; cursor: pointer; border-radius: 10px; margin: 20px; }
        .result { font-size: 20px; margin: 20px; padding: 20px; background: #f8f9fa; border: 2px solid #28a745; border-radius: 10px; }
    </style>
</head>
<body>
    <h1>🔧 JavaScript功能测试</h1>
    
    <p style="font-size: 18px;">如果JavaScript工作正常，点击下面的按钮应该弹出提示框：</p>
    
    <button class="big-button" onclick="alert('✅ JavaScript工作正常！')">
        点击测试JavaScript
    </button>
    
    <div class="result">
        <h2>测试结果：</h2>
        <p><strong>如果点击按钮弹出提示框</strong> → JavaScript正常，所有计算器都应该能工作</p>
        <p><strong>如果点击按钮没有反应</strong> → JavaScript被禁用，需要在浏览器设置中启用</p>
    </div>
    
    <hr style="margin: 40px 0;">
    
    <h2>🧮 简单计算器测试</h2>
    <p>房价: <input type="number" id="price" value="500000" style="padding: 10px; font-size: 16px;"> zł</p>
    <p>首付: <input type="number" id="down" value="20" style="padding: 10px; font-size: 16px;"> %</p>
    
    <button class="big-button" onclick="
        var p = parseFloat(document.getElementById('price').value);
        var d = parseFloat(document.getElementById('down').value);
        var loan = p - (p * d / 100);
        var monthly = loan * (0.0725/12 * Math.pow(1+0.0725/12, 300)) / (Math.pow(1+0.0725/12, 300) - 1);
        document.getElementById('calcResult').innerHTML = '月供: ' + Math.round(monthly).toLocaleString() + ' zł';
    ">
        计算月供
    </button>
    
    <div id="calcResult" class="result">结果将显示在这里</div>
    
    <div style="margin-top: 40px; padding: 20px; background: #fff3cd; border-radius: 10px;">
        <h3>⚠️ 如果以上测试都失败，请按以下步骤启用JavaScript：</h3>
        <div style="text-align: left; display: inline-block;">
            <h4>Chrome浏览器：</h4>
            <p>1. 点击右上角三个点 → 设置<br>
            2. 隐私设置和安全性 → 网站设置<br>
            3. JavaScript → 允许（推荐）</p>
            
            <h4>Firefox浏览器：</h4>
            <p>1. 地址栏输入：about:config<br>
            2. 搜索：javascript.enabled<br>
            3. 确保值为 true</p>
            
            <h4>Edge浏览器：</h4>
            <p>1. 右上角三个点 → 设置<br>
            2. Cookie和网站权限 → JavaScript<br>
            3. 确保开关是"开启"状态</p>
        </div>
    </div>
</body>
</html>
