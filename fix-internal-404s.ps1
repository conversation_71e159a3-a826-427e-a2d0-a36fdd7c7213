# 批量修复内页404错误的PowerShell脚本
Write-Host "=== 批量修复内页404错误 ===" -ForegroundColor Green

# 需要修复的文件列表
$filesToFix = @(
    "kalkulator-nadplaty-kredytu-hipotecznego.html",
    "pko-kalkulator-kredytu-hipotecznego.html",
    "ing-kalkulator-kredytu-hipotecznego.html",
    "mbank-kalkulator-kredytu-hipotecznego.html",
    "santander-kalkulator-kredytu-hipotecznego.html",
    "alior-bank-kalkulator-kredytu-hipotecznego.html",
    "kalkulator-kosztow-kredytu-hipotecznego.html",
    "kalkulator-refinansowania-kredytu-hipotecznego.html",
    "szczegolowy-kalkulator-kredytu-hipotecznego.html",
    "kalkulator-odsetek-kredytu-hipotecznego.html",
    "wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html",
    "kalkulator-nadplacania-kredytu-hipotecznego.html"
)

# 需要替换的错误链接模式
$replacements = @{
    'href="/kalkulatory-finansowe.html"' = 'href="/"'
    'href="/kalkulator-kredytu-hipotecznego.html"' = 'href="/"'
    'href="/kalkulator-zdolnosci-kredytowej.html"' = 'href="kalkulator-zdolnosci-kredytowej.html"'
    'href="/kalkulator-raty-kredytu-hipotecznego.html"' = 'href="kalkulator-raty-kredytu-hipotecznego.html"'
    'href="/kalkulator-nadplaty-kredytu-hipotecznego.html"' = 'href="kalkulator-nadplaty-kredytu-hipotecznego.html"'
    'href="/o-nas.html"' = 'href="o-nas.html"'
    'href="/kontakt.html"' = 'href="kontakt.html"'
    'href="/polityka-prywatnosci.html"' = 'href="polityka-prywatnosci.html"'
    'href="/regulamin.html"' = 'href="regulamin.html"'
    'href="/mapa-strony.html"' = ''
    'href="/kalkulator-kredytu.html"' = 'href="kalkulator-raty-kredytu-hipotecznego.html"'
    'href="/kalkulator-leasingu.html"' = 'href="kalkulator-kosztow-kredytu-hipotecznego.html"'
    'href="/kalkulator-oszczednosci.html"' = 'href="kalkulator-nadplaty-kredytu-hipotecznego.html"'
    'href="/kalkulator-emerytury.html"' = 'href="szczegolowy-kalkulator-kredytu-hipotecznego.html"'
    'href="/kalkulator-inwestycji.html"' = 'href="kalkulator-odsetek-kredytu-hipotecznego.html"'
    'href="/kalkulator-raty-kredytu.html"' = 'href="kalkulator-raty-kredytu-hipotecznego.html"'
    'href="/kalkulator-oprocentowania.html"' = 'href="kalkulator-odsetek-kredytu-hipotecznego.html"'
    'href="/symulator-kredytu.html"' = 'href="szczegolowy-kalkulator-kredytu-hipotecznego.html"'
    'href="/kalkulator-splaty-kredytu.html"' = 'href="wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html"'
}

$totalFixed = 0
$totalFiles = 0

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        Write-Host "修复文件: $file" -ForegroundColor Cyan
        $totalFiles++
        
        try {
            $content = Get-Content $file -Raw -Encoding UTF8
            $originalContent = $content
            $fileFixed = 0
            
            # 应用所有替换
            foreach ($pattern in $replacements.Keys) {
                $replacement = $replacements[$pattern]
                
                if ($content -match [regex]::Escape($pattern)) {
                    if ($replacement -eq '') {
                        # 删除整个链接行
                        $content = $content -replace ".*$([regex]::Escape($pattern)).*\r?\n", ""
                        Write-Host "  - 删除: $pattern" -ForegroundColor Yellow
                    } else {
                        $content = $content -replace [regex]::Escape($pattern), $replacement
                        Write-Host "  - 替换: $pattern -> $replacement" -ForegroundColor Green
                    }
                    $fileFixed++
                }
            }
            
            # 特殊处理：更新侧边栏内容
            if ($content -match '<div id="octitle">\s*<a href="[^"]*">Kalkulatory finansowe</a>\s*</div>') {
                $content = $content -replace '<div id="octitle">\s*<a href="[^"]*">Kalkulatory finansowe</a>\s*</div>', '<div id="octitle">Popularne Kalkulatory</div>'
                $fileFixed++
                Write-Host "  - 更新侧边栏标题" -ForegroundColor Green
            }
            
            # 如果有修改，保存文件
            if ($content -ne $originalContent) {
                $content | Out-File -FilePath $file -Encoding UTF8 -NoNewline
                Write-Host "  ✅ 修复了 $fileFixed 个问题" -ForegroundColor Green
                $totalFixed += $fileFixed
            } else {
                Write-Host "  ℹ️  没有发现需要修复的问题" -ForegroundColor Gray
            }
            
        } catch {
            Write-Host "  ❌ 错误: $_" -ForegroundColor Red
        }
        
    } else {
        Write-Host "⚠️  文件不存在: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== 修复完成 ===" -ForegroundColor Green
Write-Host "处理的文件: $totalFiles" -ForegroundColor Cyan
Write-Host "修复的问题: $totalFixed" -ForegroundColor Green

if ($totalFixed -gt 0) {
    Write-Host ""
    Write-Host "🎉 成功修复了 $totalFixed 个404错误！" -ForegroundColor Green
    Write-Host "建议运行链接检查工具验证修复结果。" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "ℹ️  没有发现需要修复的404错误。" -ForegroundColor Gray
}
