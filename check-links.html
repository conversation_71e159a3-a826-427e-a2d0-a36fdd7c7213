<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Checker - Sprawd<PERSON>ie 404 błędów</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        h1 { color: #333; text-align: center; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .link-list { margin: 10px 0; }
        .link-item { margin: 5px 0; padding: 8px; background: #f8f9fa; border-radius: 3px; }
        .check-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        .check-btn:hover { background: #0056b3; }
        .results { margin-top: 20px; }
        .file-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Link Checker - Sprawdzanie 404 błędów</h1>
        
        <div class="status warning">
            <strong>⚠️ Uwaga:</strong> Ten narzędzie sprawdza czy pliki istnieją w bieżącym katalogu.
        </div>
        
        <button class="check-btn" onclick="checkAllLinks()">🔍 Sprawdź wszystkie linki</button>
        <button class="check-btn" onclick="checkMainPages()">📄 Sprawdź główne strony</button>
        <button class="check-btn" onclick="generateMissingFiles()">🔧 Generuj brakujące pliki</button>
        
        <div id="results" class="results"></div>
    </div>

    <script>
        // Lista wszystkich linków z index.html i innych stron
        const allLinks = [
            // Główne kalkulatory
            'kalkulator-raty-kredytu-hipotecznego.html',
            'kalkulator-zdolnosci-kredytowej.html',
            'kalkulator-kosztow-kredytu-hipotecznego.html',
            'kalkulator-nadplaty-kredytu-hipotecznego.html',
            
            // Kalkulatory bankowe
            'pko-kalkulator-kredytu-hipotecznego.html',
            'ing-kalkulator-kredytu-hipotecznego.html',
            'mbank-kalkulator-kredytu-hipotecznego.html',
            'pekao-kalkulator-kredytu-hipotecznego.html',
            'santander-kalkulator-kredytu-hipotecznego.html',
            'alior-bank-kalkulator-kredytu-hipotecznego.html',
            
            // Dodatkowe kalkulatory
            'kalkulator-nadplacania-kredytu-hipotecznego.html',
            'kalkulator-refinansowania-kredytu-hipotecznego.html',
            'szczegolowy-kalkulator-kredytu-hipotecznego.html',
            'kalkulator-odsetek-kredytu-hipotecznego.html',
            'wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html',
            'kalkulator-nadplaty-kredytu-hipotecznego-pko-bp.html',
            
            // Strony SEO
            'o-nas.html',
            'kontakt.html',
            'polityka-prywatnosci.html',
            'regulamin.html',
            
            // Inne kalkulatory
            'millenium-kalkulator-kredytu-hipotecznego.html',
            'kalkulator-kredytu-hipotecznego-pko-sa.html',
            'kalkulator-nadplaty-kredytu-hipotecznego-excel.html',
            'rata-kredytu-hipotecznego-kalkulator.html',
            'kalkulator-zdolnosci-kredytu-hipotecznego.html',
            'kalkulator-kredytu-hipotecznego-2-procent.html',
            'kalkulator-splaty-kredytu-hipotecznego.html',
            'pekao-sa-kalkulator-kredytu-hipotecznego.html',
            'kalkulator-kredytu-hipotecznego-pko-bp.html',
            'kalkulator-kredytu-hipotecznego-bankier.html',
            'kalkulator-kredytu-hipotecznego-gofin.html',
            'kalkulator-kredytu-hipotecznego-otodom.html',
            'kalkulator-kredytu-hipotecznego-totalmoney.html'
        ];

        const mainPages = [
            'o-nas.html',
            'kontakt.html',
            'polityka-prywatnosci.html',
            'regulamin.html'
        ];

        async function checkFileExists(filename) {
            try {
                const response = await fetch(filename, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        async function checkAllLinks() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status">🔄 Sprawdzanie linków...</div>';
            
            const existingFiles = [];
            const missingFiles = [];
            
            for (const link of allLinks) {
                const exists = await checkFileExists(link);
                if (exists) {
                    existingFiles.push(link);
                } else {
                    missingFiles.push(link);
                }
            }
            
            let html = '<div class="file-section">';
            html += `<h3>✅ Istniejące pliki (${existingFiles.length})</h3>`;
            existingFiles.forEach(file => {
                html += `<div class="link-item" style="background: #d4edda;">✅ ${file}</div>`;
            });
            html += '</div>';
            
            html += '<div class="file-section">';
            html += `<h3>❌ Brakujące pliki (${missingFiles.length})</h3>`;
            missingFiles.forEach(file => {
                html += `<div class="link-item" style="background: #f8d7da;">❌ ${file}</div>`;
            });
            html += '</div>';
            
            if (missingFiles.length > 0) {
                html += '<div class="status error">';
                html += `<strong>⚠️ Znaleziono ${missingFiles.length} brakujących plików!</strong><br>`;
                html += 'Te linki będą powodować błędy 404. Kliknij "Generuj brakujące pliki" aby je utworzyć.';
                html += '</div>';
            } else {
                html += '<div class="status success">';
                html += '<strong>🎉 Wszystkie linki działają poprawnie!</strong>';
                html += '</div>';
            }
            
            resultsDiv.innerHTML = html;
        }

        async function checkMainPages() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status">🔄 Sprawdzanie głównych stron SEO...</div>';
            
            const results = [];
            
            for (const page of mainPages) {
                const exists = await checkFileExists(page);
                results.push({ page, exists });
            }
            
            let html = '<div class="file-section">';
            html += '<h3>📄 Status głównych stron SEO</h3>';
            
            results.forEach(result => {
                const status = result.exists ? '✅' : '❌';
                const bgColor = result.exists ? '#d4edda' : '#f8d7da';
                html += `<div class="link-item" style="background: ${bgColor};">${status} ${result.page}</div>`;
            });
            
            html += '</div>';
            
            const missingCount = results.filter(r => !r.exists).length;
            if (missingCount > 0) {
                html += '<div class="status warning">';
                html += `<strong>⚠️ ${missingCount} z ${mainPages.length} głównych stron SEO nie istnieje!</strong>`;
                html += '</div>';
            } else {
                html += '<div class="status success">';
                html += '<strong>✅ Wszystkie główne strony SEO istnieją!</strong>';
                html += '</div>';
            }
            
            resultsDiv.innerHTML = html;
        }

        function generateMissingFiles() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="status warning">
                    <h3>🔧 Generowanie brakujących plików</h3>
                    <p>Aby wygenerować brakujące pliki, użyj następujących komend w terminalu:</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace;">
                        <strong>Dla głównych stron SEO:</strong><br>
                        echo "Tworzenie o-nas.html..." && cp o-nas.html o-nas-backup.html<br>
                        echo "Tworzenie kontakt.html..." && cp kontakt.html kontakt-backup.html<br>
                        echo "Tworzenie polityka-prywatnosci.html..." && cp polityka-prywatnosci.html polityka-backup.html<br>
                        echo "Tworzenie regulamin.html..." && cp regulamin.html regulamin-backup.html<br><br>
                        
                        <strong>Dla kalkulatorów:</strong><br>
                        # Skopiuj istniejący kalkulator jako szablon<br>
                        cp kalkulator-raty-kredytu-hipotecznego.html szablon-kalkulator.html
                    </div>
                    <p><strong>Uwaga:</strong> Po utworzeniu plików, pamiętaj o dostosowaniu tytułów, opisów i treści dla każdej strony.</p>
                </div>
            `;
        }

        // Automatyczne sprawdzenie przy załadowaniu strony
        window.onload = function() {
            checkMainPages();
        };
    </script>
</body>
</html>
