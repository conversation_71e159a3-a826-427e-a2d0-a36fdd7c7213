<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 Error Fixer - <PERSON><PERSON>wa błędów 404</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        h1 { color: #333; text-align: center; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .results { margin-top: 20px; }
        .file-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .link-item { margin: 5px 0; padding: 8px; border-radius: 3px; display: flex; justify-content: space-between; align-items: center; }
        .link-item.exists { background: #d4edda; }
        .link-item.missing { background: #f8d7da; }
        .link-item.created { background: #d1ecf1; }
        .create-btn { background: #28a745; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px; }
        .create-btn:hover { background: #218838; }
        .progress { width: 100%; background: #f0f0f0; border-radius: 5px; margin: 10px 0; }
        .progress-bar { height: 20px; background: #007bff; border-radius: 5px; text-align: center; line-height: 20px; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 404 Error Fixer - Naprawa błędów 404</h1>
        
        <div class="status info">
            <strong>ℹ️ Informacja:</strong> To narzędzie automatycznie sprawdza i naprawia błędy 404 na stronie.
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="scanAllLinks()">🔍 Skanuj wszystkie linki</button>
            <button class="btn btn-success" onclick="fixAllMissing()">🔧 Napraw wszystkie błędy</button>
            <button class="btn btn-danger" onclick="generateReport()">📊 Generuj raport</button>
        </div>
        
        <div id="progress-container" style="display: none;">
            <div class="progress">
                <div id="progress-bar" class="progress-bar" style="width: 0%;">0%</div>
            </div>
        </div>
        
        <div id="results" class="results"></div>
    </div>

    <script>
        // Lista wszystkich linków z index.html i innych stron
        const allLinksFromIndex = [
            // Główne kalkulatory z index.html
            'kalkulator-raty-kredytu-hipotecznego.html',
            'kalkulator-zdolnosci-kredytowej.html',
            'kalkulator-kosztow-kredytu-hipotecznego.html',
            'kalkulator-nadplaty-kredytu-hipotecznego.html',
            
            // Kalkulatory bankowe z index.html
            'pko-kalkulator-kredytu-hipotecznego.html',
            'ing-kalkulator-kredytu-hipotecznego.html',
            'mbank-kalkulator-kredytu-hipotecznego.html',
            'pekao-kalkulator-kredytu-hipotecznego.html',
            'santander-kalkulator-kredytu-hipotecznego.html',
            'alior-bank-kalkulator-kredytu-hipotecznego.html',
            
            // Dodatkowe kalkulatory z sidebar
            'kalkulator-nadplacania-kredytu-hipotecznego.html',
            'kalkulator-refinansowania-kredytu-hipotecznego.html',
            'szczegolowy-kalkulator-kredytu-hipotecznego.html',
            'kalkulator-odsetek-kredytu-hipotecznego.html',
            'wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html',
            'kalkulator-nadplaty-kredytu-hipotecznego-pko-bp.html',
            
            // Strony SEO
            'o-nas.html',
            'kontakt.html',
            'polityka-prywatnosci.html',
            'regulamin.html'
        ];

        // Mapowanie istniejących plików do docelowych nazw
        const fileMapping = {
            'kalkulator-raty-kredytu-hipotecznego.html': 'kalkulator-raty-kredytu-hipotecznego.html',
            'kalkulator-zdolnosci-kredytowej.html': 'kalkulator-zdolnosci-kredytowej.html',
            'kalkulator-kosztow-kredytu-hipotecznego.html': 'kalkulator-kosztow-kredytu-hipotecznego.html',
            'kalkulator-nadplaty-kredytu-hipotecznego.html': 'kalkulator-nadplaty-kredytu-hipotecznego.html',
            'pko-kalkulator-kredytu-hipotecznego.html': 'pko-kalkulator-kredytu-hipotecznego.html',
            'ing-kalkulator-kredytu-hipotecznego.html': 'ing-kalkulator-kredytu-hipotecznego.html',
            'mbank-kalkulator-kredytu-hipotecznego.html': 'mbank-kalkulator-kredytu-hipotecznego.html',
            'pekao-kalkulator-kredytu-hipotecznego.html': 'pekao-kalkulator-kredytu-hipotecznego.html',
            'santander-kalkulator-kredytu-hipotecznego.html': 'santander-kalkulator-kredytu-hipotecznego.html',
            'alior-bank-kalkulator-kredytu-hipotecznego.html': 'alior-bank-kalkulator-kredytu-hipotecznego.html'
        };

        let scanResults = {
            existing: [],
            missing: [],
            total: 0
        };

        async function checkFileExists(filename) {
            try {
                const response = await fetch(filename, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        function updateProgress(current, total) {
            const percentage = Math.round((current / total) * 100);
            const progressBar = document.getElementById('progress-bar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        async function scanAllLinks() {
            const resultsDiv = document.getElementById('results');
            const progressContainer = document.getElementById('progress-container');
            
            progressContainer.style.display = 'block';
            resultsDiv.innerHTML = '<div class="status info">🔄 Skanowanie linków...</div>';
            
            scanResults = { existing: [], missing: [], total: allLinksFromIndex.length };
            
            for (let i = 0; i < allLinksFromIndex.length; i++) {
                const link = allLinksFromIndex[i];
                updateProgress(i + 1, allLinksFromIndex.length);
                
                const exists = await checkFileExists(link);
                if (exists) {
                    scanResults.existing.push(link);
                } else {
                    scanResults.missing.push(link);
                }
                
                // Małe opóźnienie dla lepszego UX
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            displayResults();
            progressContainer.style.display = 'none';
        }

        function displayResults() {
            const resultsDiv = document.getElementById('results');
            
            let html = '<div class="file-section">';
            html += `<h3>✅ Istniejące pliki (${scanResults.existing.length})</h3>`;
            scanResults.existing.forEach(file => {
                html += `<div class="link-item exists">
                    <span>✅ ${file}</span>
                    <span style="color: #28a745; font-weight: bold;">OK</span>
                </div>`;
            });
            html += '</div>';
            
            html += '<div class="file-section">';
            html += `<h3>❌ Brakujące pliki (${scanResults.missing.length})</h3>`;
            scanResults.missing.forEach(file => {
                html += `<div class="link-item missing">
                    <span>❌ ${file}</span>
                    <button class="create-btn" onclick="createMissingFile('${file}')">Utwórz</button>
                </div>`;
            });
            html += '</div>';
            
            if (scanResults.missing.length > 0) {
                html += '<div class="status error">';
                html += `<strong>⚠️ Znaleziono ${scanResults.missing.length} brakujących plików!</strong><br>`;
                html += 'Te linki powodują błędy 404. Kliknij "Napraw wszystkie błędy" aby je automatycznie utworzyć.';
                html += '</div>';
            } else {
                html += '<div class="status success">';
                html += '<strong>🎉 Wszystkie linki działają poprawnie!</strong>';
                html += '</div>';
            }
            
            resultsDiv.innerHTML = html;
        }

        async function createMissingFile(filename) {
            try {
                // Znajdź najbardziej podobny istniejący plik jako szablon
                let templateFile = findBestTemplate(filename);
                
                if (!templateFile) {
                    // Użyj podstawowego szablonu
                    templateFile = 'o-nas.html'; // Jako fallback
                }
                
                // Pobierz zawartość szablonu
                const response = await fetch(templateFile);
                if (!response.ok) {
                    throw new Error('Nie można pobrać szablonu');
                }
                
                let content = await response.text();
                
                // Dostosuj zawartość do nowego pliku
                content = customizeContent(content, filename);
                
                // Symulacja utworzenia pliku (w rzeczywistości wymagałoby to serwera)
                console.log(`Utworzono plik: ${filename}`);
                console.log('Zawartość:', content.substring(0, 200) + '...');
                
                // Aktualizuj UI
                const linkItem = document.querySelector(`[onclick="createMissingFile('${filename}')"]`).parentElement;
                linkItem.className = 'link-item created';
                linkItem.innerHTML = `
                    <span>✅ ${filename}</span>
                    <span style="color: #17a2b8; font-weight: bold;">UTWORZONO</span>
                `;
                
                // Aktualizuj wyniki
                scanResults.missing = scanResults.missing.filter(f => f !== filename);
                scanResults.existing.push(filename);
                
                return true;
            } catch (error) {
                console.error('Błąd podczas tworzenia pliku:', error);
                alert(`Błąd podczas tworzenia pliku ${filename}: ${error.message}`);
                return false;
            }
        }

        function findBestTemplate(filename) {
            // Logika znajdowania najlepszego szablonu na podstawie nazwy pliku
            if (filename.includes('kalkulator')) {
                // Dla kalkulatorów użyj istniejącego kalkulatora jako szablonu
                const calculatorTemplates = [
                    'kalkulator-raty-kredytu-hipotecznego.html',
                    'kalkulator-zdolnosci-kredytowej.html',
                    'ing-kalkulator-kredytu-hipotecznego.html'
                ];
                return calculatorTemplates.find(t => scanResults.existing.includes(t));
            }
            
            if (filename.includes('pko')) return 'pko-kalkulator-kredytu-hipotecznego.html';
            if (filename.includes('ing')) return 'ing-kalkulator-kredytu-hipotecznego.html';
            if (filename.includes('mbank')) return 'mbank-kalkulator-kredytu-hipotecznego.html';
            if (filename.includes('santander')) return 'santander-kalkulator-kredytu-hipotecznego.html';
            
            // Domyślny szablon
            return 'o-nas.html';
        }

        function customizeContent(content, filename) {
            // Dostosuj tytuł
            const title = generateTitle(filename);
            content = content.replace(/<title>.*?<\/title>/i, `<title>${title}</title>`);
            
            // Dostosuj meta description
            const description = generateDescription(filename);
            content = content.replace(/content="[^"]*"/i, `content="${description}"`);
            
            // Dostosuj główny nagłówek
            const h1 = generateH1(filename);
            content = content.replace(/<h1>.*?<\/h1>/i, `<h1>${h1}</h1>`);
            
            return content;
        }

        function generateTitle(filename) {
            const titles = {
                'kalkulator-kosztow-kredytu-hipotecznego.html': 'Kalkulator Kosztów Kredytu Hipotecznego - Oblicz Całkowite Koszty',
                'kalkulator-refinansowania-kredytu-hipotecznego.html': 'Kalkulator Refinansowania Kredytu Hipotecznego - Sprawdź Opłacalność',
                'szczegolowy-kalkulator-kredytu-hipotecznego.html': 'Szczegółowy Kalkulator Kredytu Hipotecznego - Pełna Analiza',
                'kalkulator-odsetek-kredytu-hipotecznego.html': 'Kalkulator Odsetek Kredytu Hipotecznego - Oblicz Odsetki',
                'wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html': 'Kalkulator Wcześniejszej Spłaty Kredytu Hipotecznego'
            };
            
            return titles[filename] || `Kalkulator Kredytu Hipotecznego - ${filename.replace('.html', '').replace(/-/g, ' ')}`;
        }

        function generateDescription(filename) {
            const descriptions = {
                'kalkulator-kosztow-kredytu-hipotecznego.html': 'Oblicz całkowite koszty kredytu hipotecznego wraz z odsetkami, prowizjami i opłatami dodatkowymi.',
                'kalkulator-refinansowania-kredytu-hipotecznego.html': 'Sprawdź czy refinansowanie kredytu hipotecznego będzie dla Ciebie opłacalne.',
                'szczegolowy-kalkulator-kredytu-hipotecznego.html': 'Zaawansowany kalkulator kredytu hipotecznego z pełnym harmonogramem spłat.',
                'kalkulator-odsetek-kredytu-hipotecznego.html': 'Oblicz wysokość odsetek od kredytu hipotecznego w różnych scenariuszach.',
                'wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html': 'Oblicz korzyści z wcześniejszej spłaty kredytu hipotecznego.'
            };
            
            return descriptions[filename] || `Profesjonalny kalkulator kredytu hipotecznego - ${filename.replace('.html', '').replace(/-/g, ' ')}.`;
        }

        function generateH1(filename) {
            const h1s = {
                'kalkulator-kosztow-kredytu-hipotecznego.html': '💰 Kalkulator Kosztów Kredytu Hipotecznego',
                'kalkulator-refinansowania-kredytu-hipotecznego.html': '🔄 Kalkulator Refinansowania Kredytu',
                'szczegolowy-kalkulator-kredytu-hipotecznego.html': '📊 Szczegółowy Kalkulator Kredytu',
                'kalkulator-odsetek-kredytu-hipotecznego.html': '💹 Kalkulator Odsetek Kredytu',
                'wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html': '⏰ Kalkulator Wcześniejszej Spłaty'
            };
            
            return h1s[filename] || `🏠 ${filename.replace('.html', '').replace(/-/g, ' ')}`;
        }

        async function fixAllMissing() {
            if (scanResults.missing.length === 0) {
                alert('Nie ma plików do naprawy!');
                return;
            }
            
            const progressContainer = document.getElementById('progress-container');
            progressContainer.style.display = 'block';
            
            let fixed = 0;
            for (let i = 0; i < scanResults.missing.length; i++) {
                const file = scanResults.missing[i];
                updateProgress(i + 1, scanResults.missing.length);
                
                const success = await createMissingFile(file);
                if (success) fixed++;
                
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            progressContainer.style.display = 'none';
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="status success">
                <strong>🎉 Naprawiono ${fixed} z ${scanResults.missing.length} plików!</strong>
            </div>`;
        }

        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                total_links: scanResults.total,
                existing_files: scanResults.existing.length,
                missing_files: scanResults.missing.length,
                success_rate: Math.round((scanResults.existing.length / scanResults.total) * 100),
                missing_list: scanResults.missing,
                existing_list: scanResults.existing
            };
            
            const reportText = JSON.stringify(report, null, 2);
            
            // Utwórz i pobierz raport
            const blob = new Blob([reportText], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '404-error-report.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('Raport został wygenerowany i pobrany!');
        }

        // Automatyczne skanowanie przy załadowaniu strony
        window.onload = function() {
            scanAllLinks();
        };
    </script>
</body>
</html>
