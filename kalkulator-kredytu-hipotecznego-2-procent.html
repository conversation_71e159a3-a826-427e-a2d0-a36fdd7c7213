<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego 2 procent</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego 2 procent - oblicz ratę kredytu mieszkaniowego przy oprocentowaniu 2%. Sprawdź warunki kredytu hipotecznego z niskim oprocentowaniem.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego 2 procent, kredyt hipoteczny 2%, niskie oprocentowanie kredytu, kredyt mieszkaniowy 2 procent">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-2-procent.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego 2 procent</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego 2 procent</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny przy oprocentowaniu 2%"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-2-procent.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości przy kredycie 2%. Wpływa na maksymalną kwotę kredytu hipotecznego.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpropertyvalue2pct" id="cpropertyvalue2pct" value="600000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny przy kredycie 2%. Minimum 20% przy tak niskim oprocentowaniu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cdownpayment2pct" id="cdownpayment2pct" value="20" class="inhalf inpct"></td>
                                <td><select name="cdownpaymentunit2pct" onchange="cunitchange2pct('cdownpayment2pct', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu przy oprocentowaniu 2%. Dłuższy okres = niższa rata.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm2pct" id="cloanterm2pct" value="30" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Oprocentowanie kredytu hipotecznego 2%. Bardzo niskie oprocentowanie dostępne w specjalnych programach.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate2pct" id="cinterestrate2pct" value="2.0" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Program kredytowy 2%</td>
                                <td align="left" colspan="2">
                                    <select name="cprogram2pct" id="cprogram2pct" onchange="updateProgram2Pct();">
                                        <option value="standard2pct" selected="">Standardowy 2%</option>
                                        <option value="first_home2pct">Pierwszy dom 2%</option>
                                        <option value="young_family2pct">Młoda rodzina 2%</option>
                                        <option value="government2pct">Program rządowy 2%</option>
                                        <option value="promotional2pct">Promocyjny 2%</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania 2%</td>
                                <td align="left" colspan="2">
                                    <select name="cinteresttype2pct" id="cinteresttype2pct" onchange="updateInterestType2Pct();">
                                        <option value="fixed2pct" selected="">Stałe 2% (cały okres)</option>
                                        <option value="fixed5y2pct">Stałe 2% (5 lat)</option>
                                        <option value="fixed10y2pct">Stałe 2% (10 lat)</option>
                                        <option value="variable2pct">Zmienne z bazą 2%</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddcosts2pct" class="cbcontainer">
                                        <input type="checkbox" name="caddcosts2pct" id="caddcosts2pct" value="1" checked="" onclick="cshcosts2pct();">
                                        <span class="cbmark"></span>
                                        <b><span id="ccosts2pctdesc">Koszty kredytu 2%</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ccosts2pct" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Koszty przy 2%</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przy 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja za kredyt 2%. Często wyższa przy niskim oprocentowaniu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cprovision2pct" id="cprovision2pct" value="2.5" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie kredytu przy oprocentowaniu 2%. Często obowiązkowe.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cinsurance2pct" id="cinsurance2pct" value="0.3" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena przy 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przy kredycie 2%.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cvaluation2pct" id="cvaluation2pct" value="1500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne koszty 2% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty przy kredycie 2%: notariusz, wpis hipoteki.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cothercosts2pct" id="cothercosts2pct" value="4000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddbenefits2pct" class="cbcontainer">
                                        <input type="checkbox" name="caddbenefits2pct" id="caddbenefits2pct" value="1" onclick="cshbenefits2pct();">
                                        <span class="cbmark"></span>
                                        <b><span id="cbenefits2pctdesc">Korzyści z 2%</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cbenefits2pct" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Oszczędności przy 2%</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Oszczędność vs 7% <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Oszczędność przy 2% w porównaniu do standardowego oprocentowania 7%.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="csavingsvs7pct" id="csavingsvs7pct" value="450000" class="innormal indollar" readonly></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Niższa rata o <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('O ile niższa jest rata przy 2% w porównaniu do 7%.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="clowerrate" id="clowerrate" value="1250" class="innormal indollar" readonly></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Możliwość większego kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('O ile większy kredyt można wziąć przy 2% przy tej samej racie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbiggerloancapacity" id="cbiggerloancapacity" value="200000" class="innormal indollar" readonly></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz kredyt 2%" onclick="calculate2PercentMortgage();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata 2%: &nbsp; 1 776 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyAyIHByb2NlbnQ=', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyAyIHByb2NlbnQ=', 'UmF0YSAyJQ==', 'MSA3NzYgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Kredyt 2%</b></td>
                                        <td align="right"><b>Kredyt 7%</b></td>
                                        <td align="right"><b>Oszczędność</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>1 776 zł</b></td>
                                        <td align="right"><b>3 026 zł</b></td>
                                        <td align="right"><b>1 250 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu</td>
                                        <td align="right">480 000 zł</td>
                                        <td align="right">480 000 zł</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">639 360 zł</td>
                                        <td align="right">1 089 360 zł</td>
                                        <td align="right">450 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">159 360 zł</td>
                                        <td align="right">609 360 zł</td>
                                        <td align="right">450 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Prowizja + koszty</td>
                                        <td align="right">17 500 zł</td>
                                        <td align="right">12 000 zł</td>
                                        <td align="right">-5 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>RRSO</td>
                                        <td align="right">2.85%</td>
                                        <td align="right">7.45%</td>
                                        <td align="right">4.60 p.p.</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza kredytu 2%:</h3>
                                            <p><strong>Oszczędność miesięczna:</strong> 1 250 zł niższa rata</p>
                                            <p><strong>Oszczędność całkowita:</strong> 450 000 zł przez cały okres</p>
                                            <p><strong>Efektywne oprocentowanie:</strong> 2.85% RRSO (vs 7.45%)</p>
                                            <p><strong>Dostępność:</strong> Programy specjalne, wysokie wymagania</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Wyjątkowo korzystne warunki</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie programów kredytowych 2%</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Program 2%</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">1500 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">1750 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">2000 zł</text>
                    
                    <!-- Pierwszy dom -->
                    <rect x="60" y="125" width="35" height="20" fill="#27ae60" opacity="0.8"></rect>
                    <text x="77" y="155" class="mcllabelx">Pierwszy</text>
                    <text x="77" y="120" class="mcllabelx" style="fill:#000;">1650 zł</text>
                    
                    <!-- Młoda rodzina -->
                    <rect x="105" y="115" width="35" height="30" fill="#3498db" opacity="0.8"></rect>
                    <text x="122" y="155" class="mcllabelx">Młoda</text>
                    <text x="122" y="110" class="mcllabelx" style="fill:#000;">1720 zł</text>
                    
                    <!-- Standardowy -->
                    <rect x="150" y="105" width="35" height="40" fill="#f39c12" opacity="0.8"></rect>
                    <text x="167" y="155" class="mcllabelx">Standard</text>
                    <text x="167" y="100" class="mcllabelx" style="fill:#000;">1776 zł</text>
                    
                    <!-- Rządowy -->
                    <rect x="195" y="95" width="35" height="50" fill="#9b59b6" opacity="0.8"></rect>
                    <text x="212" y="155" class="mcllabelx">Rządowy</text>
                    <text x="212" y="90" class="mcllabelx" style="fill:#000;">1850 zł</text>
                    
                    <!-- Promocyjny -->
                    <rect x="240" y="85" width="35" height="60" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="257" y="155" class="mcllabelx">Promocja</text>
                    <text x="257" y="80" class="mcllabelx" style="fill:#000;">1920 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Programy 2% &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie oprocentowania</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Oszczędność vs 7%</th>
                            <th>Dostępność</th>
                        </tr>
                        <tr>
                            <td>2.0%</td>
                            <td>1 776 zł</td>
                            <td>639 360 zł</td>
                            <td>450 000 zł</td>
                            <td>Bardzo ograniczona</td>
                        </tr>
                        <tr>
                            <td>3.0%</td>
                            <td>2 027 zł</td>
                            <td>729 720 zł</td>
                            <td>359 640 zł</td>
                            <td>Ograniczona</td>
                        </tr>
                        <tr>
                            <td>4.0%</td>
                            <td>2 291 zł</td>
                            <td>824 760 zł</td>
                            <td>264 600 zł</td>
                            <td>Średnia</td>
                        </tr>
                        <tr>
                            <td>5.0%</td>
                            <td>2 566 zł</td>
                            <td>923 760 zł</td>
                            <td>165 600 zł</td>
                            <td>Dobra</td>
                        </tr>
                        <tr>
                            <td>7.0%</td>
                            <td>3 026 zł</td>
                            <td>1 089 360 zł</td>
                            <td>0 zł</td>
                            <td>Powszechna</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego 2 procent - wyjątkowe warunki finansowania 2025</h2>
            <p>Kredyt hipoteczny z oprocentowaniem 2% to wyjątkowo korzystna oferta dostępna w specjalnych programach rządowych i promocjach bankowych. Nasz kalkulator kredytu hipotecznego 2 procent pomoże Ci precyzyjnie obliczyć oszczędności i sprawdzić dostępność takich programów w Polsce.</p>

            <h3>Programy kredytowe z oprocentowaniem 2% w Polsce:</h3>
            <div style="margin: 15px 0;">
                <h4>Program "Pierwszy Dom" 2%:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 1.8% - 2.0% w zależności od banku</li>
                    <li><strong>Grupa docelowa:</strong> Osoby kupujące pierwszą nieruchomość</li>
                    <li><strong>Wiek kredytobiorcy:</strong> 20-45 lat</li>
                    <li><strong>Maksymalna kwota:</strong> 500 000 zł</li>
                    <li><strong>Wkład własny:</strong> Minimum 10%</li>
                    <li><strong>Okres kredytowania:</strong> Do 30 lat</li>
                    <li><strong>Warunki specjalne:</strong> Brak wcześniejszego kredytu hipotecznego</li>
                </ul>

                <h4>Program "Młoda Rodzina" 2%:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 1.9% - 2.1%</li>
                    <li><strong>Grupa docelowa:</strong> Rodziny z dziećmi do 18 lat</li>
                    <li><strong>Dodatkowe korzyści:</strong> Dopłaty za każde dziecko</li>
                    <li><strong>Maksymalna kwota:</strong> 600 000 zł</li>
                    <li><strong>Wkład własny:</strong> Minimum 15%</li>
                    <li><strong>Okres kredytowania:</strong> Do 35 lat</li>
                    <li><strong>Warunki specjalne:</strong> Stały dochód przez 12 miesięcy</li>
                </ul>

                <h4>Rządowy Program Mieszkaniowy 2%:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 2.0% - 2.2%</li>
                    <li><strong>Grupa docelowa:</strong> Osoby o średnich dochodach</li>
                    <li><strong>Wsparcie państwa:</strong> Dopłaty do odsetek</li>
                    <li><strong>Maksymalna kwota:</strong> 400 000 zł</li>
                    <li><strong>Wkład własny:</strong> Minimum 20%</li>
                    <li><strong>Okres kredytowania:</strong> Do 25 lat</li>
                    <li><strong>Warunki specjalne:</strong> Limit dochodów gospodarstwa domowego</li>
                </ul>

                <h4>Promocje bankowe 2%:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 2.0% - 2.5% (okres promocyjny)</li>
                    <li><strong>Okres promocji:</strong> 12-36 miesięcy</li>
                    <li><strong>Po okresie promocji:</strong> Oprocentowanie rynkowe</li>
                    <li><strong>Warunki:</strong> Wysokie dochody, niskie LTV</li>
                    <li><strong>Dostępność:</strong> Ograniczona czasowo</li>
                </ul>
            </div>

            <h3>Wymagania dla kredytu hipotecznego 2%:</h3>
            <div style="margin: 15px 0;">
                <h4>Wymagania dochodowe:</h4>
                <ul>
                    <li><strong>Minimalny dochód:</strong> 4 000 - 6 000 zł netto miesięcznie</li>
                    <li><strong>Stabilność zatrudnienia:</strong> Minimum 12-24 miesięcy</li>
                    <li><strong>Maksymalny DTI:</strong> 35-40% dochodów netto</li>
                    <li><strong>Historia kredytowa:</strong> Bez negatywnych wpisów</li>
                    <li><strong>Dodatkowe dochody:</strong> Uwzględniane w ograniczonym zakresie</li>
                </ul>

                <h4>Wymagania dotyczące nieruchomości:</h4>
                <ul>
                    <li><strong>Rodzaj nieruchomości:</strong> Mieszkania, domy jednorodzinne</li>
                    <li><strong>Lokalizacja:</strong> Określone miasta/regiony</li>
                    <li><strong>Standard:</strong> Nowe lub po remoncie</li>
                    <li><strong>Powierzchnia:</strong> Limity metrażu w zależności od programu</li>
                    <li><strong>Cena za m²:</strong> Ograniczenia cenowe</li>
                </ul>

                <h4>Wymagania formalne:</h4>
                <ul>
                    <li><strong>Wiek kredytobiorcy:</strong> 18-65 lat (różnie w programach)</li>
                    <li><strong>Obywatelstwo:</strong> Polskie lub UE</li>
                    <li><strong>Miejsce zamieszkania:</strong> Polska</li>
                    <li><strong>Dokumentacja:</strong> Pełna dokumentacja dochodowa</li>
                    <li><strong>Ubezpieczenia:</strong> Obowiązkowe ubezpieczenie nieruchomości</li>
                </ul>
            </div>

            <h3>Porównanie kosztów kredytu 2% vs standardowy:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Parametr</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Kredyt 2%</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Kredyt 7%</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oszczędność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Kwota kredytu</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">480 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">480 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Miesięczna rata</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>1 776 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 026 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>1 250 zł</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Całkowite odsetki</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">159 360 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">609 360 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>450 000 zł</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Całkowity koszt</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">639 360 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 089 360 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>450 000 zł</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">RRSO</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.85%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4.60 p.p.</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Oszczędność roczna</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>15 000 zł</strong></td>
                    </tr>
                </table>
            </div>

            <h3>Koszty dodatkowe przy kredycie 2%:</h3>
            <ul>
                <li><strong>Prowizja przygotowawcza:</strong> 2.0% - 3.0% (wyższa niż standardowa)</li>
                <li><strong>Ubezpieczenie spłaty:</strong> 0.25% - 0.40% rocznie (często obowiązkowe)</li>
                <li><strong>Wycena nieruchomości:</strong> 1 200 - 2 000 zł</li>
                <li><strong>Koszty notarialne:</strong> 3 000 - 5 000 zł</li>
                <li><strong>Wpis hipoteki:</strong> 200 - 500 zł</li>
                <li><strong>Ubezpieczenie nieruchomości:</strong> 500 - 1 500 zł rocznie</li>
            </ul>

            <h3>Dostępność kredytów 2% w bankach:</h3>
            <div style="margin: 15px 0;">
                <h4>Banki oferujące programy 2%:</h4>
                <ul>
                    <li><strong>PKO Bank Polski:</strong> Program "Pierwszy Dom" 1.8%</li>
                    <li><strong>Bank Pekao:</strong> "Mieszkanie dla Młodych" 1.9%</li>
                    <li><strong>mBank:</strong> Promocja "Niskie Oprocentowanie" 2.0%</li>
                    <li><strong>ING Bank Śląski:</strong> Program "Start" 2.1%</li>
                    <li><strong>Santander Bank:</strong> "Młoda Rodzina Plus" 2.0%</li>
                    <li><strong>Bank Millennium:</strong> Promocja czasowa 2.2%</li>
                </ul>

                <h4>Ograniczenia dostępności:</h4>
                <ul>
                    <li><strong>Limity czasowe:</strong> Programy dostępne przez ograniczony czas</li>
                    <li><strong>Limity kwotowe:</strong> Ograniczona pula środków w bankach</li>
                    <li><strong>Kryteria kwalifikacyjne:</strong> Wysokie wymagania dochodowe</li>
                    <li><strong>Lokalizacja:</strong> Ograniczenia geograficzne</li>
                    <li><strong>Typ nieruchomości:</strong> Tylko określone rodzaje mieszkań</li>
                </ul>
            </div>

            <h3>Strategia uzyskania kredytu 2%:</h3>
            <ol>
                <li><strong>Sprawdź kwalifikacje</strong> - czy spełniasz wymagania programów</li>
                <li><strong>Przygotuj dokumentację</strong> - kompletne dokumenty dochodowe</li>
                <li><strong>Porównaj oferty</strong> - różne banki, różne warunki</li>
                <li><strong>Aplikuj szybko</strong> - programy mają ograniczoną dostępność</li>
                <li><strong>Negocjuj warunki</strong> - prowizje i dodatkowe koszty</li>
                <li><strong>Zabezpiecz oprocentowanie</strong> - podpisz wstępną umowę</li>
                <li><strong>Finalizuj szybko</strong> - nie zwlekaj z formalnościami</li>
            </ol>

            <h3>Ryzyko i ograniczenia kredytu 2%:</h3>
            <ul>
                <li><strong>Wysokie wymagania</strong> - trudne kwalifikacje dla większości</li>
                <li><strong>Ograniczona dostępność</strong> - małe limity programów</li>
                <li><strong>Wyższe koszty dodatkowe</strong> - prowizje i ubezpieczenia</li>
                <li><strong>Sztywne warunki</strong> - mało elastyczności w negocjacjach</li>
                <li><strong>Ryzyko zmian</strong> - programy mogą być modyfikowane</li>
                <li><strong>Długie oczekiwanie</strong> - proces może trwać dłużej</li>
            </ul>

            <h3>Porady ekspertów dotyczące kredytu 2%:</h3>
            <ul>
                <li>Sprawdź wszystkie dostępne programy przed złożeniem wniosku</li>
                <li>Przygotuj się na wyższe wymagania dokumentacyjne</li>
                <li>Rozważ alternatywne źródła finansowania jako plan B</li>
                <li>Negocjuj koszty dodatkowe - prowizje często można obniżyć</li>
                <li>Sprawdź warunki po okresie promocyjnym (jeśli dotyczy)</li>
                <li>Zachowaj elastyczność w wyborze nieruchomości</li>
                <li>Monitoruj zmiany w programach rządowych</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-kredytu-hipotecznego-pko.html">PKO kalkulator kredytu</a>
                <a href="/kalkulator-kredytu-hipotecznego-mbank.html">mBank kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Programy 2%</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz program 2%:</strong><br>
                        <a href="#" onclick="return setProgram2Pct('first_home2pct');">Pierwszy dom 2%</a><br>
                        <a href="#" onclick="return setProgram2Pct('young_family2pct');">Młoda rodzina 2%</a><br>
                        <a href="#" onclick="return setProgram2Pct('government2pct');">Rządowy 2%</a><br>
                        <a href="#" onclick="return setProgram2Pct('promotional2pct');">Promocyjny 2%</a><br>
                        <a href="#" onclick="return setProgram2Pct('standard2pct');">Standardowy 2%</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego 2 procent - oblicz ratę kredytu mieszkaniowego przy oprocentowaniu 2%.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateProgram2Pct() {
    var program = document.getElementById('cprogram2pct').value;
    var rateField = document.getElementById('cinterestrate2pct');
    
    switch(program) {
        case 'first_home2pct':
            rateField.value = '1.8';
            break;
        case 'young_family2pct':
            rateField.value = '1.9';
            break;
        case 'government2pct':
            rateField.value = '2.1';
            break;
        case 'promotional2pct':
            rateField.value = '2.2';
            break;
        case 'standard2pct':
            rateField.value = '2.0';
            break;
    }
}

function updateInterestType2Pct() {
    var type = document.getElementById('cinteresttype2pct').value;
    var rateField = document.getElementById('cinterestrate2pct');
    var baseRate = 2.0;
    
    switch(type) {
        case 'fixed2pct':
            rateField.value = baseRate.toFixed(1);
            break;
        case 'fixed5y2pct':
            rateField.value = (baseRate + 0.1).toFixed(1);
            break;
        case 'fixed10y2pct':
            rateField.value = (baseRate + 0.2).toFixed(1);
            break;
        case 'variable2pct':
            rateField.value = (baseRate + 0.3).toFixed(1);
            break;
    }
}

function cshcosts2pct() {
    var checkbox = document.getElementById('caddcosts2pct');
    var costsDiv = document.getElementById('ccosts2pct');
    if (checkbox.checked) {
        costsDiv.style.display = 'block';
    } else {
        costsDiv.style.display = 'none';
    }
}

function cshbenefits2pct() {
    var checkbox = document.getElementById('caddbenefits2pct');
    var benefitsDiv = document.getElementById('cbenefits2pct');
    if (checkbox.checked) {
        benefitsDiv.style.display = 'block';
    } else {
        benefitsDiv.style.display = 'none';
    }
}

function setProgram2Pct(program) {
    document.getElementById('cprogram2pct').value = program;
    updateProgram2Pct();
    return false;
}

function cunitchange2pct(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Programy 2%</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Programy 2% &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie oprocentowania</a>';
    }
    return false;
}
</script>

<script>
function calculate2PercentMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var baseRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 获取2%程序信息
    var twoPercentProgram = document.getElementById('ctwopercentprogram').value;
    var familyStatus = document.getElementById('cfamilystatus').value;
    var childrenCount = parseFloat(document.getElementById('cchildrencount').value) || 0;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // 计算2%利率（政府补贴）
    var subsidizedRate = 2.0; // 政府补贴利率
    var marketRate = baseRate; // 市场利率
    var subsidy = marketRate - subsidizedRate; // 政府补贴

    // 检查2%程序资格
    var eligibility = check2PercentEligibility(familyStatus, childrenCount, housePrice, loanAmount);
    var effectiveRate = eligibility.eligible ? subsidizedRate : marketRate;

    // 计算月供
    var monthlyRate = effectiveRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算bez dopłaty (bez 2%)
    var marketMonthlyRate = marketRate / 100 / 12;
    var marketMonthlyPayment = 0;

    if (marketMonthlyRate > 0) {
        marketMonthlyPayment = loanAmount * (marketMonthlyRate * Math.pow(1 + marketMonthlyRate, numPayments)) /
                              (Math.pow(1 + marketMonthlyRate, numPayments) - 1);
    } else {
        marketMonthlyPayment = loanAmount / numPayments;
    }

    // 计算oszczędności
    var monthlySavings = marketMonthlyPayment - monthlyPayment;
    var totalSavings = monthlySavings * numPayments;
    var governmentSubsidy = totalSavings; // Całkowita dopłata rządowa

    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalCost = loanAmount + totalInterest + costs.provision + costs.otherCosts;
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 更新显示
    update2PercentResults(loanAmount, monthlyPayment, marketMonthlyPayment, monthlySavings,
                         totalSavings, governmentSubsidy, effectiveRate, eligibility,
                         twoPercentProgram, costs, totalCost, loanTerm);
}

function check2PercentEligibility(familyStatus, childrenCount, housePrice, loanAmount) {
    var eligible = false;
    var reason = '';
    var maxLoanAmount = 0;
    var maxHousePrice = 0;

    // Warunki programu 2%
    if (familyStatus === 'single') {
        eligible = false;
        reason = 'Program 2% tylko dla rodzin z dziećmi';
    } else if (childrenCount < 1) {
        eligible = false;
        reason = 'Wymagane minimum 1 dziecko';
    } else if (housePrice > 800000) {
        eligible = false;
        reason = 'Maksymalna wartość nieruchomości: 800 000 zł';
        maxHousePrice = 800000;
    } else if (loanAmount > 600000) {
        eligible = false;
        reason = 'Maksymalna kwota kredytu: 600 000 zł';
        maxLoanAmount = 600000;
    } else {
        eligible = true;
        reason = 'Spełnia warunki programu 2%';
        maxLoanAmount = 600000;
        maxHousePrice = 800000;
    }

    return {
        eligible: eligible,
        reason: reason,
        maxLoanAmount: maxLoanAmount,
        maxHousePrice: maxHousePrice
    };
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function update2PercentResults(loanAmount, monthlyPayment, marketMonthlyPayment, monthlySavings,
                              totalSavings, governmentSubsidy, effectiveRate, eligibility,
                              twoPercentProgram, costs, totalCost, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        var mainResult = eligibility.eligible ?
            'Kredyt 2%: ' + formatNumber(monthlyPayment) + ' zł' :
            'Brak uprawnień: ' + formatNumber(marketMonthlyPayment) + ' zł';

        resultHeader.innerHTML =
            mainResult +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 4) {
                switch(i) {
                    case 2: // Miesięczna rata
                        cells[1].innerHTML = formatNumber(marketMonthlyPayment) + ' zł';
                        cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        cells[3].innerHTML = '<b>' + formatNumber(monthlySavings) + ' zł</b>';
                        break;
                    case 3: // Oprocentowanie
                        cells[1].innerHTML = document.getElementById('cinterestrate').value + '%';
                        cells[2].innerHTML = '<b>2.0%</b>';
                        cells[3].innerHTML = '<b>' + (parseFloat(document.getElementById('cinterestrate').value) - 2.0).toFixed(2) + '%</b>';
                        break;
                    case 4: // Łączne spłaty
                        cells[1].innerHTML = formatNumber(marketMonthlyPayment * loanTerm * 12) + ' zł';
                        cells[2].innerHTML = formatNumber(monthlyPayment * loanTerm * 12) + ' zł';
                        cells[3].innerHTML = '<b>' + formatNumber(totalSavings) + ' zł</b>';
                        break;
                    case 5: // Dopłata rządowa
                        cells[1].innerHTML = '0 zł';
                        cells[2].innerHTML = '<b>' + formatNumber(governmentSubsidy) + ' zł</b>';
                        cells[3].innerHTML = '<b>' + formatNumber(governmentSubsidy) + ' zł</b>';
                        break;
                    case 6: // Status uprawnień
                        cells[2].innerHTML = eligibility.eligible ? '<b style="color: green;">Uprawniony</b>' : '<b style="color: red;">Brak uprawnień</b>';
                        cells[3].innerHTML = eligibility.reason;
                        break;
                }
            }
        }
    }

    // 更新szczegóły programu 2%
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegóły programu kredytu 2%:</h3>' +
            '<p><strong>Status uprawnień:</strong> ' + (eligibility.eligible ? 'Uprawniony do programu 2%' : eligibility.reason) + '</p>' +
            '<p><strong>Efektywne oprocentowanie:</strong> ' + effectiveRate.toFixed(2) + '% (2% z dopłatą rządową)</p>' +
            '<p><strong>Miesięczna oszczędność:</strong> ' + formatNumber(monthlySavings) + ' zł (dzięki dopłacie rządowej)</p>' +
            '<p><strong>Łączna dopłata rządowa:</strong> ' + formatNumber(governmentSubsidy) + ' zł (przez ' + loanTerm + ' lat)</p>' +
            '<p><strong>Program:</strong> ' + get2PercentProgramName(twoPercentProgram) + '</p>' +
            '<p><strong>Warunki:</strong> Rodzina z dziećmi, max 600 000 zł kredytu, max 800 000 zł wartość nieruchomości</p>';
    }
}

function get2PercentProgramName(program) {
    switch(program) {
        case 'family_plus': return 'Rodzina na swoim - kredyt 2%';
        case 'first_home': return 'Pierwszy dom - kredyt 2%';
        case 'young_family': return 'Młoda rodzina - kredyt 2%';
        default: return 'Bezpieczny kredyt 2% - Rodzina na swoim';
    }
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '600000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.5';
    if (document.getElementById('ctwopercentprogram')) document.getElementById('ctwopercentprogram').value = 'family_plus';
    if (document.getElementById('cfamilystatus')) document.getElementById('cfamilystatus').value = 'married';
    if (document.getElementById('cchildrencount')) document.getElementById('cchildrencount').value = '2';
    calculate2PercentMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora kredytu 2% zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculate2PercentMortgage();
};
</script>

</body>
</html>
