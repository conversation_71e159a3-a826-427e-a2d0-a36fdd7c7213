<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator zdolności kredytu hipotecznego</title>
    <meta name="description" content="Kalkulator zdolności kredytu hipotecznego - sprawdź swoją zdolność kredytową, oblicz maksymalną kwotę kredytu mieszkaniowego i sprawdź wymagania banków.">
    <meta name="keywords" content="kalkulator zdolności kredytu hipotecznego, zdolno<PERSON><PERSON> kredytowa, maksymalny kredyt, wymagania bankowe, dti ratio">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator zdolności kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator zdolności kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Sprawdź swoją zdolność kredytową i maksymalną kwotę kredytu"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-zdolnosci-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Miesięczne dochody netto <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Suma wszystkich miesięcznych dochodów netto (po odliczeniu podatków i składek). Uwzględnij pensję, premie, dodatki.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cnetincome" id="cnetincome" value="8500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Dochody współmałżonka <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczne dochody netto współmałżonka lub partnera, jeśli będzie współkredytobiorcą.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cspouseincome" id="cspouseincome" value="6000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Miesięczne wydatki <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Stałe miesięczne wydatki: raty innych kredytów, alimenty, ubezpieczenia, inne zobowiązania finansowe.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyexpenses" id="cmonthlyexpenses" value="2800" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Liczba osób w rodzinie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita liczba osób w gospodarstwie domowym, włączając kredytobiorców i dzieci na utrzymaniu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cfamilysize" id="cfamilysize" value="3" class="inhalf"></td>
                                <td>osób</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Planowany okres spłaty kredytu hipotecznego w latach. Dłuższy okres zwiększa zdolność kredytową.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Przewidywane oprocentowanie kredytu hipotecznego. Sprawdź aktualne stawki w bankach.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.5" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ zatrudnienia</td>
                                <td align="left" colspan="2">
                                    <select name="cemploymenttype" id="cemploymenttype" onchange="updateCreditCapacity();">
                                        <option value="permanent" selected="">Umowa o pracę na czas nieokreślony</option>
                                        <option value="temporary">Umowa o pracę na czas określony</option>
                                        <option value="business">Działalność gospodarcza</option>
                                        <option value="freelance">Umowy zlecenie/o dzieło</option>
                                        <option value="pension">Emerytura/renta</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Wiek kredytobiorcy</td>
                                <td align="right"><input type="text" name="cage" id="cage" value="35" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="cadddetails" class="cbcontainer">
                                        <input type="checkbox" name="cadddetails" id="cadddetails" value="1" checked="" onclick="cshdetails();">
                                        <span class="cbmark"></span>
                                        <b><span id="cdetailsdesc">Szczegółowa analiza zdolności</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cdetails" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Dodatkowe parametry</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Koszt utrzymania na osobę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczny koszt utrzymania jednej osoby według standardów bankowych (zwykle 600-1000 zł).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="clivingcost" id="clivingcost" value="800" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Maksymalny DTI <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Maksymalny wskaźnik DTI (Debt-to-Income) akceptowany przez banki. Zwykle 40-50% dochodów.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmaxdti" id="cmaxdti" value="42" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne kredyty (raty) <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Suma miesięcznych rat wszystkich innych kredytów i zobowiązań finansowych.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cothercredits" id="cothercredits" value="450" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rezerwa bezpieczeństwa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa rezerwa finansowa uwzględniana przez bank jako bufor bezpieczeństwa.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="csafetymargin" id="csafetymargin" value="500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="ccomparebanks" class="cbcontainer">
                                        <input type="checkbox" name="ccomparebanks" id="ccomparebanks" value="1" onclick="cshbanks();">
                                        <span class="cbmark"></span>
                                        <b><span id="cbanksdesc">Porównaj wymagania banków</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cbanks" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">PKO BP - DTI</td>
                                                    <td align="right"><input type="text" name="cpkodti" id="cpkodti" value="40" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">ING - DTI</td>
                                                    <td align="right"><input type="text" name="cingdti" id="cingdti" value="42" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">mBank - DTI</td>
                                                    <td align="right"><input type="text" name="cmbankdti" id="cmbankdti" value="45" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz zdolność kredytową" onclick="calculateCreditCapacity();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Maksymalny kredyt: &nbsp; 685 000 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBaZG9sbm/FmWNpIEtyZWR5dHUgSGlwb3RlY3puZWdv', 0, 'S2Fsa3VsYXRvciBaZG9sbm/FmWNpIEtyZWR5dHUgSGlwb3RlY3puZWdv', 'TWFrc3ltYWxueSBrcmVkeXQ=', 'Njg1IDAwMCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Twoja sytuacja</b></td>
                                        <td align="right"><b>Wynik analizy</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Maksymalny kredyt</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>685 000 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Miesięczna rata</td>
                                        <td align="right">-</td>
                                        <td align="right">5 085 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite dochody</td>
                                        <td align="right">14 500 zł</td>
                                        <td align="right">100%</td>
                                    </tr>
                                    <tr>
                                        <td>Dostępne dochody</td>
                                        <td align="right">8 850 zł</td>
                                        <td align="right">61%</td>
                                    </tr>
                                    <tr>
                                        <td>Wskaźnik DTI</td>
                                        <td align="right">35.1%</td>
                                        <td align="right">✓ Pozytywny</td>
                                    </tr>
                                    <tr>
                                        <td>Koszty utrzymania</td>
                                        <td align="right">2 400 zł</td>
                                        <td align="right">3 osoby</td>
                                    </tr>
                                    <tr>
                                        <td>Inne zobowiązania</td>
                                        <td align="right">3 250 zł</td>
                                        <td align="right">22.4%</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="3" style="padding-top: 15px;">
                                            <h3>Analiza zdolności kredytowej:</h3>
                                            <p><strong>Status:</strong> ✓ Pozytywna zdolność kredytowa</p>
                                            <p><strong>Rekomendacja:</strong> Możesz ubiegać się o kredyt do 685 000 zł</p>
                                            <p><strong>Typ zatrudnienia:</strong> Umowa na czas nieokreślony (najlepsze warunki)</p>
                                            <p><strong>Wiek:</strong> 35 lat (optimalny dla długoterminowego kredytu)</p>
                                            <p><strong>Rezerwa:</strong> 2 765 zł miesięcznie po spłacie raty</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie zdolności w różnych bankach</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Bank</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">500K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">650K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">800K zł</text>
                    
                    <!-- PKO BP -->
                    <rect x="60" y="105" width="40" height="40" fill="#dc143c" opacity="0.8"></rect>
                    <text x="80" y="155" class="mcllabelx">PKO</text>
                    <text x="80" y="100" class="mcllabelx" style="fill:#000;">620K</text>
                    
                    <!-- ING -->
                    <rect x="120" y="95" width="40" height="50" fill="#ff6600" opacity="0.8"></rect>
                    <text x="140" y="155" class="mcllabelx">ING</text>
                    <text x="140" y="90" class="mcllabelx" style="fill:#000;">685K</text>
                    
                    <!-- mBank -->
                    <rect x="180" y="85" width="40" height="60" fill="#00a651" opacity="0.8"></rect>
                    <text x="200" y="155" class="mcllabelx">mBank</text>
                    <text x="200" y="80" class="mcllabelx" style="fill:#000;">750K</text>
                    
                    <!-- Santander -->
                    <rect x="240" y="100" width="40" height="45" fill="#ec0000" opacity="0.8"></rect>
                    <text x="260" y="155" class="mcllabelx">Santander</text>
                    <text x="260" y="95" class="mcllabelx" style="fill:#000;">650K</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Porównanie banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Szczegółowa analiza</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Bank</th>
                            <th>Max DTI</th>
                            <th>Maksymalny kredyt</th>
                            <th>Miesięczna rata</th>
                            <th>Status</th>
                        </tr>
                        <tr>
                            <td>PKO BP</td>
                            <td>40%</td>
                            <td>620 000 zł</td>
                            <td>4 605 zł</td>
                            <td>✓ Pozytywny</td>
                        </tr>
                        <tr>
                            <td>ING</td>
                            <td>42%</td>
                            <td>685 000 zł</td>
                            <td>5 085 zł</td>
                            <td>✓ Pozytywny</td>
                        </tr>
                        <tr>
                            <td>mBank</td>
                            <td>45%</td>
                            <td>750 000 zł</td>
                            <td>5 570 zł</td>
                            <td>✓ Pozytywny</td>
                        </tr>
                        <tr>
                            <td>Santander</td>
                            <td>41%</td>
                            <td>650 000 zł</td>
                            <td>4 825 zł</td>
                            <td>✓ Pozytywny</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator zdolności kredytu hipotecznego - jak sprawdzić swoją zdolność kredytową?</h2>
            <p>Zdolność kredytowa to maksymalna kwota kredytu, jaką bank może Ci udzielić na podstawie analizy Twoich dochodów, wydatków i sytuacji finansowej. Nasz kalkulator zdolności kredytu hipotecznego pomoże Ci precyzyjnie obliczyć, na jaki kredyt mieszkaniowy możesz liczyć w różnych bankach.</p>

            <h3>Jak banki oceniają zdolność kredytową?</h3>
            <div style="margin: 15px 0;">
                <h4>Główne kryteria oceny zdolności kredytowej:</h4>
                <ul>
                    <li><strong>Dochody netto</strong> - suma wszystkich miesięcznych dochodów po odliczeniu podatków</li>
                    <li><strong>Stałość zatrudnienia</strong> - typ umowy i staż pracy</li>
                    <li><strong>Wydatki stałe</strong> - raty innych kredytów, alimenty, ubezpieczenia</li>
                    <li><strong>Koszty utrzymania</strong> - szacowane wydatki na życie dla gospodarstwa domowego</li>
                    <li><strong>Wiek kredytobiorcy</strong> - wpływa na maksymalny okres kredytowania</li>
                    <li><strong>Historia kredytowa</strong> - wpisy w BIK i BIG</li>
                </ul>

                <h4>Wskaźnik DTI (Debt-to-Income):</h4>
                <p>DTI to kluczowy wskaźnik określający stosunek wszystkich miesięcznych zobowiązań do dochodów netto. Różne banki mają różne limity DTI:</p>
                <ul>
                    <li><strong>PKO Bank Polski:</strong> maksymalnie 40% DTI</li>
                    <li><strong>ING Bank Śląski:</strong> maksymalnie 42% DTI</li>
                    <li><strong>mBank:</strong> maksymalnie 45% DTI</li>
                    <li><strong>Santander Bank:</strong> maksymalnie 41% DTI</li>
                    <li><strong>Bank Millennium:</strong> maksymalnie 43% DTI</li>
                </ul>
            </div>

            <h3>Czynniki wpływające na zdolność kredytową:</h3>
            <div style="margin: 15px 0;">
                <h4>Pozytywne czynniki:</h4>
                <ul>
                    <li><strong>Umowa o pracę na czas nieokreślony</strong> - najlepsze warunki</li>
                    <li><strong>Wysokie i stabilne dochody</strong> - zwiększają maksymalną kwotę kredytu</li>
                    <li><strong>Długi staż pracy</strong> - minimum 3-6 miesięcy w obecnym miejscu</li>
                    <li><strong>Brak innych zobowiązań</strong> - więcej środków na spłatę kredytu</li>
                    <li><strong>Współkredytobiorca</strong> - dodatkowe dochody zwiększają zdolność</li>
                    <li><strong>Wyższy wkład własny</strong> - zmniejsza ryzyko dla banku</li>
                    <li><strong>Pozytywna historia kredytowa</strong> - terminowe spłaty w przeszłości</li>
                </ul>

                <h4>Negatywne czynniki:</h4>
                <ul>
                    <li><strong>Umowy czasowe</strong> - ograniczają zdolność kredytową</li>
                    <li><strong>Działalność gospodarcza</strong> - niestabilne dochody</li>
                    <li><strong>Inne kredyty</strong> - zmniejszają dostępne dochody</li>
                    <li><strong>Negatywna historia BIK</strong> - opóźnienia w spłatach</li>
                    <li><strong>Zaawansowany wiek</strong> - ogranicza okres kredytowania</li>
                    <li><strong>Niskie dochody</strong> - mogą być niewystarczające</li>
                </ul>
            </div>

            <h3>Jak zwiększyć swoją zdolność kredytową?</h3>
            <ul>
                <li><strong>Spłać inne kredyty</strong> - zmniejsz miesięczne zobowiązania</li>
                <li><strong>Zwiększ dochody</strong> - dodatkowe źródła zarobków</li>
                <li><strong>Dodaj współkredytobiorcę</strong> - łączenie dochodów</li>
                <li><strong>Wydłuż okres kredytowania</strong> - niższa miesięczna rata</li>
                <li><strong>Zwiększ wkład własny</strong> - mniejsza kwota kredytu</li>
                <li><strong>Popraw historię kredytową</strong> - terminowe spłaty</li>
                <li><strong>Zmień typ zatrudnienia</strong> - umowa na czas nieokreślony</li>
            </ul>

            <h3>Wymagania różnych typów zatrudnienia:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Typ zatrudnienia</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Maksymalny DTI</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wymagany staż</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Dokumenty</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowa na czas nieokreślony</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>42%</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 miesiące</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowa + zaświadczenie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowa na czas określony</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">38%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6 miesięcy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowa + historia zatrudnienia</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Działalność gospodarcza</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">24 miesiące</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">PIT + księgi rachunkowe</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowy zlecenie/dzieło</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">12 miesięcy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umowy + PIT</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Emerytura/renta</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">40%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Decyzja ZUS</td>
                    </tr>
                </table>
            </div>

            <h3>Koszty utrzymania według banków:</h3>
            <p>Banki uwzględniają standardowe koszty utrzymania na osobę w gospodarstwie domowym:</p>
            <ul>
                <li><strong>Dorosły (główny żywiciel):</strong> 800-1000 zł miesięcznie</li>
                <li><strong>Dorosły (współmałżonek):</strong> 600-800 zł miesięcznie</li>
                <li><strong>Dziecko (do 18 lat):</strong> 400-600 zł miesięcznie</li>
                <li><strong>Student (18-26 lat):</strong> 600-800 zł miesięcznie</li>
            </ul>

            <h3>Proces oceny zdolności kredytowej:</h3>
            <ol>
                <li><strong>Wstępna kalkulacja</strong> - użyj naszego kalkulatora</li>
                <li><strong>Złożenie wniosku</strong> - w banku lub online</li>
                <li><strong>Weryfikacja dochodów</strong> - sprawdzenie dokumentów</li>
                <li><strong>Analiza BIK/BIG</strong> - historia kredytowa</li>
                <li><strong>Ocena ryzyka</strong> - analiza bankowa</li>
                <li><strong>Decyzja wstępna</strong> - informacja o zdolności</li>
                <li><strong>Wycena nieruchomości</strong> - ostateczna weryfikacja</li>
                <li><strong>Decyzja końcowa</strong> - zatwierdzenie kredytu</li>
            </ol>

            <h3>Dokumenty potrzebne do oceny zdolności:</h3>
            <div style="margin: 15px 0;">
                <h4>Dokumenty podstawowe:</h4>
                <ul>
                    <li>Wniosek o kredyt hipoteczny</li>
                    <li>Dokument tożsamości (dowód osobisty)</li>
                    <li>Zaświadczenie o dochodach (ostatnie 3 miesiące)</li>
                    <li>Umowa o pracę lub działalność gospodarczą</li>
                    <li>Wyciągi z rachunków bankowych (3 miesiące)</li>
                </ul>

                <h4>Dokumenty dodatkowe:</h4>
                <ul>
                    <li>PIT z ostatniego roku</li>
                    <li>Informacja o innych kredytach</li>
                    <li>Dokumenty współmałżonka (jeśli dotyczy)</li>
                    <li>Dokumenty dotyczące nieruchomości</li>
                </ul>
            </div>

            <h3>Porady ekspertów:</h3>
            <ul>
                <li>Sprawdź swoją zdolność kredytową przed rozpoczęciem poszukiwań nieruchomości</li>
                <li>Porównaj warunki w różnych bankach - DTI może się różnić</li>
                <li>Nie wykorzystuj całej zdolności kredytowej - zostaw rezerwę</li>
                <li>Rozważ dodanie współkredytobiorcy dla zwiększenia zdolności</li>
                <li>Spłać drogie kredyty konsumpcyjne przed wnioskiem o kredyt hipoteczny</li>
                <li>Monitoruj swoją historię kredytową w BIK</li>
                <li>Przygotuj wszystkie dokumenty przed wizytą w banku</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-zdolnosci-kredytowej.html">Kalkulator zdolności kredytowej</a>
                <a href="/szczegolowy-kalkulator-kredytu-hipotecznego.html">Szczegółowy kalkulator</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator kredytu</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
                <a href="/kalkulator-kredytu.html">Kalkulator kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wskaźniki DTI banków</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Maksymalne DTI:</strong><br>
                        PKO BP: <a href="#" onclick="return setDTI('40');">40%</a><br>
                        ING: <a href="#" onclick="return setDTI('42');">42%</a><br>
                        mBank: <a href="#" onclick="return setDTI('45');">45%</a><br>
                        Santander: <a href="#" onclick="return setDTI('41');">41%</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator zdolności kredytu hipotecznego - sprawdź swoją zdolność kredytową i maksymalną kwotę kredytu mieszkaniowego.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function cshdetails() {
    var checkbox = document.getElementById('cadddetails');
    var detailsDiv = document.getElementById('cdetails');
    if (checkbox.checked) {
        detailsDiv.style.display = 'block';
    } else {
        detailsDiv.style.display = 'none';
    }
}

function cshbanks() {
    var checkbox = document.getElementById('ccomparebanks');
    var banksDiv = document.getElementById('cbanks');
    if (checkbox.checked) {
        banksDiv.style.display = 'block';
    } else {
        banksDiv.style.display = 'none';
    }
}

function updateCreditCapacity() {
    var employmentType = document.getElementById('cemploymenttype').value;
    var dtiField = document.getElementById('cmaxdti');
    
    switch(employmentType) {
        case 'permanent':
            dtiField.value = '42';
            break;
        case 'temporary':
            dtiField.value = '38';
            break;
        case 'business':
            dtiField.value = '35';
            break;
        case 'freelance':
            dtiField.value = '30';
            break;
        case 'pension':
            dtiField.value = '40';
            break;
    }
}

function setDTI(dti) {
    document.getElementById('cmaxdti').value = dti;
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Szczegółowa analiza &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Porównanie banków</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Porównanie banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Szczegółowa analiza</a>';
    }
    return false;
}
</script>

<script>
function calculateCreditCapacity() {
    // 获取收入信息
    var monthlyIncome = parseFloat(document.getElementById('cmonthlyincome').value) || 0;
    var additionalIncome = parseFloat(document.getElementById('cadditionalincome').value) || 0;
    var partnerIncome = parseFloat(document.getElementById('cpartnerincome').value) || 0;

    // 获取支出信息
    var monthlyExpenses = parseFloat(document.getElementById('cmonthlyexpenses').value) || 0;
    var existingLoans = parseFloat(document.getElementById('cexistingloans').value) || 0;
    var otherObligations = parseFloat(document.getElementById('cotherobligations').value) || 0;

    // 获取贷款参数
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;

    // 计算总收入
    var totalIncome = monthlyIncome + additionalIncome + partnerIncome;

    // 计算总支出
    var totalExpenses = monthlyExpenses + existingLoans + otherObligations;

    // 计算可用收入 (通常银行使用80%的净收入)
    var availableIncome = totalIncome * 0.8;

    // 计算最大可承受的月供
    var maxMonthlyPayment = availableIncome - totalExpenses;

    // 确保月供为正数
    if (maxMonthlyPayment <= 0) {
        maxMonthlyPayment = 0;
    }

    // 计算最大贷款金额
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var maxLoanAmount = 0;

    if (monthlyRate > 0 && maxMonthlyPayment > 0) {
        maxLoanAmount = maxMonthlyPayment * (Math.pow(1 + monthlyRate, numPayments) - 1) /
                       (monthlyRate * Math.pow(1 + monthlyRate, numPayments));
    } else if (maxMonthlyPayment > 0) {
        maxLoanAmount = maxMonthlyPayment * numPayments;
    }

    // 计算最大房产价值
    var maxPropertyValue = maxLoanAmount + downPayment;

    // 计算DTI (Debt-to-Income ratio)
    var dti = totalExpenses / totalIncome * 100;

    // 计算LTV (Loan-to-Value ratio)
    var ltv = maxPropertyValue > 0 ? (maxLoanAmount / maxPropertyValue) * 100 : 0;

    // 评估信用状况
    var creditAssessment = assessCreditworthiness(dti, availableIncome, maxMonthlyPayment);

    // 更新显示
    updateCreditCapacityResults(totalIncome, totalExpenses, maxMonthlyPayment,
                               maxLoanAmount, maxPropertyValue, dti, ltv, creditAssessment);
}

function assessCreditworthiness(dti, availableIncome, maxMonthlyPayment) {
    if (maxMonthlyPayment <= 0) {
        return {
            status: 'Negatywna',
            color: '#e74c3c',
            description: 'Brak zdolności kredytowej - wydatki przewyższają dochody',
            recommendation: 'Zmniejsz wydatki lub zwiększ dochody przed złożeniem wniosku'
        };
    } else if (dti > 60) {
        return {
            status: 'Bardzo niska',
            color: '#e67e22',
            description: 'Wysokie zadłużenie względem dochodów',
            recommendation: 'Spłać część zobowiązań przed złożeniem wniosku'
        };
    } else if (dti > 40) {
        return {
            status: 'Niska',
            color: '#f39c12',
            description: 'Umiarkowane zadłużenie, ograniczona zdolność',
            recommendation: 'Rozważ zwiększenie wkładu własnego'
        };
    } else if (dti > 25) {
        return {
            status: 'Dobra',
            color: '#27ae60',
            description: 'Stabilna sytuacja finansowa',
            recommendation: 'Dobre perspektywy na uzyskanie kredytu'
        };
    } else {
        return {
            status: 'Bardzo dobra',
            color: '#2ecc71',
            description: 'Doskonała sytuacja finansowa',
            recommendation: 'Możliwość negocjacji korzystnych warunków'
        };
    }
}

function updateCreditCapacityResults(totalIncome, totalExpenses, maxMonthlyPayment,
                                   maxLoanAmount, maxPropertyValue, dti, ltv, creditAssessment) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Zdolność kredytowa: &nbsp; ' + formatNumber(maxLoanAmount) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 2) {
                switch(i) {
                    case 2: // Maksymalna kwota kredytu
                        cells[1].innerHTML = '<b>' + formatNumber(maxLoanAmount) + ' zł</b>';
                        break;
                    case 3: // Maksymalna wartość nieruchomości
                        cells[1].innerHTML = '<b>' + formatNumber(maxPropertyValue) + ' zł</b>';
                        break;
                    case 4: // Maksymalna rata
                        cells[1].innerHTML = formatNumber(maxMonthlyPayment) + ' zł';
                        break;
                    case 5: // Całkowite dochody
                        cells[1].innerHTML = formatNumber(totalIncome) + ' zł';
                        break;
                    case 6: // Całkowite wydatki
                        cells[1].innerHTML = formatNumber(totalExpenses) + ' zł';
                        break;
                    case 7: // DTI
                        cells[1].innerHTML = dti.toFixed(1) + '%';
                        break;
                    case 8: // LTV
                        cells[1].innerHTML = ltv.toFixed(1) + '%';
                        break;
                }
            }
        }
    }

    // 更新ocena zdolności kredytowej
    var assessmentSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (assessmentSection) {
        assessmentSection.innerHTML =
            '<h3>Ocena zdolności kredytowej:</h3>' +
            '<p><strong>Status:</strong> <span style="color: ' + creditAssessment.color + ';">' + creditAssessment.status + '</span></p>' +
            '<p><strong>Opis:</strong> ' + creditAssessment.description + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + creditAssessment.recommendation + '</p>' +
            '<p><strong>Wskaźnik DTI:</strong> ' + dti.toFixed(1) + '% (optymalne < 40%)</p>' +
            '<p><strong>Dostępny dochód:</strong> ' + formatNumber(totalIncome * 0.8 - totalExpenses) + ' zł miesięcznie</p>';
    }
}

function clearForm(form) {
    if (document.getElementById('cmonthlyincome')) document.getElementById('cmonthlyincome').value = '8000';
    if (document.getElementById('cadditionalincome')) document.getElementById('cadditionalincome').value = '0';
    if (document.getElementById('cpartnerincome')) document.getElementById('cpartnerincome').value = '0';
    if (document.getElementById('cmonthlyexpenses')) document.getElementById('cmonthlyexpenses').value = '3000';
    if (document.getElementById('cexistingloans')) document.getElementById('cexistingloans').value = '0';
    if (document.getElementById('cotherobligations')) document.getElementById('cotherobligations').value = '0';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '100000';
    calculateCreditCapacity();
}

function saveCalResult() {
    alert('Wyniki kalkulatora zdolności kredytowej zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateCreditCapacity();
};
</script>

</body>
</html>
