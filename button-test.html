<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button, input[type="button"] { 
            padding: 10px 20px; 
            margin: 10px; 
            background: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer; 
        }
        .result { 
            background: #f0f0f0; 
            padding: 15px; 
            margin: 10px 0; 
            border: 1px solid #ccc; 
        }
    </style>
</head>
<body>
    <h1>Button Click Test</h1>
    
    <p>Testing different ways to call JavaScript functions:</p>
    
    <button onclick="testFunction1()">Test 1: Button with onclick</button>
    <input type="button" value="Test 2: Input button" onclick="testFunction2()">
    <button onclick="alert('Test 3: Direct alert')">Test 3: Direct alert</button>
    
    <div id="result1" class="result">Test 1 result will appear here</div>
    <div id="result2" class="result">Test 2 result will appear here</div>
    
    <h2>Calculator Button Test</h2>
    <p>This mimics the exact structure from the calculator:</p>
    
    <input type="text" id="chouseprice" value="500000" placeholder="House Price">
    <input type="text" id="cdownpayment" value="20" placeholder="Down Payment">
    <input type="text" id="cloanterm" value="25" placeholder="Loan Term">
    <input type="text" id="cinterestrate" value="7.25" placeholder="Interest Rate">
    
    <br><br>
    <input type="button" name="x" value="Oblicz" onclick="calculateMortgage();">
    
    <h2 class="h2result">Result: 0 zł</h2>
    
    <div id="debug" class="result">Debug info will appear here</div>

    <script>
        function testFunction1() {
            document.getElementById('result1').innerHTML = "✅ Test 1 SUCCESS: Function called at " + new Date().toLocaleTimeString();
        }
        
        function testFunction2() {
            document.getElementById('result2').innerHTML = "✅ Test 2 SUCCESS: Input button works at " + new Date().toLocaleTimeString();
        }
        
        function calculateMortgage() {
            var debug = document.getElementById('debug');
            debug.innerHTML = "🔄 calculateMortgage called at " + new Date().toLocaleTimeString() + "<br>";
            
            try {
                // Get input values
                var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
                var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
                var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
                var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
                
                debug.innerHTML += "📊 Input values: House=" + housePrice + ", Down=" + downPayment + ", Term=" + loanTerm + ", Rate=" + interestRate + "<br>";
                
                // Calculate
                var downPaymentAmount = housePrice * (downPayment / 100);
                var loanAmount = housePrice - downPaymentAmount;
                var monthlyRate = interestRate / 100 / 12;
                var numPayments = loanTerm * 12;
                
                var monthlyPayment = 0;
                if (monthlyRate > 0) {
                    monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                    (Math.pow(1 + monthlyRate, numPayments) - 1);
                } else {
                    monthlyPayment = loanAmount / numPayments;
                }
                
                debug.innerHTML += "💰 Calculated monthly payment: " + Math.round(monthlyPayment) + " zł<br>";
                
                // Update result
                var resultHeader = document.querySelector('.h2result');
                if (resultHeader) {
                    resultHeader.innerHTML = 'Result: ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
                    debug.innerHTML += "✅ Successfully updated h2result element<br>";
                } else {
                    debug.innerHTML += "❌ ERROR: h2result element not found<br>";
                }
                
                debug.innerHTML += "✅ calculateMortgage completed successfully";
                
            } catch (error) {
                debug.innerHTML += "❌ ERROR in calculateMortgage: " + error.message + "<br>";
                debug.innerHTML += "Stack: " + error.stack;
            }
        }
        
        // Test on page load
        window.onload = function() {
            console.log("Page loaded successfully");
            document.getElementById('debug').innerHTML = "🟢 Page loaded, JavaScript is working. Click 'Oblicz' to test calculator.";
        };
    </script>
</body>
</html>
