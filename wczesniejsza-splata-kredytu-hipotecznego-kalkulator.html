<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Wcześniejsza spłata kredytu hipotecznego kalkulator</title>
    <meta name="description" content="Kalkulator wcześniejszej spłaty kredytu hipotecznego - oblicz oszczędności z przedterminowej spłaty, sprawdź prowizje i porównaj różne strategie spłaty kredytu mieszkaniowego.">
    <meta name="keywords" content="wcześniejsza spłata kredytu hipotecznego kalkulator, przed<PERSON><PERSON>owa spłata, prowizja za wcześniejszą spłatę, oszczędności kredytu">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html" itemprop="item"><span itemprop="name">wcześniejsza spłata kredytu hipotecznego kalkulator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Wcześniejsza spłata kredytu hipotecznego kalkulator</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz oszczędności z wcześniejszej spłaty kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostała kwota kredytu do spłaty. Sprawdź w ostatnim wyciągu bankowym lub w bankowości internetowej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentbalance" id="ccurrentbalance" value="420000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Aktualna miesięczna rata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wysokość obecnej miesięcznej raty kredytu hipotecznego (kapitał + odsetki).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentpayment" id="ccurrentpayment" value="2850" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego. Sprawdź w umowie kredytowej lub ostatnim wyciągu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.8" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba lat pozostałych do końca spłaty kredytu według pierwotnego harmonogramu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cremainingterm" id="cremainingterm" value="18" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ wcześniejszej spłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cprepaymenttype" id="cprepaymenttype" onchange="togglePrepaymentOptions();">
                                        <option value="partial" selected="">Częściowa wcześniejsza spłata</option>
                                        <option value="full">Całkowita wcześniejsza spłata</option>
                                        <option value="refinancing">Refinansowanie kredytu</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="partialprepayment">
                                <td align="right">Kwota wcześniejszej spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota, którą chcesz przeznaczyć na wcześniejszą spłatę części kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpartialprepayment" id="cpartialprepayment" value="80000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="fullprepayment" style="display:none;">
                                <td align="right">Data całkowitej spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Planowana data całkowitej spłaty kredytu przed terminem.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="left" colspan="2">
                                    <select name="cfullpaymentmonth" id="cfullpaymentmonth">
                                        <option value="1">Styczeń</option>
                                        <option value="2">Luty</option>
                                        <option value="3">Marzec</option>
                                        <option value="4">Kwiecień</option>
                                        <option value="5">Maj</option>
                                        <option value="6">Czerwiec</option>
                                        <option value="7">Lipiec</option>
                                        <option value="8" selected="">Sierpień</option>
                                        <option value="9">Wrzesień</option>
                                        <option value="10">Październik</option>
                                        <option value="11">Listopad</option>
                                        <option value="12">Grudzień</option>
                                    </select> 
                                    <input type="text" name="cfullpaymentyear" id="cfullpaymentyear" value="2025" class="in4char">
                                </td>
                            </tr>
                            <tr id="refinancingoption" style="display:none;">
                                <td align="right">Nowe oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Oprocentowanie nowego kredytu po refinansowaniu. Sprawdź aktualne oferty banków.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cnewinterestrate" id="cnewinterestrate" value="6.5" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Efekt wcześniejszej spłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cprepaymenteffect" id="cprepaymenteffect">
                                        <option value="reduce_term" selected="">Skrócenie okresu kredytowania</option>
                                        <option value="reduce_payment">Zmniejszenie miesięcznej raty</option>
                                        <option value="mixed">Częściowo okres, częściowo rata</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddcommission" class="cbcontainer">
                                        <input type="checkbox" name="caddcommission" id="caddcommission" value="1" checked="" onclick="cshcommission();">
                                        <span class="cbmark"></span>
                                        <b><span id="ccommissiondesc">Uwzględnij prowizję za wcześniejszą spłatę</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ccommission" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Prowizja za wcześniejszą spłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Opłata pobierana przez bank za wcześniejszą spłatę kredytu. Sprawdź w umowie kredytowej wysokość prowizji.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cprepaymentfee" id="cprepaymentfee" value="2.0" class="innormal inpct"></td>
                                                    <td><select name="cprepaymentfeeunit" onchange="cunitchange('cprepaymentfee', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Maksymalna prowizja <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Maksymalna kwota prowizji, którą bank może pobrać za wcześniejszą spłatę według umowy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmaxcommission" id="cmaxcommission" value="15000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Typ kredytu</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cloantype" id="cloantype" onchange="updateCommissionLimits();">
                                                            <option value="variable" selected="">Oprocentowanie zmienne</option>
                                                            <option value="fixed">Oprocentowanie stałe</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddscenarios" class="cbcontainer">
                                        <input type="checkbox" name="caddscenarios" id="caddscenarios" value="1" onclick="cshscenarios();">
                                        <span class="cbmark"></span>
                                        <b><span id="cscenariosdesc">Porównaj różne scenariusze</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cscenarios" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Scenariusz 1 - kwota <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Alternatywna kwota wcześniejszej spłaty do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario1" id="cscenario1" value="50000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 2 - kwota <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Druga alternatywna kwota wcześniejszej spłaty do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario2" id="cscenario2" value="120000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz wcześniejszą spłatę" onclick="calculateEarlyPayment();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędność: &nbsp; 142 850 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('V2N6ZcWbbmllcnN6YSBzcMWCYXRhIGtyZWR5dHUgaGlwb3RlY3puZWdv', 0, 'V2N6ZcWbbmllcnN6YSBzcMWCYXRhIGtyZWR5dHUgaGlwb3RlY3puZWdv', 'T3N6Y3rEmWRub8WbxIc=', 'MTQyIDg1MCAiekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez wcześniejszej spłaty</b></td>
                                        <td align="right"><b>Z wcześniejszą spłatą</b></td>
                                        <td align="right"><b>Różnica</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowity koszt kredytu</b></td>
                                        <td align="right"><b>925 200 zł</b></td>
                                        <td align="right"><b>782 350 zł</b></td>
                                        <td align="right"><b>142 850 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">505 200 zł</td>
                                        <td align="right">362 350 zł</td>
                                        <td align="right">142 850 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty</td>
                                        <td align="right">18 lat</td>
                                        <td align="right">14 lat 2 miesiące</td>
                                        <td align="right">3 lata 10 miesięcy</td>
                                    </tr>
                                    <tr>
                                        <td>Prowizja za wcześniejszą spłatę</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">1 600 zł</td>
                                        <td align="right">-1 600 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Netto oszczędność</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                        <td align="right">141 250 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza wcześniejszej spłaty:</h3>
                                            <p><strong>Efektywność:</strong> Każda złotówka wcześniejszej spłaty oszczędza 1,78 zł odsetek</p>
                                            <p><strong>ROI wcześniejszej spłaty:</strong> 178% zwrotu z inwestycji</p>
                                            <p><strong>Skrócenie okresu:</strong> 3 lata 10 miesięcy wcześniejsze zakończenie</p>
                                            <p><strong>Miesięczna oszczędność:</strong> 835 zł mniej odsetek miesięcznie</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Wcześniejsza spłata jest bardzo opłacalna</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie scenariuszy wcześniejszej spłaty</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Scenariusz</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">0 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">100K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">200K zł</text>
                    
                    <!-- Bez wcześniejszej spłaty -->
                    <rect x="60" y="45" width="40" height="100" fill="#ff6b6b" opacity="0.8"></rect>
                    <text x="80" y="155" class="mcllabelx">Bez spłaty</text>
                    <text x="80" y="40" class="mcllabelx" style="fill:#000;">505K zł</text>
                    
                    <!-- 50K wcześniejsza spłata -->
                    <rect x="120" y="65" width="40" height="80" fill="#4ecdc4" opacity="0.8"></rect>
                    <text x="140" y="155" class="mcllabelx">50K zł</text>
                    <text x="140" y="60" class="mcllabelx" style="fill:#000;">420K zł</text>
                    
                    <!-- 80K wcześniejsza spłata -->
                    <rect x="180" y="75" width="40" height="70" fill="#45b7d1" opacity="0.8"></rect>
                    <text x="200" y="155" class="mcllabelx">80K zł</text>
                    <text x="200" y="70" class="mcllabelx" style="fill:#000;">362K zł</text>
                    
                    <!-- 120K wcześniejsza spłata -->
                    <rect x="240" y="85" width="40" height="60" fill="#96ceb4" opacity="0.8"></rect>
                    <text x="260" y="155" class="mcllabelx">120K zł</text>
                    <text x="260" y="80" class="mcllabelx" style="fill:#000;">295K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Porównanie scenariuszy &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Scenariusz</th>
                            <th>Kwota spłaty</th>
                            <th>Oszczędność</th>
                            <th>Skrócenie</th>
                            <th>ROI</th>
                        </tr>
                        <tr>
                            <td>Bez wcześniejszej spłaty</td>
                            <td>0 zł</td>
                            <td>0 zł</td>
                            <td>0 miesięcy</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>50 000 zł</td>
                            <td>50 000 zł</td>
                            <td>85 200 zł</td>
                            <td>28 miesięcy</td>
                            <td>170%</td>
                        </tr>
                        <tr>
                            <td>80 000 zł</td>
                            <td>80 000 zł</td>
                            <td>141 250 zł</td>
                            <td>46 miesięcy</td>
                            <td>178%</td>
                        </tr>
                        <tr>
                            <td>120 000 zł</td>
                            <td>120 000 zł</td>
                            <td>207 500 zł</td>
                            <td>68 miesięcy</td>
                            <td>173%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Wcześniejsza spłata kredytu hipotecznego - kalkulator i przewodnik</h2>
            <p>Wcześniejsza spłata kredytu hipotecznego to jedna z najefektywniejszych metod oszczędzania na odsetkach i skracania okresu zadłużenia. Nasz kalkulator wcześniejszej spłaty pomoże Ci precyzyjnie obliczyć oszczędności, uwzględnić prowizje bankowe i porównać różne scenariusze przedterminowej spłaty kredytu mieszkaniowego.</p>

            <h3>Rodzaje wcześniejszej spłaty kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Częściowa wcześniejsza spłata:</h4>
                <ul>
                    <li><strong>Jednorazowa nadpłata</strong> - spłata części kapitału kredytu</li>
                    <li><strong>Skrócenie okresu</strong> - zachowanie wysokości raty, wcześniejsze zakończenie</li>
                    <li><strong>Zmniejszenie raty</strong> - obniżenie miesięcznych zobowiązań</li>
                    <li><strong>Rozwiązanie mieszane</strong> - częściowo okres, częściowo rata</li>
                    <li><strong>Elastyczność</strong> - możliwość wyboru efektu nadpłaty</li>
                </ul>

                <h4>Całkowita wcześniejsza spłata:</h4>
                <ul>
                    <li><strong>Pełne zamknięcie kredytu</strong> - spłata całego pozostałego salda</li>
                    <li><strong>Uwolnienie hipoteki</strong> - pełne prawo własności nieruchomości</li>
                    <li><strong>Brak dalszych zobowiązań</strong> - koniec płacenia odsetek</li>
                    <li><strong>Wyższa prowizja</strong> - banki pobierają wyższe opłaty</li>
                    <li><strong>Oszczędności maksymalne</strong> - eliminacja wszystkich przyszłych odsetek</li>
                </ul>

                <h4>Refinansowanie kredytu:</h4>
                <ul>
                    <li><strong>Zmiana banku</strong> - przeniesienie kredytu do innej instytucji</li>
                    <li><strong>Lepsze warunki</strong> - niższe oprocentowanie lub prowizje</li>
                    <li><strong>Nowe możliwości</strong> - zmiana typu oprocentowania</li>
                    <li><strong>Koszty przejścia</strong> - opłaty notarialne i bankowe</li>
                    <li><strong>Analiza opłacalności</strong> - porównanie kosztów z oszczędnościami</li>
                </ul>
            </div>

            <h3>Prowizje za wcześniejszą spłatę w Polsce:</h3>
            <div style="margin: 15px 0;">
                <h4>Przepisy prawne:</h4>
                <p>Zgodnie z polskim prawem, banki mogą pobierać prowizję za wcześniejszą spłatę kredytu hipotecznego:</p>
                <ul>
                    <li><strong>Kredyty o zmiennym oprocentowaniu:</strong> maksymalnie 1% spłacanej kwoty</li>
                    <li><strong>Kredyty o stałym oprocentowaniu:</strong> maksymalnie 3% spłacanej kwoty</li>
                    <li><strong>Ograniczenia czasowe:</strong> prowizja tylko w pierwszych latach kredytu</li>
                    <li><strong>Minimalne kwoty:</strong> prowizja od określonej kwoty (np. 10 000 zł)</li>
                    <li><strong>Maksymalne limity:</strong> górny limit prowizji (np. 50 000 zł)</li>
                </ul>

                <h4>Praktyka bankowa:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Bank</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie zmienne</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie stałe</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Maksymalna prowizja</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">PKO BP</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">ING Bank Śląski</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">mBank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">20 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Santander</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">40 000 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Kiedy warto dokonać wcześniejszej spłaty?</h3>
            <ul>
                <li><strong>Wysokie oprocentowanie</strong> - powyżej 6-7% rocznie</li>
                <li><strong>Długi pozostały okres</strong> - więcej niż 10 lat do spłaty</li>
                <li><strong>Stabilna sytuacja finansowa</strong> - pewne źródła dochodów</li>
                <li><strong>Rezerwa finansowa</strong> - 6-12 miesięcznych wydatków odłożonych</li>
                <li><strong>Brak lepszych inwestycji</strong> - wcześniejsza spłata jako bezpieczna lokata</li>
                <li><strong>Niska prowizja</strong> - koszt wcześniejszej spłaty poniżej 2%</li>
                <li><strong>Planowane zmiany</strong> - sprzedaż nieruchomości, przeprowadzka</li>
            </ul>

            <h3>Kiedy nie warto dokonywać wcześniejszej spłaty?</h3>
            <ul>
                <li><strong>Niskie oprocentowanie</strong> - poniżej 4-5% rocznie</li>
                <li><strong>Wysokie prowizje</strong> - powyżej 2-3% spłacanej kwoty</li>
                <li><strong>Lepsze inwestycje</strong> - możliwość osiągnięcia wyższej stopy zwrotu</li>
                <li><strong>Brak rezerwy</strong> - wszystkie środki zamrożone w kredycie</li>
                <li><strong>Inne długi</strong> - kredyty o wyższym oprocentowaniu do spłaty</li>
                <li><strong>Krótki pozostały okres</strong> - mniej niż 5 lat do końca spłaty</li>
                <li><strong>Ulgi podatkowe</strong> - możliwość odliczenia odsetek od podatku</li>
            </ul>

            <h3>Strategie wcześniejszej spłaty:</h3>
            <div style="margin: 15px 0;">
                <h4>Strategia skracania okresu:</h4>
                <ul>
                    <li><strong>Maksymalne oszczędności</strong> - największa redukcja odsetek</li>
                    <li><strong>Zachowanie raty</strong> - bez zmiany miesięcznego budżetu</li>
                    <li><strong>Wcześniejsze uwolnienie</strong> - szybsze zakończenie zadłużenia</li>
                    <li><strong>Większa elastyczność</strong> - wcześniejsza swoboda finansowa</li>
                </ul>

                <h4>Strategia zmniejszania raty:</h4>
                <ul>
                    <li><strong>Poprawa płynności</strong> - niższe miesięczne zobowiązania</li>
                    <li><strong>Większy komfort</strong> - łatwiejsze zarządzanie budżetem</li>
                    <li><strong>Mniejsze oszczędności</strong> - niższy efekt na odsetki</li>
                    <li><strong>Dłuższy okres</strong> - zachowanie pierwotnego terminu</li>
                </ul>

                <h4>Strategia mieszana:</h4>
                <ul>
                    <li><strong>Kompromis</strong> - częściowo okres, częściowo rata</li>
                    <li><strong>Elastyczność</strong> - dostosowanie do sytuacji finansowej</li>
                    <li><strong>Optymalizacja</strong> - balans między oszczędnościami a płynnością</li>
                </ul>
            </div>

            <h3>Proces wcześniejszej spłaty kredytu:</h3>
            <ol>
                <li><strong>Analiza umowy kredytowej</strong> - sprawdź warunki wcześniejszej spłaty</li>
                <li><strong>Kalkulacja opłacalności</strong> - użyj naszego kalkulatora</li>
                <li><strong>Zgromadzenie środków</strong> - przygotuj kwotę do spłaty</li>
                <li><strong>Kontakt z bankiem</strong> - złóż wniosek o wcześniejszą spłatę</li>
                <li><strong>Otrzymanie kalkulacji</strong> - bank przeliczy saldo i prowizję</li>
                <li><strong>Dokonanie wpłaty</strong> - przelej środki na rachunek kredytu</li>
                <li><strong>Potwierdzenie spłaty</strong> - otrzymaj dokumenty potwierdzające</li>
                <li><strong>Aktualizacja harmonogramu</strong> - nowy plan spłat</li>
            </ol>

            <h3>Dokumenty potrzebne do wcześniejszej spłaty:</h3>
            <ul>
                <li>Wniosek o wcześniejszą spłatę kredytu</li>
                <li>Dokument tożsamości kredytobiorcy</li>
                <li>Umowa kredytowa (kopia)</li>
                <li>Ostatni wyciąg z rachunku kredytowego</li>
                <li>Potwierdzenie źródła środków (jeśli wymagane)</li>
                <li>Pełnomocnictwo (jeśli spłata przez pełnomocnika)</li>
            </ul>

            <h3>Alternatywy dla wcześniejszej spłaty:</h3>
            <ul>
                <li><strong>Lokaty bankowe</strong> - bezpieczne oprocentowanie 4-6%</li>
                <li><strong>Obligacje skarbowe</strong> - oprocentowanie 5-7%</li>
                <li><strong>Fundusze inwestycyjne</strong> - potencjalnie wyższe zyski</li>
                <li><strong>Nieruchomości inwestycyjne</strong> - dochód z wynajmu</li>
                <li><strong>Akcje i ETF</strong> - długoterminowe inwestycje</li>
                <li><strong>Własny biznes</strong> - inwestycja w rozwój działalności</li>
            </ul>

            <h3>Porady ekspertów:</h3>
            <ul>
                <li>Zawsze sprawdź dokładnie warunki wcześniejszej spłaty w umowie</li>
                <li>Porównaj koszt prowizji z oszczędnościami na odsetkach</li>
                <li>Zachowaj rezerwę finansową na nieprzewidziane wydatki</li>
                <li>Rozważ alternatywne inwestycje o wyższej stopie zwrotu</li>
                <li>Spłacaj najpierw droższe kredyty konsumpcyjne</li>
                <li>Planuj wcześniejszą spłatę na początku roku podatkowego</li>
                <li>Skonsultuj się z doradcą finansowym przy dużych kwotach</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/szczegolowy-kalkulator-kredytu-hipotecznego.html">Szczegółowy kalkulator</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator kredytu</a>
                <a href="/kalkulator-kredytu.html">Kalkulator kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Prowizje banków</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Prowizje za wcześniejszą spłatę:</strong><br>
                        Zmienne: <a href="#" onclick="return setCommission('1.0');">1%</a><br>
                        Stałe: <a href="#" onclick="return setCommission('3.0');">3%</a><br>
                        <small>*sprawdź w umowie kredytowej</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator wcześniejszej spłaty kredytu hipotecznego - oblicz oszczędności z przedterminowej spłaty i sprawdź opłacalność wcześniejszej spłaty.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function togglePrepaymentOptions() {
    var type = document.getElementById('cprepaymenttype').value;
    var partial = document.getElementById('partialprepayment');
    var full = document.getElementById('fullprepayment');
    var refinancing = document.getElementById('refinancingoption');
    
    partial.style.display = (type === 'partial') ? 'table-row' : 'none';
    full.style.display = (type === 'full') ? 'table-row' : 'none';
    refinancing.style.display = (type === 'refinancing') ? 'table-row' : 'none';
}

function cshcommission() {
    var checkbox = document.getElementById('caddcommission');
    var commissionDiv = document.getElementById('ccommission');
    if (checkbox.checked) {
        commissionDiv.style.display = 'block';
    } else {
        commissionDiv.style.display = 'none';
    }
}

function cshscenarios() {
    var checkbox = document.getElementById('caddscenarios');
    var scenariosDiv = document.getElementById('cscenarios');
    if (checkbox.checked) {
        scenariosDiv.style.display = 'block';
    } else {
        scenariosDiv.style.display = 'none';
    }
}

function updateCommissionLimits() {
    var loanType = document.getElementById('cloantype').value;
    var feeField = document.getElementById('cprepaymentfee');
    
    if (loanType === 'variable') {
        feeField.value = '1.0';
    } else {
        feeField.value = '3.0';
    }
}

function setCommission(commission) {
    document.getElementById('cprepaymentfee').value = commission;
    return false;
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram spłat &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Porównanie scenariuszy</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Porównanie scenariuszy &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a>';
    }
    return false;
}
</script>

<script>
function calculateEarlyPayment() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var monthsPaid = parseFloat(document.getElementById('cmonthspaid').value) || 0;

    // 获取wcześniejsza spłata信息
    var earlyPaymentAmount = parseFloat(document.getElementById('cearlypayment').value) || 0;
    var earlyPaymentType = document.getElementById('cearlypaymenttype').value;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算当前余额
    var currentBalance = loanAmount;
    for (var i = 0; i < monthsPaid; i++) {
        var monthlyInterest = currentBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;
        currentBalance -= monthlyPrincipal;
    }

    // 确保余额不为负
    if (currentBalance < 0) currentBalance = 0;

    // 计算没有wcześniejsza spłata的情况
    var remainingPayments = numPayments - monthsPaid;
    var totalWithoutEarlyPayment = monthlyPayment * remainingPayments;
    var interestWithoutEarlyPayment = totalWithoutEarlyPayment - currentBalance;

    // 计算有wcześniejsza spłata的情况
    var newBalance = currentBalance - earlyPaymentAmount;
    if (newBalance < 0) newBalance = 0;

    var newMonthlyPayment = monthlyPayment;
    var newRemainingPayments = remainingPayments;

    if (earlyPaymentType === 'reduce_time' && newBalance > 0) {
        // 缩短期限，保持月供不变
        if (monthlyRate > 0) {
            newRemainingPayments = Math.log(1 + (newBalance * monthlyRate) / monthlyPayment) / Math.log(1 + monthlyRate);
        } else {
            newRemainingPayments = newBalance / monthlyPayment;
        }
        newRemainingPayments = Math.ceil(newRemainingPayments);
    } else if (earlyPaymentType === 'reduce_payment' && newBalance > 0) {
        // 降低月供，保持期限不变
        if (monthlyRate > 0) {
            newMonthlyPayment = newBalance * (monthlyRate * Math.pow(1 + monthlyRate, remainingPayments)) /
                               (Math.pow(1 + monthlyRate, remainingPayments) - 1);
        } else {
            newMonthlyPayment = newBalance / remainingPayments;
        }
    }

    // 计算节省
    var totalWithEarlyPayment = newMonthlyPayment * newRemainingPayments + earlyPaymentAmount;
    var timeSaved = remainingPayments - newRemainingPayments;
    var moneySaved = totalWithoutEarlyPayment - (totalWithEarlyPayment - earlyPaymentAmount);
    var interestSaved = interestWithoutEarlyPayment - (totalWithEarlyPayment - earlyPaymentAmount - newBalance);

    // 计算效率
    var efficiency = earlyPaymentAmount > 0 ? (moneySaved / earlyPaymentAmount) * 100 : 0;

    // 更新显示
    updateEarlyPaymentResults(currentBalance, earlyPaymentAmount, monthlyPayment, newMonthlyPayment,
                            remainingPayments, newRemainingPayments, timeSaved, moneySaved,
                            interestSaved, efficiency, earlyPaymentType);
}

function updateEarlyPaymentResults(currentBalance, earlyPaymentAmount, monthlyPayment, newMonthlyPayment,
                                 remainingPayments, newRemainingPayments, timeSaved, moneySaved,
                                 interestSaved, efficiency, earlyPaymentType) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        var mainResult = earlyPaymentType === 'reduce_time' ?
            'Skrócenie: ' + formatTime(timeSaved) :
            'Nowa rata: ' + formatNumber(newMonthlyPayment) + ' zł';

        resultHeader.innerHTML =
            mainResult +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 3) {
                switch(i) {
                    case 2: // Obecne saldo
                        cells[1].innerHTML = '<b>' + formatNumber(currentBalance) + ' zł</b>';
                        break;
                    case 3: // Kwota wcześniejszej spłaty
                        cells[1].innerHTML = formatNumber(earlyPaymentAmount) + ' zł';
                        break;
                    case 4: // Nowe saldo
                        cells[1].innerHTML = formatNumber(currentBalance - earlyPaymentAmount) + ' zł';
                        break;
                    case 5: // Obecna rata
                        cells[1].innerHTML = formatNumber(monthlyPayment) + ' zł';
                        cells[2].innerHTML = formatNumber(newMonthlyPayment) + ' zł';
                        break;
                    case 6: // Pozostały czas
                        cells[1].innerHTML = formatTime(remainingPayments);
                        cells[2].innerHTML = formatTime(newRemainingPayments);
                        break;
                    case 7: // Oszczędność czasu
                        cells[2].innerHTML = '<b>' + formatTime(timeSaved) + '</b>';
                        break;
                    case 8: // Oszczędność pieniędzy
                        cells[2].innerHTML = '<b>' + formatNumber(moneySaved) + ' zł</b>';
                        break;
                }
            }
        }
    }

    // 更新analiza wcześniejszej spłaty
    var analysisSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (analysisSection) {
        analysisSection.innerHTML =
            '<h3>Analiza wcześniejszej spłaty:</h3>' +
            '<p><strong>Typ spłaty:</strong> ' + (earlyPaymentType === 'reduce_time' ? 'Skrócenie okresu kredytowania' : 'Zmniejszenie miesięcznej raty') + '</p>' +
            '<p><strong>Efektywność:</strong> ' + efficiency.toFixed(0) + '% (każda złotówka wcześniejszej spłaty oszczędza ' + (efficiency/100).toFixed(2) + ' zł)</p>' +
            '<p><strong>Oszczędność odsetek:</strong> ' + formatNumber(interestSaved) + ' zł</p>' +
            '<p><strong>Całkowita oszczędność:</strong> ' + formatNumber(moneySaved) + ' zł</p>' +
            '<p><strong>Rekomendacja:</strong> ' + getEarlyPaymentRecommendation(efficiency) + '</p>';
    }
}

function getEarlyPaymentRecommendation(efficiency) {
    if (efficiency > 150) {
        return 'Wcześniejsza spłata bardzo opłacalna - zdecydowanie zalecana';
    } else if (efficiency > 120) {
        return 'Wcześniejsza spłata opłacalna - warto rozważyć';
    } else if (efficiency > 100) {
        return 'Wcześniejsza spłata umiarkowanie opłacalna';
    } else {
        return 'Rozważ inne formy inwestycji zamiast wcześniejszej spłaty';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cmonthspaid')) document.getElementById('cmonthspaid').value = '60';
    if (document.getElementById('cearlypayment')) document.getElementById('cearlypayment').value = '50000';
    calculateEarlyPayment();
}

function saveCalResult() {
    alert('Wyniki kalkulatora wcześniejszej spłaty zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateEarlyPayment();
};
</script>

</body>
</html>
