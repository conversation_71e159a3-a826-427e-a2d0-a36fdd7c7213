<!DOCTYPE html>
<!-- saved from url=(0070)https://securepubads.g.doubleclick.net/static/topics/topics_frame.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <title>Topics Frame</title>
    <meta http-equiv="origin-trial" content="Avh5Ny0XEFCyQ7+oNieXskUrqY8edUzL5/XrwKlGjARQHW4TFRK+jVd5HnDIpY20n5OLHfgU4ku7x48N3uhG/A0AAABxeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=">
    <script>
      
/*

 Copyright 2022 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var m,aa,ba=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},ea=da(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",q={},ia={},w=function(a,b,c){if(!c||a!=null){c=ia[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}},y=function(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],g;!a&&e in q?g=q:g=ea;for(e=0;e<d.length-1;e++){var f=d[e];if(!(f in g))break a;g=g[f]}d=d[d.length-1];c=fa&&c==="es6"?g[d]:null;b=b(c);b!=null&&(a?ca(q,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ia[d]===void 0&&(a=Math.random()*1E9>>>0,ia[d]=fa?ea.Symbol(d):"$jscp$"+a+"$"+d),ca(g,ia[d],{configurable:!0,writable:!0,value:b})))}};y("Symbol",function(a){if(a)return a;var b=function(g,f){this.g=g;ca(this,"description",{configurable:!0,writable:!0,value:f})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(g){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(g||"")+"_"+d++,g)};return e},"es6");y("Symbol.iterator",function(a){if(a)return a;a=(0,q.Symbol)("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ja(ba(this))}})}return a},"es6");var ja=function(a){a={next:a};a[w(q.Symbol,"iterator")]=function(){return this};return a},ka=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},la;if(fa&&typeof w(Object,"setPrototypeOf")=="function")la=w(Object,"setPrototypeOf");else{var ma;a:{var na={a:!0},oa={};try{oa.__proto__=na;ma=oa.a;break a}catch(a){}ma=!1}la=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var pa=la,z=function(a,b){a.prototype=ka(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Na=b.prototype},A=function(a){var b=typeof q.Symbol!="undefined"&&w(q.Symbol,"iterator")&&a[w(q.Symbol,"iterator")];if(b)return b.call(a);if(typeof a.length=="number")return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},qa=function(a){if(!(a instanceof Array)){a=A(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},ua=function(a){return ta(a,a)},ta=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},va=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},wa=fa&&typeof w(Object,"assign")=="function"?w(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)va(d,e)&&(a[e]=d[e])}return a};y("Object.assign",function(a){return a||wa},"es6");var xa=function(){this.F=!1;this.i=null;this.g=void 0;this.h=1;this.T=this.u=0;this.l=null},ya=function(a){if(a.F)throw new TypeError("Generator is already running");a.F=!0};xa.prototype.S=function(a){this.g=a};var za=function(a,b){a.l={ka:b,Ca:!0};a.h=a.u||a.T};xa.prototype.return=function(a){this.l={return:a};this.h=this.T};var B=function(a,b,c){a.h=c;return{value:b}},Aa=function(a){a.u=0;var b=a.l.ka;a.l=null;return b},Ba=function(a){this.g=new xa;this.h=a},Fa=function(a,b){ya(a.g);var c=a.g.i;if(c)return Ca(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return Da(a)},Ca=function(a,b,c,d){try{var e=b.call(a.g.i,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.F=!1,e;var g=e.value}catch(f){return a.g.i=null,za(a.g,f),Da(a)}a.g.i=null;d.call(a.g,g);return Da(a)},Da=function(a){for(;a.g.h;)try{var b=a.h(a.g);if(b)return a.g.F=!1,{value:b.value,done:!1}}catch(c){a.g.g=void 0,za(a.g,c)}a.g.F=!1;if(a.g.l){b=a.g.l;a.g.l=null;if(b.Ca)throw b.ka;return{value:b.return,done:!0}}return{value:void 0,done:!0}},Ga=function(a){this.next=function(b){ya(a.g);a.g.i?b=Ca(a,a.g.i.next,b,a.g.S):(a.g.S(b),b=Da(a));return b};this.throw=function(b){ya(a.g);a.g.i?b=Ca(a,a.g.i["throw"],b,a.g.S):(za(a.g,b),b=Da(a));return b};this.return=function(b){return Fa(a,b)};this[w(q.Symbol,"iterator")]=function(){return this}},Ha=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new q.Promise(function(d,e){function g(f){f.done?d(f.value):q.Promise.resolve(f.value).then(b,c).then(g,e)}g(a.next())})},C=function(a){return Ha(new Ga(new Ba(a)))},Ia=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};y("globalThis",function(a){return a||ea},"es_2020");y("Reflect.setPrototypeOf",function(a){return a?a:pa?function(b,c){try{return pa(b,c),!0}catch(d){return!1}}:null},"es6");y("Promise",function(a){function b(){this.g=null}function c(f){return f instanceof e?f:new e(function(h){h(f)})}if(a)return a;b.prototype.h=function(f){if(this.g==null){this.g=[];var h=this;this.i(function(){h.u()})}this.g.push(f)};var d=ea.setTimeout;b.prototype.i=function(f){d(f,0)};b.prototype.u=function(){for(;this.g&&this.g.length;){var f=this.g;this.g=[];for(var h=0;h<f.length;++h){var k=f[h];f[h]=null;try{k()}catch(l){this.l(l)}}}this.g=null};b.prototype.l=function(f){this.i(function(){throw f;})};var e=function(f){this.h=0;this.i=void 0;this.g=[];this.S=!1;var h=this.l();try{f(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.l=function(){function f(l){return function(n){k||(k=!0,l.call(h,n))}}var h=this,k=!1;return{resolve:f(this.ra),reject:f(this.u)}};e.prototype.ra=function(f){if(f===this)this.u(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.ta(f);else{a:switch(typeof f){case "object":var h=f!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.qa(f):this.F(f)}};e.prototype.qa=function(f){var h=void 0;try{h=f.then}catch(k){this.u(k);return}typeof h=="function"?this.ua(h,f):this.F(f)};e.prototype.u=function(f){this.T(2,f)};e.prototype.F=function(f){this.T(1,f)};e.prototype.T=function(f,h){if(this.h!=0)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.h);this.h=f;this.i=h;this.h===2&&this.sa();this.oa()};e.prototype.sa=function(){var f=this;d(function(){if(f.pa()){var h=ea.console;typeof h!=="undefined"&&h.error(f.i)}},1)};e.prototype.pa=function(){if(this.S)return!1;var f=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if(typeof k==="undefined")return!0;typeof f==="function"?f=new f("unhandledrejection",{cancelable:!0}):typeof h==="function"?f=new h("unhandledrejection",{cancelable:!0}):(f=ea.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.i;return k(f)};e.prototype.oa=function(){if(this.g!=null){for(var f=0;f<this.g.length;++f)g.h(this.g[f]);this.g=null}};var g=new b;e.prototype.ta=function(f){var h=this.l();f.Y(h.resolve,h.reject)};e.prototype.ua=function(f,h){var k=this.l();try{f.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=function(f,h){function k(r,t){return typeof r=="function"?function(u){try{l(r(u))}catch(v){n(v)}}:t}var l,n,p=new e(function(r,t){l=r;n=t});this.Y(k(f,l),k(h,n));return p};e.prototype.catch=function(f){return this.then(void 0,f)};e.prototype.Y=function(f,h){function k(){switch(l.h){case 1:f(l.i);break;case 2:h(l.i);break;default:throw Error("Unexpected state: "+l.h);}}var l=this;this.g==null?g.h(k):this.g.push(k);this.S=!0};e.resolve=c;e.reject=function(f){return new e(function(h,k){k(f)})};e.race=function(f){return new e(function(h,k){for(var l=A(f),n=l.next();!n.done;n=l.next())c(n.value).Y(h,k)})};e.all=function(f){var h=A(f),k=h.next();return k.done?c([]):new e(function(l,n){function p(u){return function(v){r[u]=v;t--;t==0&&l(r)}}var r=[],t=0;do r.push(void 0),t++,c(k.value).Y(p(r.length-1),n),k=h.next();while(!k.done)})};return e},"es6");y("Object.setPrototypeOf",function(a){return a||pa},"es6");y("WeakMap",function(a){function b(){}function c(f){var h=typeof f;return h==="object"&&f!==null||h==="function"}if(function(){if(!a||!Object.seal)return!1;try{var f=Object.seal({}),h=Object.seal({}),k=new a([[f,2],[h,3]]);if(k.get(f)!=2||k.get(h)!=3)return!1;k.delete(f);k.set(h,4);return!k.has(f)&&k.get(h)==4}catch(l){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.g=(e+=Math.random()+1).toString();if(f){f=A(f);for(var h;!(h=f.next()).done;)h=h.value,this.set(h[0],h[1])}};g.prototype.set=function(f,h){if(!c(f))throw Error("Invalid WeakMap key");if(!va(f,d)){var k=new b;ca(f,d,{value:k})}if(!va(f,d))throw Error("WeakMap key fail: "+f);f[d][this.g]=h;return this};g.prototype.get=function(f){return c(f)&&va(f,d)?f[d][this.g]:void 0};g.prototype.has=function(f){return c(f)&&va(f,d)&&va(f[d],this.g)};g.prototype.delete=function(f){return c(f)&&va(f,d)&&va(f[d],this.g)?delete f[d][this.g]:!1};return g},"es6");y("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(A([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),n=l.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=l.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!l.next().done?!1:!0}catch(p){return!1}}())return a;var b=new q.WeakMap,c=function(h){this[0]={};this[1]=g();this.size=0;if(h){h=A(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],H:this[1].H,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].H.next=l.entry,this[1].H=l.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.H.next=h.entry.next,h.entry.next.H=h.entry.H,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].H=g();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),n;!(n=l.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[w(q.Symbol,"iterator")]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;l=="object"||l=="function"?b.has(k)?l=b.get(k):(l=""+ ++f,b.set(k,l)):l="p_"+k;var n=h[0][l];if(n&&va(h[0],l))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:l,list:n,index:h,entry:p}}return{id:l,list:n,index:-1,entry:void 0}},e=function(h,k){var l=h[1];return ja(function(){if(l){for(;l.head!=h[1];)l=l.H;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})},g=function(){var h={};return h.H=h.next=h.head=h},f=0;return c},"es6");y("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}},"es6");y("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||w(Object,"is").call(Object,g,b))return!0}return!1}},"es7");y("String.prototype.includes",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return this.indexOf(b,c||0)!==-1}},"es6");y("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}},"es6");y("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6");y("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991},"es6");y("Number.isInteger",function(a){return a?a:function(b){return w(Number,"isFinite").call(Number,b)?b===Math.floor(b):!1}},"es6");y("Number.isSafeInteger",function(a){return a?a:function(b){return w(Number,"isInteger").call(Number,b)&&Math.abs(b)<=w(Number,"MAX_SAFE_INTEGER")}},"es6");y("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}},"es6");y("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}},"es6");/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var D=this||self,Ja=function(a,b){a:{var c=["CLOSURE_FLAGS"];for(var d=D,e=0;e<c.length;e++)if(d=d[c[e]],d==null){c=null;break a}c=d}a=c&&c[a];return a!=null?a:b},Ka=function(a){return a};function La(){throw Error("Invalid UTF8");}function Ma(a,b){b=String.fromCharCode.apply(null,b);return a==null?b:a+b}var Na=void 0,Oa,Pa=typeof TextDecoder!=="undefined",Qa,Ra=typeof String.prototype.isWellFormed==="function",Sa=typeof TextEncoder!=="undefined";function Ta(a){D.setTimeout(function(){throw a;},0)};var Ua=Ja(610401301,!1),Va=Ja(748402147,Ja(1,!0));function Wa(){var a=D.navigator;return a&&(a=a.userAgent)?a:""}var Xa,Ya=D.navigator;Xa=Ya?Ya.userAgentData||null:null;function Za(a){return Wa().indexOf(a)!=-1};var $a=function(a){$a[" "](a);return a};$a[" "]=function(){};var ab=Za("Gecko")&&!(Wa().toLowerCase().indexOf("webkit")!=-1&&!Za("Edge"))&&!(Za("Trident")||Za("MSIE"))&&!Za("Edge"),bb=Wa().toLowerCase().indexOf("webkit")!=-1&&!Za("Edge");var cb={},db=null,fb=function(a,b){b===void 0&&(b=0);eb();b=cb[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,g=0;e<a.length-2;e+=3){var f=a[e],h=a[e+1],k=a[e+2],l=b[f>>2];f=b[(f&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[g++]=l+f+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[g]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")},hb=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):"=.".indexOf(a[b-1])!=-1&&(c="=.".indexOf(a[b-2])!=-1?c-2:c-1);var d=new Uint8Array(c),e=0;gb(a,function(g){d[e++]=g});return e!==c?d.subarray(0,e):d},gb=function(a,b){function c(k){for(;d<a.length;){var l=a.charAt(d++),n=db[l];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(l))throw Error("Unknown base64 encoding at char: "+l);}return k}eb();for(var d=0;;){var e=c(-1),g=c(0),f=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|g>>4);f!=64&&(b(g<<4&240|f>>2),h!=64&&b(f<<6&192|h))}},eb=function(){if(!db){db={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));cb[c]=d;for(var e=0;e<d.length;e++){var g=d[e];db[g]===void 0&&(db[g]=e)}}}};var ib=typeof Uint8Array!=="undefined",jb=!(Ua&&Xa&&Xa.brands.length>0?0:Za("Trident")||Za("MSIE"))&&typeof btoa==="function",kb=/[-_.]/g,lb={"-":"+",_:"/",".":"="};function mb(a){return lb[a]||""}function nb(a){if(!jb)return hb(a);a=kb.test(a)?a.replace(kb,mb):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b}var ob={};var qb=function(a,b){pb(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");},sb=function(){return rb||(rb=new qb(null,ob))},tb=function(a){pb(ob);var b=a.g;b=b==null||ib&&b!=null&&b instanceof Uint8Array?b:typeof b==="string"?nb(b):null;return b==null?b:a.g=b},rb;function pb(a){if(a!==ob)throw Error("illegal external caller");};function ub(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var yb=void 0;function zb(a){a=Error(a);ub(a,"warning");return a}function Ab(a,b){if(a!=null){var c;var d=(c=yb)!=null?c:yb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),ub(a,"incident"),Ta(a))}};var Bb=typeof q.Symbol==="function"&&typeof(0,q.Symbol)()==="symbol";function Cb(a,b,c){return typeof q.Symbol==="function"&&typeof(0,q.Symbol)()==="symbol"?(c===void 0?0:c)&&q.Symbol.for&&a?q.Symbol.for(a):a!=null?(0,q.Symbol)(a):(0,q.Symbol)():b}var Db=Cb("jas",void 0,!0),Eb=Cb(void 0,(0,q.Symbol)()),Fb=Cb(void 0,"0ubs"),Gb=Cb(void 0,"0ubsb"),Hb=Cb(void 0,"0actk"),Ib=Cb("m_m","Ja",!0);var Jb={Ba:{value:0,configurable:!0,writable:!0,enumerable:!1}},Kb=Object.defineProperties,E=Bb?Db:"Ba",Lb,Mb=[];F(Mb,7);Lb=Object.freeze(Mb);function Nb(a,b){Bb||E in a||Kb(a,Jb);a[E]|=b}function F(a,b){Bb||E in a||Kb(a,Jb);a[E]=b};function Ob(){return typeof BigInt==="function"};var Pb={};function G(a,b){return b===void 0?a.g!==Qb&&!!(2&(a.j[E]|0)):!!(2&b)&&a.g!==Qb}var Qb={};function Rb(a,b){if(a!=null)if(typeof a==="string")a=a?new qb(a,ob):sb();else if(a.constructor!==qb)if(ib&&a!=null&&a instanceof Uint8Array)a=a.length?new qb(new Uint8Array(a),ob):sb();else{if(!b)throw Error();a=void 0}return a}var Sb=Object.freeze({});function Tb(a,b,c){var d=b&128?0:-1,e=a.length,g;if(g=!!e)g=a[e-1],g=g!=null&&typeof g==="object"&&g.constructor===Object;var f=e+(g?-1:0);for(b=b&128?1:0;b<f;b++)c(b-d,a[b]);if(g){a=a[e-1];for(var h in a)Object.prototype.hasOwnProperty.call(a,h)&&!isNaN(h)&&c(+h,a[h])}}var Ub={};function Vb(a){a.Ia=!0;return a};var Wb=Vb(function(a){return typeof a==="number"}),Xb=Vb(function(a){return typeof a==="string"}),Yb=Vb(function(a){return typeof a==="boolean"});var Zb=typeof D.BigInt==="function"&&typeof D.BigInt(0)==="bigint";function $b(a){var b=a;if(Xb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Wb(b)&&!w(Number,"isSafeInteger").call(Number,b))throw Error(String(b));return Zb?BigInt(a):a=Yb(a)?a?"1":"0":Xb(a)?a.trim()||"0":String(a)}var fc=Vb(function(a){return Zb?a>=ac&&a<=bc:a[0]==="-"?cc(a,dc):cc(a,ec)}),dc=w(Number,"MIN_SAFE_INTEGER").toString(),ac=Zb?BigInt(w(Number,"MIN_SAFE_INTEGER")):void 0,ec=w(Number,"MAX_SAFE_INTEGER").toString(),bc=Zb?BigInt(w(Number,"MAX_SAFE_INTEGER")):void 0;function cc(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var hc=typeof Uint8Array.prototype.slice==="function",H=0,I=0;function ic(a){var b=a>>>0;H=b;I=(a-b)/4294967296>>>0}function jc(a){if(a<0){ic(-a);var b=A(kc(H,I));a=b.next().value;b=b.next().value;H=a>>>0;I=b>>>0}else ic(a)}function lc(a,b){var c=b&2147483648;c&&(a=~a+1>>>0,b=~b>>>0,a==0&&(b=b+1>>>0));var d=b*4294967296+(a>>>0);a=w(Number,"isSafeInteger").call(Number,d)?d:mc(a,b);return typeof a==="number"?c?-a:a:c?"-"+a:a}function mc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Ob()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+nc(c)+nc(a));return c}function nc(a){a=String(a);return"0000000".slice(a.length)+a}function oc(){var a=H,b=I;b&2147483648?Ob()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=A(kc(a,b)),a=b.next().value,b=b.next().value,a="-"+mc(a,b)):a=mc(a,b);return a}function pc(a){if(a.length<16)jc(Number(a));else if(Ob())a=BigInt(a),H=Number(a&BigInt(4294967295))>>>0,I=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");I=H=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),I*=1E6,H=H*1E6+d,H>=4294967296&&(I+=w(Math,"trunc").call(Math,H/4294967296),I>>>=0,H>>>=0);b&&(b=A(kc(H,I)),a=b.next().value,b=b.next().value,H=a,I=b)}}function kc(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var qc=typeof BigInt==="function"?BigInt.asIntN:void 0,rc=w(Number,"isSafeInteger"),sc=w(Number,"isFinite"),tc=w(Math,"trunc"),uc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function vc(a){switch(typeof a){case "bigint":return!0;case "number":return sc(a);case "string":return uc.test(a);default:return!1}}function wc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return sc(a)?a|0:void 0}function xc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return sc(a)?a>>>0:void 0}function yc(a){var b=0;b=b===void 0?0:b;if(!vc(a))throw zb("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return zc(a);case "bigint":return String(qc(64,a));default:return Ac(a)}case 1024:switch(c){case "string":return b=tc(Number(a)),rc(b)?a=$b(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Ob()?$b(qc(64,BigInt(a))):$b(Bc(a))),a;case "bigint":return $b(qc(64,a));default:return rc(a)?$b(Cc(a)):$b(Ac(a))}case 0:switch(c){case "string":return zc(a);case "bigint":return $b(qc(64,a));default:return Cc(a)}default:throw Error("Unknown format requested type for int64");}}function Dc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Bc(a){if(Dc(a))return a;pc(a);return oc()}function Cc(a){a=tc(a);rc(a)||(jc(a),a=lc(H,I));return a}function Ac(a){a=tc(a);if(rc(a))a=String(a);else{var b=String(a);Dc(b)?a=b:(jc(a),a=oc())}return a}function zc(a){var b=tc(Number(a));if(rc(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Bc(a)}function Ec(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(qc(64,a));if(vc(a)){if(b==="string")return zc(a);if(b==="number")return Cc(a)}}function Fc(a){return a==null||typeof a==="string"?a:void 0}function Gc(a,b,c){if(a!=null&&a[Ib]===Pb)return a;if(Array.isArray(a)){var d=a[E]|0;c=d|c&32|c&2;c!==d&&F(a,c);return new b(a)}};function Hc(a){return a};function Ic(a){var b=Ka(Eb);return b?a[b]:void 0}var Jc=function(){},Kc=function(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&!isNaN(c)&&b(a,+c,a[c])},Lc=function(a){var b=new Jc;Kc(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b};function Mc(a,b){b<100||Ab(Fb,1)};function Nc(a,b,c,d){var e=d!==void 0;d=!!d;var g=Ka(Eb),f;!e&&Bb&&g&&(f=a[g])&&Kc(f,Mc);g=[];var h=a.length;f=4294967295;var k=!1,l=!!(b&64),n=l?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,f=h):p=void 0;if(l&&!(b&128)&&!e){k=!0;var r;f=((r=Oc)!=null?r:Hc)(f-n,n,a,p,void 0)+n}}b=void 0;for(r=0;r<h;r++){var t=a[r];if(t!=null&&(t=c(t,d))!=null)if(l&&r>=f){var u=r-n,v=void 0;((v=b)!=null?v:b={})[u]=t}else g[r]=t}if(p)for(var x in p)Object.prototype.hasOwnProperty.call(p,x)&&(h=p[x],h!=null&&(h=c(h,d))!=null&&(r=+x,t=void 0,l&&!w(Number,"isNaN").call(Number,r)&&(t=r+n)<f?g[t]=h:(r=void 0,((r=b)!=null?r:b={})[x]=h)));b&&(k?g.push(b):g[f]=b);e&&Ka(Eb)&&(a=Ic(a))&&a instanceof Jc&&(g[Eb]=Lc(a));return g}function Pc(a){switch(typeof a){case "number":return w(Number,"isFinite").call(Number,a)?a:""+a;case "bigint":return fc(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[E]|0;return a.length===0&&b&1?void 0:Nc(a,b,Pc)}if(a!=null&&a[Ib]===Pb)return Qc(a);if(a instanceof qb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(jb){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):b);b=btoa(c)}else b=fb(b);a=a.g=b}return a}return}return a}var Oc;function Qc(a){a=a.j;return Nc(a,a[E]|0,Pc)};var Rc,Sc;function Tc(a){switch(typeof a){case "boolean":return Rc||(Rc=[0,void 0,!0]);case "number":return a>0?void 0:a===0?Sc||(Sc=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}function Uc(a,b,c){return a=J(a,b[0],b[1],c?1:2)}function J(a,b,c,d){d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[E]|0;if(Va&&1&e)throw Error("rfarr");2048&e&&!(2&e)&&Vc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||F(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var g=c.length;if(g){var f=g-1,h=c[f];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;f-=b;if(f>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(g=+k,g<f&&(c[g+b]=h[k],delete h[k]));e=e&-8380417|(f&1023)<<13;break a}}if(b){k=Math.max(b,g-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);F(a,e);return a}function Vc(){if(Va)throw Error("carr");Ab(Hb,5)};function Wc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[E]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Xc(a,c,!1,b&&!(c&16)):(Nb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Ib]===Pb)return b=a.j,c=b[E]|0,G(a,c)?a:Yc(a,b,c)?Zc(a,b):Xc(b,c);if(a instanceof qb)return a}function Zc(a,b,c){a=new a.constructor(b);c&&(a.g=Qb);a.i=Qb;return a}function Xc(a,b,c,d){d!=null||(d=!!(34&b));a=Nc(a,b,Wc,d);d=32;c&&(d|=2);b=b&8380609|d;F(a,b);return a}function $c(a){var b=a.j,c=b[E]|0;return G(a,c)?Yc(a,b,c)?Zc(a,b,!0):new a.constructor(Xc(b,c,!1)):a}function ad(a){if(a.g!==Qb)return!1;var b=a.j;b=Xc(b,b[E]|0);Nb(b,2048);a.j=b;a.g=void 0;a.i=void 0;return!0}function bd(a){if(!ad(a)&&G(a,a.j[E]|0))throw Error();}function cd(a,b){b===void 0&&(b=a[E]|0);b&32&&!(b&4096)&&F(a,b|4096)}function Yc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(F(b,c|2),a.g=Qb,!0):!1};var ed=function(a,b,c){a=dd(a.j,b,void 0,c);if(a!==null)return a},dd=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),g=a.length-1;if(!(g<1+(c?0:-1))){if(e>=g){var f=a[g];if(f!=null&&typeof f==="object"&&f.constructor===Object){c=f[b];var h=!0}else if(e===g)c=f;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!w(Object,"is").call(Object,d,c))return h?f[b]=d:a[e]=d,d}return c}};function fd(a,b,c,d,e){var g=c+(e?0:-1),f=a.length-1;if(f>=1+(e?0:-1)&&g>=f){var h=a[f];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(g<=f)return a[g]=d,b;if(d!==void 0){var k;f=((k=b)!=null?k:b=a[E]|0)>>13&1023||536870912;c>=f?d!=null&&(g={},a[f+(e?0:-1)]=(g[c]=d,g)):a[g]=d}return b}function gd(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function hd(a){return Rb(a,!0)}var L=function(a,b){a=ed(a,b,hd);return a==null?sb():a};function id(a,b,c,d){bd(a);var e=a.j;fd(e,e[E]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}var jd=function(a,b,c){var d=a[E]|0,e=d&128?Ub:void 0,g=dd(a,c,e);if(g!=null&&g[Ib]===Pb){if(!G(g))return ad(g),g.j;var f=g.j}else Array.isArray(g)&&(f=g);if(f){var h=f[E]|0;h&2&&(f=Xc(f,h))}f=Uc(f,b,!0);f!==g&&fd(a,d,c,f,e);return f};function kd(a,b,c,d){var e=!1;d=dd(a,d,void 0,function(g){var f=Gc(g,c,b);e=f!==g&&f!=null;return f});if(d!=null)return e&&!G(d)&&cd(a,b),d}var M=function(a,b,c){var d=a.j,e=d[E]|0;b=kd(d,e,b,c);if(b==null)return b;e=d[E]|0;if(!G(a,e)){var g=$c(b);g!==b&&(ad(a)&&(d=a.j,e=d[E]|0),b=g,e=fd(d,e,c,b),cd(d,e))}return b},ld=function(a,b,c){c==null&&(c=void 0);var d=c;bd(a);var e=a.j;fd(e,e[E]|0,b,d);c&&!G(c)&&cd(a.j);return a};function md(a,b){return a=(2&b?a|2:a&-3)&-273}var N=function(a,b){var c=c===void 0?0:c;a=xc(ed(a,b));return a!=null?a:c},O=function(a,b){var c=c===void 0?"":c;a=Fc(ed(a,b));return a!=null?a:c},P=function(a,b){var c=c===void 0?0:c;a=ed(a,b);a=a==null?a:sc(a)?a|0:void 0;return a!=null?a:c},nd=function(a,b,c){if(c!=null){if(typeof c!=="number")throw zb("uint32");if(!sc(c))throw zb("uint32");c>>>=0}return id(a,b,c,0)},od=function(a,b,c){c=c==null?c:yc(c);return id(a,b,c,"0")},pd=function(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return id(a,b,c,"")},Q=function(a,b,c){return id(a,b,Rb(c,!1),sb())},qd=function(a,b,c){if(c!=null){if(!sc(c))throw zb("enum");c|=0}return id(a,b,c,0)};var rd=function(a,b,c){this.buffer=a;if(c&&!b)throw Error();this.g=b};function sd(a,b){if(typeof a==="string")return new rd(nb(a),b);if(Array.isArray(a))return new rd(new Uint8Array(a),b);if(a.constructor===Uint8Array)return new rd(a,!1);if(a.constructor===ArrayBuffer)return a=new Uint8Array(a),new rd(a,!1);if(a.constructor===qb)return b=tb(a)||new Uint8Array(0),new rd(b,!0,a);if(a instanceof Uint8Array)return a=a.constructor===Uint8Array?a:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),new rd(a,!1);throw Error();};var td=function(a,b,c,d){this.h=null;this.u=!1;this.g=this.i=this.l=0;this.init(a,b,c,d)};td.prototype.init=function(a,b,c,d){var e=d===void 0?{}:d;d=e.X===void 0?!1:e.X;e=e.ba===void 0?!1:e.ba;this.X=d;this.ba=e;a&&(a=sd(a,this.ba),this.h=a.buffer,this.u=a.g,this.l=b||0,this.i=c!==void 0?this.l+c:this.h.length,this.g=this.l)};td.prototype.clear=function(){this.h=null;this.u=!1;this.g=this.i=this.l=0;this.X=!1};var ud=function(a,b){a.g=b;if(b>a.i)throw Error();},vd=function(a){var b=a.h,c=a.g,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw Error();ud(a,c);return e},wd=function(a,b){if(b<0)throw Error();var c=a.g;b=c+b;if(b>a.i)throw Error();a.g=b;return c},xd=function(a,b){if(b==0)return sb();var c=wd(a,b);a.X&&a.u?c=a.h.subarray(c,c+b):(a=a.h,b=c+b,c=c===b?new Uint8Array(0):hc?a.slice(c,b):new Uint8Array(a.subarray(c,b)));return c.length==0?sb():new qb(c,ob)},yd=[];var Ad=function(a,b,c,d){if(yd.length){var e=yd.pop();e.init(a,b,c,d);a=e}else a=new td(a,b,c,d);this.g=a;this.l=this.g.g;this.h=this.i=-1;zd(this,d)},zd=function(a,b){b=b===void 0?{}:b;a.fa=b.fa===void 0?!1:b.fa},Cd=function(a,b,c,d){if(Bd.length){var e=Bd.pop();zd(e,d);e.g.init(a,b,c,d);return e}return new Ad(a,b,c,d)},Dd=function(a){a.g.clear();a.i=-1;a.h=-1;Bd.length<100&&Bd.push(a)},Ed=function(a){var b=a.g;if(b.g==b.i)return!1;a.l=a.g.g;var c=vd(a.g)>>>0;b=c>>>3;c&=7;if(!(c>=0&&c<=5))throw Error();if(b<1)throw Error();a.i=b;a.h=c;return!0},Fd=function(a){switch(a.h){case 0:if(a.h!=0)Fd(a);else a:{a=a.g;for(var b=a.g,c=b+10,d=a.h;b<c;)if((d[b++]&128)===0){ud(a,b);break a}throw Error();}break;case 1:a=a.g;ud(a,a.g+8);break;case 2:a.h!=2?Fd(a):(b=vd(a.g)>>>0,a=a.g,ud(a,a.g+b));break;case 5:a=a.g;ud(a,a.g+4);break;case 3:b=a.i;do{if(!Ed(a))throw Error();if(a.h==4){if(a.i!=b)throw Error();break}Fd(a)}while(1);break;default:throw Error();}},Gd=function(a,b,c){var d=a.g.i,e=vd(a.g)>>>0;e=a.g.g+e;var g=e-d;g<=0&&(a.g.i=e,c(b,a,void 0,void 0,void 0),g=e-a.g.g);if(g)throw Error();a.g.g=e;a.g.i=d},Bd=[];var Hd=function(a,b){this.h=a>>>0;this.g=b>>>0},Jd=function(a){if(!a)return Id||(Id=new Hd(0,0));if(!/^-?\d+$/.test(a))return null;pc(a);return new Hd(H,I)},Id;var Kd=function(){this.g=[]};Kd.prototype.length=function(){return this.g.length};Kd.prototype.end=function(){var a=this.g;this.g=[];return a};var Ld=function(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)},Md=function(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)};var Nd=function(){this.i=[];this.h=0;this.g=new Kd},Od=function(a,b){b.length!==0&&(a.i.push(b),a.h+=b.length)},Pd=function(a,b){Od(a,a.g.end());Od(a,b)},Qd=function(a,b){Md(a.g,b*8+2);b=a.g.end();Od(a,b);b.push(a.h);return b},Rd=function(a,b){var c=b.pop();for(c=a.h+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.h++;b.push(c);a.h++},Sd=function(a){Od(a,a.g.end());for(var b=new Uint8Array(a.h),c=a.i,d=c.length,e=0,g=0;g<d;g++){var f=c[g];b.set(f,e);e+=f.length}a.i=[b];return b},Td=function(a,b,c,d){c!=null&&(b=Qd(a,b),d(c,a),Rd(a,b))},Vd=function(a,b,c){var d=Ud;if(c!=null)for(var e=0;e<c.length;e++){var g=Qd(a,b);d(c[e],a);Rd(a,g)}};function Wd(){var a=function(){throw Error();};w(Object,"setPrototypeOf").call(Object,a,a.prototype);return a}var Xd=Wd(),Yd=Wd(),Zd=Wd(),$d=Wd(),ae=Wd(),be=Wd();var R=function(a,b,c){this.j=J(a,b,c)};R.prototype.toJSON=function(){return Qc(this)};R.prototype[Ib]=Pb;R.prototype.toString=function(){return this.j.toString()};var ce=function(a,b,c){this.g=a;this.h=b;a=Ka(Xd);this.i=!!a&&c===a||!1};function de(a){var b=ee;var c=c===void 0?Xd:c;return new ce(a,b,c)}function ee(a,b,c,d,e){b=b instanceof R?b.j:Array.isArray(b)?Uc(b,d,!1):void 0;Td(a,c,b,e)}var fe=de(function(a,b,c,d,e){if(a.h!==2)return!1;Gd(a,jd(b,d,c),e);return!0}),ge=de(function(a,b,c,d,e){if(a.h!==2)return!1;Gd(a,jd(b,d,c),e);return!0}),he=(0,q.Symbol)(),ie=(0,q.Symbol)(),je=(0,q.Symbol)(),ke=(0,q.Symbol)(),le=(0,q.Symbol)(),me,ne;function oe(a,b,c,d){var e=d[a];if(e)return e;e={};e.wa=d;e.V=Tc(d[0]);var g=d[1],f=1;g&&g.constructor===Object&&(e.ia=g,g=d[++f],typeof g==="function"&&(e.la=!0,me!=null||(me=g),ne!=null||(ne=d[f+1]),g=d[f+=2]));for(var h={};g&&Array.isArray(g)&&g.length&&typeof g[0]==="number"&&g[0]>0;){for(var k=0;k<g.length;k++)h[g[k]]=g;g=d[++f]}for(k=1;g!==void 0;){typeof g==="number"&&(k+=g,g=d[++f]);var l=void 0;if(g instanceof ce)var n=g;else n=fe,f--;g=void 0;if((g=n)==null?0:g.i){g=d[++f];l=d;var p=f;typeof g==="function"&&(g=g(),l[p]=g);l=g}g=d[++f];p=k+1;typeof g==="number"&&g<0&&(p-=g,g=d[++f]);for(;k<p;k++){var r=h[k];l?c(e,k,n,l,r):b(e,k,n,r)}}return d[a]=e}function pe(a){return Array.isArray(a)?a[0]instanceof ce?a:[ge,a]:[a,void 0]};function qe(a,b,c,d){var e=c.g;a[b]=d?function(g,f,h){return e(g,f,h,d)}:e}function re(a,b,c,d,e){var g=c.g,f,h;a[b]=function(k,l,n){return g(k,l,n,h||(h=oe(ie,qe,re,d).V),f||(f=se(d)),e)}}function se(a){var b=a[je];if(b!=null)return b;var c=oe(ie,qe,re,a);b=c.la?function(d,e){return me(d,e,c)}:function(d,e){for(;Ed(e)&&e.h!=4;){var g=e.i,f=c[g];if(f==null){var h=c.ia;h&&(h=h[g])&&(h=te(h),h!=null&&(f=c[g]=h))}if(f==null||!f(e,d,g)){h=e;f=h.l;Fd(h);if(h.fa)var k=void 0;else{var l=h.g.g-f;h.g.g=f;k=xd(h.g,l)}l=h=f=void 0;var n=d;k&&((f=(h=(l=n[Eb])!=null?l:n[Eb]=new Jc)[g])!=null?f:h[g]=[]).push(k)}}if(d=Ic(d))d.g=c.wa[le];return!0};a[je]=b;a[le]=ue.bind(a);return b}function ue(a,b,c,d){var e=this[ie],g=this[je],f=Uc(void 0,e.V,!1),h=Ic(a);if(h){var k=!1,l=e.ia;if(l){e=function(u,v,x){if(x.length!==0)if(l[v])for(u=A(x),v=u.next();!v.done;v=u.next()){v=Cd(v.value);try{k=!0,g(f,v)}finally{Dd(v)}}else d==null||d(a,v,x)};if(b==null)Kc(h,e);else if(h!=null){var n=h[b];n&&e(h,b,n)}if(k){var p=a[E]|0;if(p&2&&p&2048&&(c==null||!c.Ma))throw Error();var r=p&128?Ub:void 0,t=function(u,v){if(dd(a,u,r)!=null)switch(c==null?void 0:c.La){case 1:return;default:throw Error();}v!=null&&(p=fd(a,p,u,v,r));delete h[u]};b==null?Tb(f,f[E]|0,function(u,v){t(u,v)}):t(b,dd(f,b,r))}}}}function te(a){a=pe(a);var b=a[0].g;if(a=a[1]){var c=se(a),d=oe(ie,qe,re,a).V;return function(e,g,f){return b(e,g,f,d,c)}}return b};function ve(a,b,c){a[b]=c.h}function we(a,b,c,d){var e,g,f=c.h;a[b]=function(h,k,l){return f(h,k,l,g||(g=oe(he,ve,we,d).V),e||(e=xe(d)))}}function xe(a){var b=a[ke];if(!b){var c=oe(he,ve,we,a);b=function(d,e){return ye(d,e,c)};a[ke]=b}return b}function ye(a,b,c){Tb(a,a[E]|0,function(d,e){if(e!=null){var g=ze(c,d);g?g(b,e,d):d<500||Ab(Gb,3)}});(a=Ic(a))&&Kc(a,function(d,e,g){Od(b,b.g.end());for(d=0;d<g.length;d++)Od(b,tb(g[d])||new Uint8Array(0))})}function ze(a,b){var c=a[b];if(c)return c;if(c=a.ia)if(c=c[b]){c=pe(c);var d=c[0].h;if(c=c[1]){var e=xe(c),g=oe(he,ve,we,c).V;c=a.la?ne(g,e):function(f,h,k){return d(f,h,k,g,e)}}else c=d;return a[b]=c}};var Ae=function(a,b){var c=new Nd;ye(a.j,c,oe(he,ve,we,b));return Sd(c)};function Be(a,b,c){return new ce(a,b,c)}function Ce(a,b,c){fd(a,a[E]|0,b,c,(a[E]|0)&128?Ub:void 0)}var De=Be(function(a,b,c){if(a.h!==0)return!1;a=a.g;var d=0,e=0,g=0,f=a.h,h=a.g;do{var k=f[h++];d|=(k&127)<<g;g+=7}while(g<32&&k&128);if(g>32)for(e|=(k&127)>>4,g=3;g<32&&k&128;g+=7)k=f[h++],e|=(k&127)<<g;ud(a,h);if(k&128)throw Error();a=lc(d>>>0,e>>>0);Ce(b,c,a===0?void 0:a);return!0},function(a,b,c){b=Ec(b);if(b!=null){switch(typeof b){case "string":Jd(b)}if(b!=null)switch(Md(a.g,c*8),typeof b){case "number":a=a.g;jc(b);Ld(a,H,I);break;case "bigint":c=BigInt.asUintN(64,b);c=new Hd(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));Ld(a.g,c.h,c.g);break;default:c=Jd(b),Ld(a.g,c.h,c.g)}}},$d),Ee=Be(function(a,b,c){if(a.h!==2)return!1;var d,e=vd(a.g)>>>0;a=a.g;var g=wd(a,e);a=a.h;if(Pa){var f=a;(d=Oa)||(d=Oa=new TextDecoder("utf-8",{fatal:!0}));e=g+e;f=g===0&&e===f.length?f:f.subarray(g,e);try{var h=d.decode(f)}catch(p){if(Na===void 0){try{d.decode(new Uint8Array([128]))}catch(r){}try{d.decode(new Uint8Array([97])),Na=!0}catch(r){Na=!1}}!Na&&(Oa=void 0);throw p;}}else{h=g;e=h+e;g=[];for(var k=null,l,n;h<e;)l=a[h++],l<128?g.push(l):l<224?h>=e?La():(n=a[h++],l<194||(n&192)!==128?(h--,La()):g.push((l&31)<<6|n&63)):l<240?h>=e-1?La():(n=a[h++],(n&192)!==128||l===224&&n<160||l===237&&n>=160||((d=a[h++])&192)!==128?(h--,La()):g.push((l&15)<<12|(n&63)<<6|d&63)):l<=244?h>=e-2?La():(n=a[h++],(n&192)!==128||(l<<28)+(n-144)>>30!==0||((d=a[h++])&192)!==128||((f=a[h++])&192)!==128?(h--,La()):(l=(l&7)<<18|(n&63)<<12|(d&63)<<6|f&63,l-=65536,g.push((l>>10&1023)+55296,(l&1023)+56320))):La(),g.length>=8192&&(k=Ma(k,g),g.length=0);h=Ma(k,g)}d=h;Ce(b,c,d===""?void 0:d);return!0},function(a,b,c){b=Fc(b);if(b!=null){var d=!1;d=d===void 0?!1:d;if(Sa){if(d&&(Ra?!b.isWellFormed():/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(b)))throw Error("Found an unpaired surrogate");b=(Qa||(Qa=new TextEncoder)).encode(b)}else{for(var e=0,g=new Uint8Array(3*b.length),f=0;f<b.length;f++){var h=b.charCodeAt(f);if(h<128)g[e++]=h;else{if(h<2048)g[e++]=h>>6|192;else{if(h>=55296&&h<=57343){if(h<=56319&&f<b.length){var k=b.charCodeAt(++f);if(k>=56320&&k<=57343){h=(h-55296)*1024+k-56320+65536;g[e++]=h>>18|240;g[e++]=h>>12&63|128;g[e++]=h>>6&63|128;g[e++]=h&63|128;continue}else f--}if(d)throw Error("Found an unpaired surrogate");h=65533}g[e++]=h>>12|224;g[e++]=h>>6&63|128}g[e++]=h&63|128}}b=e===g.length?g:g.subarray(0,e)}Md(a.g,c*8+2);Md(a.g,b.length);Pd(a,b)}},Yd),Fe=Be(function(a,b,c){if(a.h!==2)return!1;var d=vd(a.g)>>>0;a=xd(a.g,d);Ce(b,c,a===sb()?void 0:a);return!0},function(a,b,c){b=b==null||typeof b=="string"||b instanceof qb?b:void 0;b!=null&&(b=sd(b,!0).buffer,Md(a.g,c*8+2),Md(a.g,b.length),Pd(a,b))},ae),S=Be(function(a,b,c){if(a.h!==0)return!1;a=vd(a.g)>>>0;Ce(b,c,a===0?void 0:a);return!0},function(a,b,c){b=xc(b);b!=null&&b!=null&&(Md(a.g,c*8),Md(a.g,b))},Zd),Ge=Be(function(a,b,c){if(a.h!==0)return!1;a=vd(a.g);Ce(b,c,a===0?void 0:a);return!0},function(a,b,c){b=wc(b);if(b!=null)if(b=parseInt(b,10),Md(a.g,c*8),a=a.g,c=b,c>=0)Md(a,c);else{for(b=0;b<9;b++)a.g.push(c&127|128),c>>=7;a.g.push(1)}},be);function He(a,b){return function(c,d){var e={ba:!0};d&&w(Object,"assign").call(Object,e,d);c=Cd(c,void 0,void 0,e);try{var g=new a,f=g.j;se(b)(f,c);var h=g}finally{Dd(c)}return h}}function Ie(a){return function(b){return Ae(b,a)}};var Je=function(a){this.j=J(a)};z(Je,R);var Ke=Ie([0,Ge,Ee]);var Le=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function Me(a){var b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);for(var d;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(e){b=c;break a}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ne;var Oe=function(a){this.g=a};Oe.prototype.toString=function(){return this.g+""};function Pe(a){var b;Ne===void 0&&(Ne=null);a=(b=Ne)?b.createScriptURL(a):a;return new Oe(a)};var Qe=function(){return"border".replace(/\-([a-z])/g,function(a,b){return b.toUpperCase()})},Re=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};function Se(a){var b=Ia.apply(1,arguments);if(b.length===0)return Pe(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Pe(c)};var Te=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Ue=function(a){a=a===void 0?document:a;return a.createElement("img")};var Ve={};function We(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Ue(a.document);if(c){var g=function(){if(c){var f=a.google_image_requests,h=Array.prototype.indexOf.call(f,e,void 0);h>=0&&Array.prototype.splice.call(f,h,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",g,!1);typeof e.removeEventListener==="function"&&e.removeEventListener("error",g,!1)};typeof e.addEventListener==="function"&&e.addEventListener("load",g,!1);typeof e.addEventListener==="function"&&e.addEventListener("error",g,!1)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}function Xe(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Te(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Ye(c,b)}function Ye(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):We(c,a,b===void 0?!1:b,d===void 0?!1:d)};function Ze(a,b){var c=new Nd;try{var d=a.filter(function(g){return g.ma}).map($e);Vd(c,1,d);Td(c,2,Ke(b),Ud);var e=a.filter(function(g){return!g.ma}).map($e);Vd(c,3,e)}catch(g){af(g,b)}return Sd(c)}function af(a,b){try{Xe({m:Me(a instanceof Error?a:Error(String(a))),b:P(b,1)||null,v:O(b,2)||null})}catch(c){}}function $e(a){var b=new Nd;Td(b,a.Ha,a.Ea,Ud);return Sd(b)}function Ud(a,b){Pd(b,a.subarray(0,a.length))}var bf=function(a,b){var c=new Je;a=qd(c,1,a);b=pd(a,2,b);a=b.j;c=a[E]|0;this.g=G(b,c)?b:Yc(b,a,c)?Zc(b,a):new b.constructor(Xc(a,c,!0))};var cf=function(a){this.j=J(a)};z(cf,R);var df=function(a,b){return od(a,1,b)},ef=function(a,b){return od(a,2,b)},ff=function(a,b){return qd(a,3,b)};var gf=Ie([0,De,-1,Ge]);var hf=function(){bf.apply(this,arguments)};z(hf,bf);var jf=function(){hf.apply(this,arguments)};z(jf,hf);jf.prototype.l=function(){this.u.apply(this,qa(Ia.apply(0,arguments).map(function(a){return{ma:!0,Ha:16,Ea:gf(a)}})))};var kf=function(a,b,c,d){jf.call(this,a,b);this.i=c;this.h=d};z(kf,jf);kf.prototype.u=function(){var a=Ia.apply(0,arguments);try{var b=encodeURIComponent(fb(Ze(a,this.g),3));this.h(this.i+"?e=4&d="+b)}catch(c){af(c,this.g)}};var lf=function(a,b){this.data=a;this.g=b};var mf=function(a){this.g=a},of=function(a,b,c){c=c===void 0?[]:c;var d=nf();a.g.postMessage(b,[d.port2].concat(c))},qf=function(a,b){pf(a,b);return new mf(a)},nf=function(a){var b=new MessageChannel;pf(b.port1,a);return b},pf=function(a,b){b&&(a.onmessage=function(c){var d=c.data;c=qf(c.ports[0]);b(new lf(d,c))})};var rf=function(a){var b=a.destination;var c=a.origin;var d=a.Ga===void 0?void 0:a.Ga;var e=a.ja===void 0?"ZNWN1d":a.ja;a=a.onMessage===void 0?void 0:a.onMessage;if(c==="*")throw Error("Sending to wildcard origin not allowed.");var g=nf(a),f={};d=d?(f.n=e,f.t=d,f):e;b.postMessage(d,c,[g.port2]);qf(g.port1,a)};var sf=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)"),tf=function(a,b){this.g=a;this.h=b},uf=function(a,b){this.url=a;this.g=!!b;this.depth=null};var vf=function(){this.i="&";this.h={};this.l=0;this.g=[]},wf=function(a,b){var c={};c[a]=b;return[c]},yf=function(a,b,c,d,e){var g=[];Te(a,function(f,h){(f=xf(f,b,c,d,e))&&g.push(h+"="+f)});return g.join(b)},xf=function(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){for(var g=[],f=0;f<a.length;f++)g.push(xf(a[f],b,c,d+1,e));return g.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(yf(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))},Gf=function(a,b){var c="https://pagead2.googlesyndication.com"+b,d=Df(a)-b.length;if(d<0)return"";a.g.sort(function(n,p){return n-p});b=null;for(var e="",g=0;g<a.g.length;g++)for(var f=a.g[g],h=a.h[f],k=0;k<h.length;k++){if(!d){b=b==null?f:b;break}var l=yf(h[k],a.i,",$");if(l){l=e+l;if(d>=l.length){d-=l.length;c+=l;e=a.i;break}b=b==null?f:b}}a="";b!=null&&(a=e+"trn="+b);return c+a},Df=function(a){var b=1,c;for(c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1};var Hf=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");var If=function(){this.h=this.g},Jf=function(a,b){try{var c=b()}catch(g){b=!1;try{b=a.h(987,new Le(g,{message:Me(g)}),void 0,void 0)}catch(f){a.g(217,f)}if(b){var d,e;(d=window.console)==null||(e=d.error)==null||e.call(d,g)}else throw g;}return c},Kf=function(a,b){return function(){var c=Ia.apply(0,arguments);return Jf(a,function(){return b.apply(void 0,c)})}};If.prototype.g=function(a,b,c,d,e){e=e||"topics_frame_error";var g=void 0;try{var f=new vf;f.g.push(1);f.h[1]=wf("context",a);b.error&&b.meta&&b.id||(b=new Le(b,{message:Me(b)}));if(b.msg){var h=b.msg.substring(0,512);f.g.push(2);f.h[2]=wf("msg",h)}var k=b.meta||{};if(d)try{d(k)}catch(vb){}d=[k];f.g.push(3);f.h[3]=d;var l;if(!(l=x)){h=D;d=[];k=null;do{var n=h;try{var p;if(p=!!n&&n.location.href!=null)b:{try{$a(n.foo);p=!0;break b}catch(vb){}p=!1}var r=p}catch(vb){r=!1}if(r){var t=n.location.href;k=n.document&&n.document.referrer||null}else t=k,k=null;d.push(new uf(t||""));try{h=n.parent}catch(vb){h=null}}while(h&&n!==h);t=0;for(var u=d.length-1;t<=u;++t)d[t].depth=u-t;n=D;if(n.location&&n.location.ancestorOrigins&&n.location.ancestorOrigins.length===d.length-1)for(u=1;u<d.length;++u){var v=d[u];v.url||(v.url=n.location.ancestorOrigins[u-1]||"",v.g=!0)}l=d}var x=l;var K=new uf(D.location.href,!1);l=null;var ra=x.length-1;for(n=ra;n>=0;--n){var ha=x[n];!l&&sf.test(ha.url)&&(l=ha);if(ha.url&&!ha.g){K=ha;break}}ha=null;var wb=x.length&&x[ra].url;K.depth!==0&&wb&&(ha=x[ra]);g=new tf(K,ha);if(g.h){var xb=g.h.url||"";f.g.push(4);f.h[4]=wf("top",xb)}var Ea={url:g.g.url||""};if(g.g.url){var sa=g.g.url.match(Hf),zf=sa[1],Af=sa[3],Bf=sa[4];x="";zf&&(x+=zf+":");Af&&(x+="//",x+=Af,Bf&&(x+=":"+Bf));var Cf=x}else Cf="";Ea=[Ea,{url:Cf}];f.g.push(5);f.h[5]=Ea;Lf(e,f,c)}catch(vb){try{var Ef,Ff;Lf(e,{context:"ecmserr",rctx:a,msg:Me(vb),url:(Ff=(Ef=g)==null?void 0:Ef.g.url)!=null?Ff:""},c)}catch(xj){}}return!1};var Mf=function(a,b,c){c.catch(function(d){d=d?d:"unknown rejection";a.g(b,d instanceof Error?d:Error(d),void 0,void 0)})};var Lf=function(a,b,c){if(Math.random()<(c||.01))try{if(b instanceof vf)var d=b;else d=new vf,Te(b,function(g,f){var h=d,k=h.l++;g=wf(f,g);h.g.push(k);h.h[k]=g});var e=Gf(d,"/pagead/gen_204?id="+a+"&");e&&We(D,e,!1,!1)}catch(g){}};var Nf=ua(["https://securepubads.g.doubleclick.net/pagead/js/car.js"]),Of=ua(["https://securepubads.g.doubleclick.net/pagead/js/cocar.js"]),Pf=ua(["https://ep3.adtrafficquality.google/ivt/worklet/caw.js"]),Qf=Se(Nf);Se(Of);Se(Pf);function Rf(a,b){for(var c=[],d=0;d<8;++d){var e=new kf(7,"","https://pagead2.googlesyndication.com/pagead/ping",function(f){c.push({url:f})}),g=ff(ef(df(new cf,a),d),b);e.l(g)}return c}function Sf(a,b,c){var d=window,e=void 0;e=e===void 0?Qf:e;var g,f;return C(function(h){switch(h.h){case 1:g=d;if(g.sharedStorage){var k=k===void 0?D:k;k=k.performance;k=g.sharedStorage.set("ps_cct",String(k&&k.now&&k.timing?Math.floor(k.now()+k.timing.navigationStart):Date.now()),{ignoreIfPresent:!0});h=B(h,k,2)}else h=h.return();return h;case 2:return B(h,g.sharedStorage.worklet.addModule(e.toString()),3);case 3:return B(h,g.sharedStorage.selectURL("ps_caus",Rf(a,b),c?{resolveToConfig:!0,savedQuery:"ps_cac"}:{resolveToConfig:!0}),4);case 4:f=h.g;k=g.document.body;var l=document.createElement("fencedframe");l.id="ps_caff";l.name="ps_caff";l.mode="opaque-ads";l.config=f;var n=Ve.border;if(!n){var p=Qe();n=p;l.style[p]===void 0&&(p=(bb?"Webkit":ab?"Moz":null)+Re(p),l.style[p]!==void 0&&(n=p));Ve.border=n}n&&(l.style[n]="0");k.appendChild(l);h.h=0}})};var Tf=function(a){this.j=J(a)};z(Tf,R);Tf.prototype.o=function(){return O(this,2)};var Uf=function(a){this.j=J(a)};z(Uf,R);/*

 Copyright 2020 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Vf=function(){};var T=function(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);w(Object,"setPrototypeOf").call(Object,this,T.prototype)};z(T,Error);T.prototype.name="SecurityException";var Wf=function(a){this.j=J(a)};z(Wf,R);var Xf=function(a){this.j=J(a)};z(Xf,R);Xf.prototype.o=function(){return N(this,1)};var Yf=function(a){this.j=J(a)};z(Yf,R);var Zf=function(a){this.j=J(a)};z(Zf,R);Zf.prototype.o=function(){return N(this,1)};var $f=function(a){this.j=J(a)};z($f,R);$f.prototype.o=function(){return N(this,1)};var ag=[0,S];var bg=[0,Ge,S];var cg=He($f,[0,S,[0,S,ag,Fe],[0,S,bg,Fe]]);var dg=function(a){this.j=J(a)};z(dg,R);var eg=function(a){this.j=J(a)};z(eg,R);eg.prototype.o=function(){return N(this,3)};var fg=function(a){this.j=J(a)};z(fg,R);var gg=He(fg,[0,[0,ag,S],[0,bg,S,-1]]);var hg=function(a){this.j=J(a)};z(hg,R);hg.prototype.o=function(){return N(this,1)};var ig=He(hg,[0,S,1,Fe]);var jg=function(a){this.j=J(a)};z(jg,R);jg.prototype.o=function(){return N(this,3)};var kg=He(jg,[0,1,S,-1]);var lg=function(a){this.j=J(a)};z(lg,R);lg.prototype.getValue=function(){return L(this,2)};var mg=function(a){this.j=J(a)};z(mg,R);var ng=[0,1,[0,Ee,Fe,Ge]];var og=function(a){this.j=J(a)};z(og,R);var pg=[0,Ge,-1,8,Fe];var qg=function(a){this.j=J(a)};z(qg,R);var rg=[0,pg,ng,Ge];var sg=function(a){this.j=J(a)};z(sg,R);var tg=He(sg,[0,rg]);var ug=function(a){this.j=J(a)};z(ug,R);ug.prototype.o=function(){return N(this,1)};var vg=[0,S,rg,Fe,-1];var wg=function(a){this.j=J(a)};z(wg,R);wg.prototype.o=function(){return N(this,1)};var xg=He(wg,[0,S,vg,Fe]);var yg=He(ug,vg);var zg=function(a){this.j=J(a)};z(zg,R);zg.prototype.M=function(){return P(this,3)};var Ag=[0,Ge,-2];var Bg=function(a){this.j=J(a)};z(Bg,R);var Cg=[0,Ag];Bg.prototype.h=function(a){return function(){return Ae(this,a)}}(Cg);var Dg=He(Bg,Cg);var Eg=function(a){this.j=J(a)};z(Eg,R);Eg.prototype.o=function(){return N(this,1)};var Fg=[0,S,Ag,Fe];var Gg=function(a){this.j=J(a)};z(Gg,R);Gg.prototype.o=function(){return N(this,1)};var Hg=He(Gg,[0,S,Fg,Fe]);var Ig=He(Eg,Fg);var U=function(a){this.j=J(a)};z(U,R);U.prototype.getValue=function(){return L(this,2)};var Jg=function(a){this.j=J(a)};z(Jg,R);var Kg=function(a){this.j=J(a)};z(Kg,R);var Lg=function(a){var b=a.j,c=b,d=b[E]|0;b=void 0===Sb?2:4;var e=G(a,d),g=e?1:b;b=g===3;var f=!e;(g===2||f)&&ad(a)&&(c=a.j,d=c[E]|0);a=dd(c,2);e=Array.isArray(a)?a:Lb;var h=e===Lb?7:e[E]|0;a=h;2&d&&(a|=2);var k=a|1;if(a=!(4&k)){var l=e,n=d,p=!!(2&k);p&&(n|=2);for(var r=!p,t=!0,u=0,v=0;u<l.length;u++){var x=Gc(l[u],Jg,n);if(x instanceof Jg){if(!p){var K=G(x);r&&(r=!K);t&&(t=K)}l[v++]=x}}v<u&&(l.length=v);k|=4;k=t?k&-4097:k|4096;k=r?k|8:k&-9}k!==h&&(F(e,k),2&k&&Object.freeze(e));if(f&&!(8&k||!e.length&&(g===1||(g!==4?0:2&k||!(16&k)&&32&d)))){gd(k)&&(e=Array.prototype.slice.call(e),k=md(k,d),d=fd(c,d,2,e));f=e;h=k;for(l=0;l<f.length;l++)k=f[l],n=$c(k),k!==n&&(f[l]=n);h|=8;k=h=f.length?h|4096:h&-4097;F(e,k)}h=f=k;g===1||(g!==4?0:2&f||!(16&f)&&32&d)?gd(f)||(f|=!e.length||a&&!(4096&f)||32&d&&!(4096&f||16&f)?2:256,f!==h&&F(e,f),Object.freeze(e)):(g===2&&gd(f)&&(e=Array.prototype.slice.call(e),h=0,f=md(f,d),d=fd(c,d,2,e)),gd(f)||(b||(f|=16),f!==h&&F(e,f)));2&f||!(4096&f||16&f)||cd(c,d);return e},Mg=function(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Nb(b,32);b=new a(b)}return b}}(Kg);function V(a){return a==null?void 0:new Uint8Array(tb(a)||0)}function Ng(a){a==null?a=void 0:a=(a=tb(a))?a.length:0;return a};var W=function(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);w(Object,"setPrototypeOf").call(Object,this,W.prototype)};z(W,Error);W.prototype.name="InvalidArgumentsException";function X(){for(var a=0,b=0;b<arguments.length;b++)a+=arguments[b].length;a=new Uint8Array(a);for(var c=b=0;c<arguments.length;c++)a.set(arguments[c],b),b+=arguments[c].length;return a}function Og(a){if(w(Number,"isNaN").call(Number,a)||a%1!==0)throw new W("cannot convert non-integer value");if(a<0)throw new W("cannot convert negative number");if(a>w(Number,"MAX_SAFE_INTEGER"))throw new W("cannot convert number larger than "+w(Number,"MAX_SAFE_INTEGER"));var b=a%4294967296;a/=4294967296;for(var c=new Uint8Array(8),d=7;d>=4;d--)c[d]=b&255,b>>>=8;for(b=3;b>=0;b--)c[b]=a&255,a>>>=8;return c}function Pg(a){for(var b="",c=0;c<a.length;c++){var d=a[c].toString(16);b+=d.length>1?d:"0"+d}return b}function Y(a){return Qg(q.globalThis.atob(a.replace(/-/g,"+").replace(/_/g,"/")))}function Rg(a){for(var b="",c=0;c<a.length;c+=1)b+=String.fromCharCode(a[c]);return q.globalThis.btoa(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Qg(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);b[c++]=e}return new Uint8Array(b)};function Sg(a){switch(a){case 1:return"P-256";case 2:return"P-384";case 3:return"P-521"}}function Tg(a){switch(a){case "P-256":return 1;case "P-384":return 2;case "P-521":return 3}throw new W("unknown curve: "+a);}function Ug(a,b,c){a=Vg(Tg(a));switch(b){case 1:b=c.x;var d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=new Uint8Array(1+2*a);d=Y(d);b=Y(b);c.set(d,1+2*a-d.length);c.set(b,1+a-b.length);c[0]=4;return c;case 3:b=c.x;d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=Y(b);b=Y(d);c.length>a&&(c=c.slice(c.length-a,c.length));b.length>a&&(b=b.slice(b.length-a,b.length));d=new Uint8Array(2*a);d.set(c,0);d.set(b,a);return d;case 2:b=c.x;d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=Y(b);b=Y(d);c.length>a&&(c=c.slice(c.length-a,c.length));b.length>a&&(b=b.slice(b.length-a,b.length));d=new Uint8Array(1+a);d.set(c,1+a-c.length);d[0]=Wg(BigInt("0x"+Pg(b)),0)?3:2;return d;default:throw new T("invalid format");}}function Xg(a){switch(a){case 1:return BigInt("115792089210356248762697446949407573530086143415290314195533631308867097853951");case 2:return BigInt("39402006196394479212279040100143613805079739270465446667948293404245721771496870329047266088258938001861606973112319");case 3:return BigInt("6864797660130609714981900799081393217269435300143305409394463459185543183397656052122559640661454554977296311391480858037121987999716643812574028291115057151");default:throw new W("invalid curve");}}function Yg(a){a=a.toString(16);a=a.length%2===0?a:"0"+a;if(a.length%2!=0)throw new W("Hex string length must be multiple of 2");for(var b=new Uint8Array(a.length/2),c=0;c<a.length;c+=2)b[c/2]=parseInt(a.substring(c,c+2),16);return b}function Wg(a,b){return(a&BigInt(1)<<BigInt(b))!==BigInt(0)}function Zg(a,b,c){var d=Vg(Tg(a));switch(b){case 1:if(c.length!==1+2*d||c[0]!==4)throw new T("invalid point");return{kty:"EC",crv:a,x:Rg(new Uint8Array(c.subarray(1,1+d))),y:Rg(new Uint8Array(c.subarray(1+d,c.length))),ext:!0};case 3:if(c.length!==2*d)throw new T("invalid point");return{kty:"EC",crv:a,x:Rg(new Uint8Array(c.subarray(0,d))),y:Rg(new Uint8Array(c.subarray(d,c.length))),ext:!0};case 2:if(c.length!==1+d)throw new T("compressed point has wrong length");if(c[0]!==2&&c[0]!==3)throw new T("invalid format");b=c[0]===3;c=BigInt("0x"+Pg(c.subarray(1,c.length)));d=Xg(Tg(a));if(c<BigInt(0)||c>=d)throw new T("x is out of range");d=Xg(Tg(a));var e=d-BigInt(3);a:switch(Tg(a)){case 1:var g=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b");break a;case 2:g=BigInt("0xb3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef");break a;case 3:g=BigInt("0x051953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00");break a;default:throw new W("invalid curve");}if(d<=BigInt(0))throw new W("p must be positive");e=((c*c+e)*c+g)%d%d;if(Wg(d,0)&&Wg(d,1)){var f=d+BigInt(1)>>BigInt(2);if(f===BigInt(0))g=BigInt(1);else{g=e;f=f.toString(2);for(var h=1;h<f.length;++h)g=g*g%d,f[h]==="1"&&(g=g*e%d)}if(g*g%d!==e)throw new T("could not find a modular square root");e=g}else throw new W("unsupported modulus value");b!==Wg(e,0)&&(e=(d-e)%d);b=e;return{kty:"EC",crv:a,x:Rg(Yg(c)),y:Rg(Yg(b)),ext:!0};default:throw new T("invalid format");}}function Vg(a){switch(a){case 1:return 32;case 2:return 48;case 3:return 66}}function $g(a,b){var c,d,e,g,f;return C(function(h){if(h.h==1){c=a.algorithm;d=c.namedCurve;if(!d)throw new W("namedCurve must be provided");e=w(Object,"assign").call(Object,{},{"public":b},a.algorithm);g=8*Vg(Tg(d));return B(h,q.globalThis.crypto.subtle.deriveBits(e,a,g),2)}f=h.g;return h.return(new Uint8Array(f))})}function ah(a){var b,c;return C(function(d){if(d.h==1)return b={name:"ECDH",namedCurve:a},B(d,q.globalThis.crypto.subtle.generateKey(b,!0,["deriveKey","deriveBits"]),2);c=d.g;return d.return(c)})}function bh(a){var b;return C(function(c){if(c.h==1)return B(c,q.globalThis.crypto.subtle.exportKey("jwk",a),2);b=c.g;if(b.crv===void 0)throw new W("crv must be provided");var d=Vg(Tg(b.crv));if(b.x===void 0)throw new W("x must be provided");if(b.y===void 0)throw new W("y must be provided");var e=Y(b.x);if(e.length!==d)throw new W("x-coordinate byte-length is invalid (got: "+e.length+", want: "+d+").");e=Y(b.y);if(e.length!==d)throw new W("y-coordinate byte-length is invalid (got: "+e.length+", want: "+d+").");return c.return(b)})}function ch(a){var b,c,d;return C(function(e){if(e.h==1){b=a;c=b.crv;if(!c)throw new W("crv must be provided");return B(e,q.globalThis.crypto.subtle.importKey("jwk",a,{name:"ECDH",namedCurve:c},!0,[]),2)}d=e.g;return e.return(d)})}function dh(a){var b,c,d;return C(function(e){if(e.h==1){b=a;c=b.crv;if(!c)throw new W("crv must be provided");return B(e,q.globalThis.crypto.subtle.importKey("jwk",a,{name:"ECDH",namedCurve:c},!0,["deriveKey","deriveBits"]),2)}d=e.g;return e.return(d)})};function eh(a,b){var c=a.length;if(c<b)return b=new Uint8Array(b-c),X(b,a);if(c>b){for(var d=0;d<c-b;d++)if(a[d]!=0)throw new T("Number needs more bytes to be represented.");return a.slice(c-b,c)}return a}function fh(a){switch(a){case 2:return 1;case 3:return 2;case 4:return 3;default:throw new T("Unknown curve type.");}}function gh(a){switch(a){case 1:return"SHA-1";case 3:return"SHA-256";case 4:return"SHA-512";default:throw new T("Unknown hash type.");}}function hh(a){switch(a){case 1:return 1;case 2:return 2;case 3:return 3;default:throw new T("Unknown point format.");}};var ih=new q.Map,jh=new q.Map,kh=new q.Map;function lh(a){var b;b===void 0&&(b=!0);if(!a)throw new T("Key manager cannot be null.");var c=a.B();if(ih.has(c)){if(!(ih.get(c)instanceof a.constructor))throw new T("Key manager for key type "+c+" has already been registered and cannot be overwritten.");if(!jh.get(c)&&b)throw new T("Key manager for key type "+c+" has already been registered with forbidden new key operation.");jh.set(c,b)}ih.set(c,a);jh.set(c,b)}function mh(a){var b=ih.get(a);if(!b)throw new T("Key manager for key type "+a+" has not been registered.");return b}function nh(a,b,c){var d,e;return C(function(g){if(g.h==1){if(b instanceof U){if(c&&O(b,1)!=c)throw new T("Key type is "+c+", but it is expected to be "+O(b,1)+" or undefined.");c=O(b,1)}if(!c)throw new T("Key type has to be specified.");d=mh(c);return B(g,d.D(a,b),2)}e=g.g;if(!(e instanceof a))throw new TypeError("Unexpected type");return g.return(e)})}function oh(a){if(!a)throw new T("primitive wrapper cannot be null");var b=a.A();if(!b)throw new T("primitive wrapper cannot be undefined");if(kh.has(b)&&!(kh.get(b)instanceof a.constructor))throw new T("primitive wrapper for type "+b+" has already been registered and cannot be overwritten");kh.set(b,a)};function ph(a){if(!w(Number,"isInteger").call(Number,a)||a<0)throw new W("n must be a nonnegative integer");a=new Uint8Array(a);q.globalThis.crypto.getRandomValues(a);return a};var qh=[16,32];function rh(a){if(!w(qh,"includes").call(qh,a))throw new W("unsupported AES key size: "+a);}function Z(a){if(a==null||!(a instanceof Uint8Array))throw new W("input must be a non null Uint8Array");}function sh(a,b){if(a<0||a>b)throw new T("Version is out of bound, must be between 0 and "+b+".");};var th=function(a,b){this.key=a;this.K=b};th.prototype.encrypt=function(a){var b=this,c,d,e,g;return C(function(f){if(f.h==1)return Z(a),c=ph(b.K),d=new Uint8Array(16),d.set(c),e={name:"AES-CTR",counter:d,length:128},B(f,q.globalThis.crypto.subtle.encrypt(e,b.key,a),2);g=f.g;return f.return(X(c,new Uint8Array(g)))})};th.prototype.decrypt=function(a){var b=this,c,d,e;return C(function(g){if(g.h==1){Z(a);if(a.length<b.K)throw new T("ciphertext too short");c=new Uint8Array(16);c.set(a.subarray(0,b.K));d={name:"AES-CTR",counter:c,length:128};e=Uint8Array;return B(g,q.globalThis.crypto.subtle.decrypt(d,b.key,new Uint8Array(a.subarray(b.K))),2)}return g.return(new e(g.g))})};function uh(a,b){var c;return C(function(d){if(d.h==1){if(!w(Number,"isInteger").call(Number,b))throw new T("invalid IV length, must be an integer");if(b<12||b>16)throw new T("invalid IV length, must be at least 12 and at most 16");Z(a);rh(a.length);return B(d,q.globalThis.crypto.subtle.importKey("raw",a,{name:"AES-CTR",length:a.length},!1,["encrypt","decrypt"]),2)}c=d.g;return d.return(new th(c,b))})};var vh=function(){};var wh=function(a,b,c){this.hash=a;this.key=b;this.L=c};z(wh,vh);var xh=function(a,b){var c;return C(function(d){if(d.h==1)return Z(b),B(d,q.globalThis.crypto.subtle.sign({name:"HMAC",hash:{name:a.hash}},a.key,b),2);c=d.g;return d.return(new Uint8Array(c.slice(0,a.L)))})},yh=function(a,b,c){var d;return C(function(e){if(e.h==1)return Z(b),Z(c),B(e,xh(a,c),2);d=e.g;if(b.length!==d.length)var g=!1;else{for(var f=g=0;f<b.length;f++)g|=b[f]^d[f];g=g==0}return e.return(g)})};function zh(a,b,c){var d;return C(function(e){if(e.h==1){Z(b);if(!w(Number,"isInteger").call(Number,c))throw new W("invalid tag size, must be an integer");if(c<10)throw new W("tag too short, must be at least "+(10).toString()+" bytes");switch(a){case "SHA-1":if(c>20)throw new W("tag too long, must not be larger than 20 bytes");break;case "SHA-256":if(c>32)throw new W("tag too long, must not be larger than 32 bytes");break;case "SHA-384":if(c>48)throw new W("tag too long, must not be larger than 48 bytes");break;case "SHA-512":if(c>64)throw new W("tag too long, must not be larger than 64 bytes");break;default:throw new W(a+" is not supported");}return B(e,q.globalThis.crypto.subtle.importKey("raw",b,{name:"HMAC",hash:{name:a},length:b.length*8},!1,["sign","verify"]),2)}d=e.g;return e.return(new wh(a,d,c))})};var Ah=function(a,b,c,d){this.g=a;this.K=b;this.h=c;this.L=d};z(Ah,Vf);Ah.prototype.encrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g;return C(function(f){if(f.h==1)return Z(a),B(f,c.g.encrypt(a),2);if(f.h!=3)return d=f.g,Z(b),e=Og(b.length*8),B(f,xh(c.h,X(b,d,e)),3);g=f.g;if(c.L!=g.length)throw new T("invalid tag size, expected "+c.L+" but got "+g.length);return f.return(X(d,g))})};Ah.prototype.decrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g,f,h;return C(function(k){if(k.h==1){Z(a);if(a.length<c.K+c.L)throw new T("ciphertext too short");d=new Uint8Array(a.subarray(0,a.length-c.L));Z(b);e=Og(b.length*8);g=X(b,d,e);f=new Uint8Array(a.subarray(d.length));return B(k,yh(c.h,f,g),2)}h=k.g;if(!h)throw new T("invalid MAC");return k.return(c.g.decrypt(d))})};function Bh(a,b,c,d,e){var g,f;return C(function(h){if(h.h==1)return Z(a),Z(d),B(h,uh(a,b),2);if(h.h!=3)return g=h.g,B(h,zh(c,d,e),3);f=h.g;return h.return(new Ah(g,b,f,e))})};var Ch=function(){};Ch.prototype.R=function(a){if(a instanceof Uint8Array){try{var b=gg(a)}catch(e){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}if(!b||!M(b,dg,1)||!M(b,eg,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}else if(a instanceof fg)b=a;else throw new T("Expected AesCtrHmacAeadKeyFormat-proto");var c=Dh(M(b,dg,1));a=c.va;c=c.W;var d=new Xf;d=nd(d,1,0);a=ld(d,2,a);c=ph(c);a=Q(a,3,c);c=Eh(M(b,eg,2));b=c.Aa;c=c.za;d=new Zf;d=nd(d,1,0);b=ld(d,2,b);c=ph(c);b=Q(b,3,c);c=new $f;a=ld(c,2,a);return ld(a,3,b)};var Dh=function(a){if(!a)throw new T("Invalid AES CTR HMAC key format: key format undefined");var b=N(a,2);rh(b);a=M(a,Wf,1);if(!a)throw new T("Invalid AES CTR HMAC key format: params undefined");var c=N(a,1);if(c<12||c>16)throw new T("Invalid AES CTR HMAC key format: IV size is out of range: "+c);return{va:a,W:b,K:c}},Eh=function(a){if(!a)throw new T("Invalid AES CTR HMAC key format: key format undefined");var b=N(a,2);if(b<16)throw new T("Invalid AES CTR HMAC key format: HMAC key is too small: "+N(a,2));a=M(a,Yf,1);if(!a)throw new T("Invalid AES CTR HMAC key format: params undefined");var c=N(a,2);if(c<10)throw new T("Invalid HMAC params: tag size "+c+" is too small.");if(!Fh.has(P(a,1)))throw new T("Unknown hash type.");if(c>Fh.get(P(a,1)))throw new T("Invalid HMAC params: tag size "+c+" is out of range.");switch(P(a,1)){case 1:var d="SHA-1";break;case 3:d="SHA-256";break;case 4:d="SHA-512";break;default:d="UNKNOWN HASH"}return{Aa:a,za:b,ya:d,L:c}},Fh=new q.Map([[1,20],[3,32],[4,64]]),Gh=function(){this.g=new Ch};m=Gh.prototype;m.D=function(a,b){var c,d,e,g,f,h=this,k,l,n,p,r,t;return C(function(u){if(u.h==1){if(a!=h.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(!h.O(O(b,1)))throw new T("Key type "+O(b,1)+" is not supported. This key manager supports "+h.B()+".");try{k=cg(b.getValue())}catch(ra){throw new T("Could not parse the key in key data as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}if(k===null||k===void 0)throw new T("Could not parse the key in key data as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}else if(b instanceof $f)k=b;else throw new T("Given key type is not supported. This key manager supports "+h.B()+".");var v=M(k,Xf,2);if(!v)throw new T("Invalid AES CTR HMAC key format: key undefined");sh(v.o(),h.o());var x=new dg;var K=M(v,Wf,2);x=ld(x,1,K);K=Ng(L(v,3));x=nd(x,2,K);x=Dh(x).K;g=V(L(v,3));f=x;l=g;n=f;v=M(k,Zf,3);if(!v)throw new T("Invalid AES CTR HMAC key format: key undefined");sh(v.o(),h.o());x=new eg;K=M(v,Yf,2);x=ld(x,1,K);K=Ng(L(v,3));x=nd(x,2,K);K=Eh(x);x=K.ya;K=K.L;c=V(L(v,3));d=x;e=K;p=c;r=d;t=e;return B(u,Bh(l,n,r,p,t),2)}return u.return(u.g)})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"};m.A=function(){return Vf};m.o=function(){return 0};m.P=function(){return this.g};var Hh=function(a){var b=a.key;a=a.G;this.key=b;this.G=a};Hh.prototype.encrypt=function(a,b,c){var d=this,e,g;return C(function(f){if(f.h==1){if(a.length!==12)throw new T("IV must be 12 bytes");e={name:"AES-GCM",iv:a,tagLength:128};c&&(e.additionalData=c);return B(f,q.globalThis.crypto.subtle.encrypt(e,d.key,b),2)}g=f.g;return f.return(d.G?X(a,new Uint8Array(g)):new Uint8Array(g))})};Hh.prototype.decrypt=function(a,b,c){var d=this,e,g,f,h,k;return C(function(l){if(l.h==1){e=d.G?28:16;if(b.length<e)throw new T("ciphertext too short");if(a.length!==12)throw new T("IV must be 12 bytes");g={name:"AES-GCM",iv:a,tagLength:128};c&&(g.additionalData=c);f=d.G?new Uint8Array(b.subarray(12)):b;l.u=2;h=Uint8Array;return B(l,q.globalThis.crypto.subtle.decrypt(g,d.key,f),4)}if(l.h!=2)return l.return(new h(l.g));k=Aa(l);throw new T(k.toString());})};function Ih(a){var b=a.key;var c=a.G;var d;return C(function(e){if(e.h==1){if(!(aa=[16,32],w(aa,"includes")).call(aa,b.length))throw new W("unsupported AES key size: ${n}");return B(e,q.globalThis.crypto.subtle.importKey("raw",b,{name:"AES-GCM",length:b.length},!1,["encrypt","decrypt"]),2)}d=e.g;return e.return(new Hh({key:d,G:c}))})};var Jh=function(a){this.key=a;this.g=new Hh({key:a,G:!0})};z(Jh,Vf);Jh.prototype.encrypt=function(a,b){var c=this,d;return C(function(e){d=ph(12);return e.return(c.g.encrypt(d,a,b))})};Jh.prototype.decrypt=function(a,b){var c=this,d;return C(function(e){d=new Uint8Array(12);d.set(a.subarray(0,12));return e.return(c.g.decrypt(d,a,b))})};function Kh(a){var b;return C(function(c){if(c.h==1)return rh(a.length),B(c,q.globalThis.crypto.subtle.importKey("raw",a,{name:"AES-GCM",length:a.length},!1,["encrypt","decrypt"]),2);b=c.g;return c.return(new Jh(b))})};var Lh=function(){};Lh.prototype.R=function(a){if(a instanceof Uint8Array){try{var b=kg(a)}catch(c){throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key format.");}if(!N(b,2))throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key format.");a=b}else if(!(a instanceof jg))throw new T("Expected AesGcmKeyFormat-proto");b=a;rh(N(b,2));a=new hg;b=ph(N(b,2));a=Q(a,3,b);return nd(a,1,0)};var Mh=function(){this.g=new Lh};m=Mh.prototype;m.D=function(a,b){var c=this,d;return C(function(e){if(e.h==1){if(a!=c.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!="type.googleapis.com/google.crypto.tink.AesGcmKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.AesGcmKey.");try{var g=ig(b.getValue())}catch(f){throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key.");}}else if(b instanceof hg)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.AesGcmKey.");d=g;rh(Ng(L(d,3)));sh(d.o(),0);return B(e,Kh(V(L(d,3))),2)}return e.return(e.g)})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.AesGcmKey"};m.A=function(){return Vf};m.o=function(){return 0};m.P=function(){return this.g};var Nh=function(){};var Oh=function(a,b){b=[b];var c=b.concat;if(!w(Number,"isInteger").call(Number,a)||a<0||a>=4294967296)throw new W("Number has to be unsigned 32-bit integer.");for(var d=Array(4),e=0;e<4;e++)d[e]=255&a>>8*(4-e-1);b=c.call(b,d);return new Uint8Array(b)},Ph=new Uint8Array(0);var Qh=function(a){this.g=a};z(Qh,Nh);Qh.prototype.decrypt=function(a,b){var c=this,d,e,g,f,h;return C(function(k){switch(k.h){case 1:if(!a)throw new T("Ciphertext has to be non-null.");if(!(a.length>5)){k.h=2;break}d=a.subarray(0,5);return B(k,Rh(c.g,d),3);case 3:return e=k.g,g=a.subarray(5,a.length),k.u=4,B(k,Sh(e,g,b),6);case 6:f=k.g;k.h=5;k.u=0;break;case 4:Aa(k);case 5:if(f)return k.return(f);case 2:return B(k,Rh(c.g,Ph),7);case 7:return h=k.g,k.return(Sh(h,a,b))}})};var Sh=function(a,b,c){var d,e,g,f;return C(function(h){switch(h.h){case 1:d=a.length,e=0;case 2:if(!(e<d)){h.h=4;break}if(a[e].h!=1){h.h=3;break}g=a[e].D();f=void 0;h.u=5;return B(h,g.decrypt(b,c),7);case 7:f=h.g;h.h=6;h.u=0;break;case 5:Aa(h);h.h=3;break;case 6:return h.return(f);case 3:e++;h.h=2;break;case 4:throw new T("Decryption failed for the given ciphertext.");}})},Th=function(){};Th.prototype.wrap=function(a){if(!a)throw new T("Primitive set has to be non-null.");return new Qh(a)};Th.prototype.A=function(){return Nh};function Uh(a){var b=null;var c=a instanceof wg?M(a,ug,2):a;var d=M(c,qg,2);if(!d)throw new T("Params not set");d=M(d,og,1);if(!d)throw new T("KEM params not set");d=fh(P(d,1));var e=Vg(d),g=eh(V(L(c,3)),e);c=eh(V(L(c,4)),e);a instanceof wg&&(b=eh(V(L(a,3)),e));a=b;b={kty:"EC",crv:Sg(d),x:Rg(g),y:Rg(c),ext:!0};a&&(b.d=Rg(a));return b};function Vh(a){var b=M(a,og,1);if(!b)throw new T("Invalid params - missing KEM params.");var c=P(b,1);if(c!==2&&c!==3&&c!==4)throw new T("Invalid KEM params - unknown curve type.");b=P(b,2);if(b!==1&&b!==3&&b!==2&&b!==4)throw new T("Invalid KEM params - unknown hash type.");b=M(a,mg,2);if(!b)throw new T("Invalid params - missing DEM params.");if(!M(b,lg,2))throw new T("Invalid DEM params - missing AEAD key template.");b=M(b,lg,2);b=O(b,1);if(b!="type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"&&b!="type.googleapis.com/google.crypto.tink.AesGcmKey")throw new T("Invalid DEM params - "+b+" template is not supported by ECIES AEAD HKDF.");a=P(a,3);if(a!==1&&a!==2&&a!==3)throw new T("Invalid key params - unknown EC point format.");}function Wh(a,b){sh(a.o(),b);b=M(a,qg,2);if(!b)throw new T("Invalid public key - missing key params.");Vh(b);if(!Ng(L(a,3))||!Ng(L(a,4)))throw new T("Invalid public key - missing value of X or Y.");};var Xh=function(){};var Yh=function(a){var b=O(a,1);switch(b){case "type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey":try{var c=gg(a.getValue())}catch(g){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey.");}if(!M(c,dg,1)||!M(c,eg,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey.");a=c;var d=M(a,dg,1);if(!d)throw new T("AES-CTR key format not set");d=N(d,2);c=M(a,eg,2);if(!c)throw new T("HMAC key format not set");c=N(c,2);c=d+c;break;case "type.googleapis.com/google.crypto.tink.AesGcmKey":try{var e=kg(a.getValue())}catch(g){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey.");}if(!N(e,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey.");a=e;c=N(a,2);break;default:throw new T("Key type URL "+b+" is not supported.");}this.key=mh(b).P().R(a);this.g=b;this.U=c;this.W=d};Yh.prototype.M=function(a){var b=this,c;return C(function(d){if(a.length!==b.U)throw new T("Key is not of the correct length, expected length: "+b.U+", but got key of length: "+a.length+".");if(b.g==="type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"){var e=b.key,g=M(e,Xf,2);if(!g)throw new T("AES-CTR key not set");Q(g,3,a.slice(0,b.W));g=M(e,Zf,3);if(!g)throw new T("HMAC key not set");Q(g,3,a.slice(b.W,b.U));c=e}else{if(!(b.key instanceof hg))throw new T("Key is not an AES-CTR key");c=Q(b.key,3,a)}return d.return(nh(Vf,c,b.g))})};function Zh(a,b,c,d,e){var g,f,h,k,l,n,p,r,t;return C(function(u){switch(u.h){case 1:if(!w(Number,"isInteger").call(Number,a))throw new W("size must be an integer");if(a<=0)throw new W("size must be positive");switch(b){case "SHA-1":g=20;if(a>5100)throw new W("size too large");break;case "SHA-256":g=32;if(a>8160)throw new W("size too large");break;case "SHA-512":g=64;if(a>16320)throw new W("size too large");break;default:throw new W(b+" is not supported");}Z(c);Z(d);f=e;if(e==null||f===void 0||f.length==0)f=new Uint8Array(g);Z(f);return B(u,zh(b,f,g),2);case 2:return h=u.g,B(u,xh(h,c),3);case 3:return k=u.g,B(u,zh(b,k,g),4);case 4:h=u.g,l=1,n=0,p=new Uint8Array(0),r=new Uint8Array(a);case 5:return t=new Uint8Array(p.length+d.length+1),t.set(p,0),t.set(d,p.length),t[t.length-1]=l,B(u,xh(h,t),8);case 8:p=u.g;if(n+p.length<a)r.set(p,n),n+=p.length,l++;else{r.set(p.subarray(0,a-n),n);u.h=7;break}u.h=5;break;case 7:return u.return(r)}})};var $h=function(a){if(!a)throw new T("Recipient public key has to be non-null.");if(a.type!=="public"||!a.algorithm)throw new T("Expected Crypto key of type: public.");this.publicKey=a};$h.prototype.ga=function(a,b,c,d,e){var g=this,f,h,k,l,n,p,r,t,u,v;return C(function(x){switch(x.h){case 1:f=g.publicKey.algorithm;h=f.namedCurve;if(!h)throw new T("Curve has to be defined.");return B(x,ah(h),2);case 2:return k=x.g,B(x,$g(k.privateKey,g.publicKey),3);case 3:return l=x.g,B(x,bh(k.publicKey),4);case 4:p=n=x.g;r=p.crv;if(!r)throw new T("Curve has to be defined.");t=Ug(r,b,n);u=X(t,l);return B(x,Zh(a,c,u,d,e),5);case 5:return v=x.g,x.return({key:v,token:t})}})};function ai(a){var b;return C(function(c){if(c.h==1)return B(c,ch(a),2);b=c.g;return c.return(new $h(b))})};var bi=function(a,b,c,d,e){if(!a)throw new T("KEM sender has to be non-null.");if(!b)throw new T("HMAC algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");this.u=a;this.h=b;this.l=c;this.g=d;this.i=e};z(bi,Xh);bi.prototype.encrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g,f,h;return C(function(k){switch(k.h){case 1:return d=c.g.U,B(k,c.u.ga(d,c.l,c.h,b,c.i),2);case 2:return e=k.g,B(k,c.g.M(e.key),3);case 3:return g=k.g,B(k,g.encrypt(a),4);case 4:return f=k.g,h=e.token,k.return(X(h,f))}})};function ci(a,b,c,d,e){var g;return C(function(f){if(f.h==1){if(!a)throw new T("Recipient public key has to be non-null.");if(!b)throw new T("HMAC algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");return B(f,ai(a),2)}g=f.g;return f.return(new bi(g,b,c,d,e))})};var di=function(){};di.prototype.R=function(){throw new T("This operation is not supported for public keys. Use EciesAeadHkdfPrivateKeyManager to generate new keys.");};var ei=function(){this.g=new di};m=ei.prototype;m.D=function(a,b){var c=this,d,e,g,f,h,k,l,n,p,r;return C(function(t){if(a!==c.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey.");try{var u=yg(b.getValue())}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey key-proto.");}if(!M(u,qg,2)||!L(u,3)||!L(u,4))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey key-proto.");}else if(b instanceof ug)u=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey.");d=u;Wh(d,c.o());e=Uh(d);g=M(d,qg,2);f=M(g,mg,2);if(!f)throw new T("DEM params not set");h=M(f,lg,2);k=new Yh(h);l=hh(P(g,3));n=M(g,og,1);if(!n)throw new T("KEM params not set");p=gh(P(n,2));r=V(L(n,11));return t.return(ci(e,p,l,k,r))})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey"};m.A=function(){return Xh};m.o=function(){return 0};m.P=function(){return this.g};var fi=function(a){if(!a)throw new T("Private key has to be non-null.");if(a.type!=="private"||!a.algorithm)throw new T("Expected crypto key of type: private.");this.g=a};fi.prototype.ea=function(a,b,c,d,e,g){var f=this,h,k,l,n,p,r,t;return C(function(u){switch(u.h){case 1:h=f.g.algorithm;k=h.namedCurve;if(!k)throw new T("Curve has to be defined.");l=Zg(k,c,a);return B(u,ch(l),2);case 2:return n=u.g,B(u,$g(f.g,n),3);case 3:return p=u.g,r=X(a,p),B(u,Zh(b,d,r,e,g),4);case 4:return t=u.g,u.return(t)}})};function gi(a){var b;return C(function(c){if(c.h==1)return B(c,dh(a),2);b=c.g;return c.return(new fi(b))})};var hi=function(a,b,c,d,e,g){if(!a)throw new T("Recipient private key has to be non-null.");if(!b)throw new T("KEM recipient has to be non-null.");if(!c)throw new T("HKDF hash algorithm has to be non-null.");if(!d)throw new T("Point format has to be non-null.");if(!e)throw new T("DEM helper has to be non-null.");a=a.crv;if(!a)throw new T("Curve has to be defined.");a=Tg(a);a:{switch(d){case 1:a=2*Vg(a)+1;break a;case 2:a=Vg(a)+1;break a;case 3:a=2*Vg(a);break a}a=void 0}this.F=b;this.i=c;this.u=d;this.h=e;this.g=a;this.l=g};z(hi,Nh);hi.prototype.decrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1){if(a.length<c.g)throw new T("Ciphertext is too short.");d=a.slice(0,c.g);e=a.slice(c.g,a.length);return B(f,c.M(d,b),2)}g=f.g;return f.return(g.decrypt(e))})};hi.prototype.M=function(a,b){var c=this,d;return C(function(e){if(e.h==1)return b||(b=new Uint8Array(0)),B(e,c.F.ea(a,c.h.U,c.u,c.i,b,c.l),2);d=e.g;return e.return(c.h.M(d))})};function ii(a,b,c,d,e){var g;return C(function(f){if(f.h==1){if(!a)throw new T("Recipient private key has to be non-null.");if(!b)throw new T("HKDF hash algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");if(!a)throw new T("Recipient private key has to be non-null.");return B(f,gi(a),2)}g=f.g;return f.return(new hi(a,g,b,c,d,e))})};var ji=function(){};ji.prototype.R=function(a){var b,c,d,e,g,f,h,k,l;return C(function(n){switch(n.h){case 1:if(!a)throw new T("Key format has to be non-null.");if(a instanceof Uint8Array){try{var p=tg(a)}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");}if(!M(p,qg,1))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");}else if(a instanceof sg)p=a;else throw new T("Expected type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");b=p;p=M(b,qg,1);if(!p)throw new T("Invalid key format - missing key params.");Vh(p);c=M(b,qg,1);if(!c)throw new T("Params not set");d=M(c,og,1);if(!d)throw new T("KEM params not set");e=P(d,1);g=fh(e);f=Sg(g);return B(n,ah(f),2);case 2:return h=n.g,B(n,bh(h.publicKey),3);case 3:return k=n.g,B(n,bh(h.privateKey),4);case 4:l=n.g;p=n.return;var r=k.x;var t=k.y;if(r===void 0)throw new T("x must be set");if(t===void 0)throw new T("y must be set");var u=new ug;u=nd(u,1,0);u=ld(u,2,c);r=Y(r);r=Q(u,3,r);t=Y(t);r=Q(r,4,t);t=l.d;if(t===void 0)throw new T("d must be set");u=new wg;u=nd(u,1,0);r=ld(u,2,r);t=Y(t);t=Q(r,3,t);return p.call(n,t)}})};var ki=function(){this.g=new ji};m=ki.prototype;m.D=function(a,b){var c=this,d,e,g,f,h,k,l,n,p,r,t;return C(function(u){if(a!==c.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey.");var v=b.getValue();try{var x=xg(v)}catch(K){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key-proto.");}if(!M(x,ug,2)||!L(x,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key-proto.");v=x}else if(b instanceof wg)v=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey.");d=v;sh(d.o(),0);if(!L(d,3))throw new T("Invalid private key - missing private key value.");v=M(d,ug,2);if(!v)throw new T("Invalid private key - missing public key information.");Wh(v,0);e=Uh(d);g=M(d,ug,2);if(!g)throw new T("Public key not set");f=M(g,qg,2);if(!f)throw new T("Params not set");h=M(f,mg,2);if(!h)throw new T("DEM params not set");k=M(h,lg,2);if(!k)throw new T("Key template not set");l=new Yh(k);n=hh(P(f,3));p=M(f,og,1);if(!p)throw new T("KEM params not set");r=gh(P(p,2));t=V(L(p,11));return u.return(ii(e,r,n,l,t))})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey"};m.A=function(){return Nh};m.o=function(){return 0};m.P=function(){return this.g};var li=function(a){this.g=a};z(li,Xh);li.prototype.encrypt=function(a,b){var c=this,d,e,g,f;return C(function(h){if(h.h==1){if(!a)throw new T("Plaintext has to be non-null.");d=c.g.h;if(!d)throw new T("Primary not set.");e=d.D();return B(h,e.encrypt(a,b),2)}g=h.g;f=d.g;return h.return(X(f,g))})};var mi=function(){};mi.prototype.wrap=function(a){if(!a)throw new T("Primitive set has to be non-null.");if(!a.h)throw new T("Primary has to be non-null.");return new li(a)};mi.prototype.A=function(){return Xh};var oi=ni(1,0),pi=ni(2,16),qi=ni(2,18),ri=ni(2,1),si=ni(2,3),ti=ni(2,1),ui=ni(2,2),vi=Qg("KEM"),wi=Qg("HPKE"),xi=Qg("HPKE-v1");function ni(a,b){for(var c=new Uint8Array(a),d=0;d<a;d++)c[d]=b>>8*(a-d-1)&255;return c}function yi(a){var b=a.J;var c=a.I;return X(xi,a.C,Qg(b),c)}function zi(a){var b=a.N;var c=a.info;var d=a.C;return X(ni(2,a.length),xi,d,Qg(b),c)}function Ai(a){switch(a){case 2:return 1;case 4:return 3;default:throw new W("Unrecognized NIST HPKE KEM identifier");}}function Bi(a,b){var c;return C(function(d){return d.h==1?(c=Zg(a,1,b),B(d,ch(c),2)):d.return(d.g)})}function Ci(a){var b=a.da;var c=a.publicKey;var d=a.privateKey;var e;return C(function(g){return g.h==1?(e=Zg(b,1,c),e.d=Rg(d),B(g,dh(e),2)):g.return(g.g)})}function Di(a){var b,c;return C(function(d){if(d.h==1)return b=a.algorithm,B(d,bh(a),2);c=d.g;if(!c.crv)throw new T("Curve has to be defined.");return d.return(Ug(b.namedCurve,1,c))})};var Ei=function(a,b,c,d){this.ha=a;this.key=b;this.i=c;this.h=d;this.g=BigInt(0);this.l=(BigInt(1)<<BigInt(96))-BigInt(1)};Ei.prototype.seal=function(a,b){var c=this,d;return C(function(e){return e.h==1?(d=Fi(c),B(e,c.h.seal({key:c.key,nonce:d,Da:a,ca:b}),2)):e.return(e.g)})};Ei.prototype.open=function(a,b){var c=this,d;return C(function(e){d=Fi(c);return e.return(c.h.open({key:c.key,nonce:d,xa:a,ca:b}))})};var Fi=function(a){for(var b=a.g,c=new Uint8Array(12),d=0;d<12;d++)c[d]=Number(b>>BigInt(8*(12-d-1)))&255;b=a.i;if(b.length!==c.length)throw new W("Both byte arrays should be of the same length");d=new Uint8Array(b.length);for(var e=0;e<d.length;e++)d[e]=b[e]^c[e];if(a.g>=a.l)throw new T("message limit reached");a.g+=BigInt(1);return d};function Gi(a,b,c,d,e,g){var f,h,k,l,n,p,r,t,u;return C(function(v){switch(v.h){case 1:a:{switch(e.aa){case 16:var x=ti;break a;case 32:x=ui;break a}x=void 0}a:{switch(d.Z){case "SHA-256":var K=ri;break a;case "SHA-512":K=si;break a}K=void 0}f=X(wi,Hi(c),K,x);h=Ii(d,{I:new Uint8Array(0),J:"psk_id_hash",C:f});return B(v,Ii(d,{I:g,J:"info_hash",C:f}),2);case 2:return k=v.g,B(v,h,3);case 3:return l=v.g,n=X(oi,l,k),B(v,Ii(d,{I:new Uint8Array(0),J:"secret",C:f,salt:b}),4);case 4:return p=v.g,r=Ji(d,{na:p,info:n,N:"key",C:f,length:e.aa}),B(v,Ji(d,{na:p,info:n,N:"base_nonce",C:f,length:12}),5);case 5:return t=v.g,B(v,r,6);case 6:return u=v.g,v.return(new Ei(a,u,t,e))}})}function Ki(a,b,c,d,e){var g,f,h;return C(function(k){return k.h==1?B(k,b.ga(a),2):k.h!=3?(g=k.g,f=g.ha,h=g.Fa,B(k,Gi(f,h,b,c,d,e),3)):k.return(k.g)})}function Li(a,b,c,d,e,g){var f;return C(function(h){return h.h==1?B(h,c.ea(a,b),2):h.h!=3?(f=h.g,B(h,Gi(a,f,c,d,e,g),3)):h.return(h.g)})};var Mi=function(a,b){this.privateKey=a;this.publicKey=b},Ni=function(a){return C(function(b){return b.h==1?B(b,Di(a.publicKey),2):b.return(b.g)})};function Oi(a){var b=a.privateKey;var c=a.publicKey;var d=a.da;var e,g;return C(function(f){if(f.h==1){if(!b)throw new W("KEM private key was null or undefined");if(!c)throw new W("KEM public key was null or undefined");return B(f,Bi(Sg(d),c),2)}if(f.h!=3)return e=f.g,B(f,Ci({da:Sg(d),publicKey:c,privateKey:b}),3);g=f.g;return f.return(new Mi(g,e))})}function Pi(a){return C(function(b){Qi(a.privateKey,"private");Qi(a.publicKey,"public");return b.return(new Mi(a.privateKey,a.publicKey))})}function Qi(a,b){if(b!==a.type)throw new W("keyPair "+b+" key was of type "+a.type);a=a.algorithm;if("ECDH"!==a.name)throw new W("keyPair "+b+" key should be ECDH but found "+a.name);};var Ri=function(a){this.aa=a};Ri.prototype.seal=function(a){var b=a.key;var c=a.nonce;var d=a.Da;var e=a.ca;var g=this,f;return C(function(h){if(h.h==1){if(b.length!==g.aa)throw new T("Unexpected key length: "+b.length.toString());return B(h,Ih({key:b,G:!1}),2)}return h.h!=3?(f=h.g,B(h,f.encrypt(c,d,e),3)):h.return(h.g)})};Ri.prototype.open=function(a){var b=a.key;var c=a.nonce;var d=a.xa;var e=a.ca;var g=this,f;return C(function(h){if(h.h==1){if(b.length!==g.aa)throw new T("Unexpected key length: "+b.length.toString());return B(h,Ih({key:b,G:!1}),2)}f=h.g;return h.return(f.decrypt(c,d,e))})};var Si=function(a){this.Z=a},Ii=function(a,b){var c=b.I;var d=b.J;var e=b.C;var g=b.salt;return C(function(f){return f.h==1?B(f,Ti(a,yi({J:d,I:c,C:e}),g),2):f.return(f.g)})},Ji=function(a,b){var c=b.na;var d=b.info;var e=b.N;var g=b.C;var f=b.length;return C(function(h){return h.h==1?B(h,Ui(a,c,zi({N:e,info:d,C:g,length:f}),f),2):h.return(h.g)})},Vi=function(a,b){var c=b.I;var d=b.J;var e=b.info;var g=b.N;var f=b.C;var h=b.length;var k=b.salt;var l;return C(function(n){return n.h==1?B(n,Ti(a,yi({J:d,I:c,C:f}),k),2):n.h!=3?(l=n.g,B(n,Ui(a,l,zi({N:g,info:e,C:f,length:h}),h),3)):n.return(n.g)})},Ui=function(a,b,c,d){var e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:if(!w(Number,"isInteger").call(Number,d))throw new T("length must be an integer");if(d<=0)throw new T("length must be positive");e=Wi(a);if(d>255*e)throw new T("length too large");Z(c);return B(p,zh(a.Z,b,e),2);case 2:g=p.g,f=1,h=0,k=new Uint8Array(0),l=new Uint8Array(d);case 3:return n=new Uint8Array(k.length+c.length+1),n.set(k,0),n.set(c,k.length),n[n.length-1]=f,B(p,xh(g,n),6);case 6:k=p.g;if(h+k.length<d)l.set(k,h),h+=k.length,f++;else{l.set(k.subarray(0,d-h),h);p.h=5;break}p.h=3;break;case 5:return p.return(l)}})},Ti=function(a,b,c){var d,e,g,f;return C(function(h){if(h.h==1)return Z(b),d=Wi(a),((e=c)==null?0:e.length)||(c=new Uint8Array(d)),Z(c),B(h,zh(a.Z,c,d),2);if(h.h!=3)return g=h.g,B(h,xh(g,b),3);f=h.g;return h.return(f)})},Wi=function(a){switch(a.Z){case "SHA-256":return 32;case "SHA-512":return 64}};var Xi=function(a,b){this.i=a;this.g=b},Yi=function(a){switch(a){case 1:return new Xi(new Si("SHA-256"),1);case 3:return new Xi(new Si("SHA-512"),3)}};Xi.prototype.h=function(a,b,c){var d=this,e,g;return C(function(f){return f.h==1?(e=X(b,c),g=X(vi,Hi(d)),B(f,Vi(d.i,{I:a,J:"eae_prk",info:e,N:"shared_secret",C:g,length:Wi(d.i)}),2)):f.return(f.g)})};Xi.prototype.l=function(a,b){var c=this,d,e,g,f,h,k;return C(function(l){switch(l.h){case 1:return B(l,Bi(Sg(c.g),a),2);case 2:return d=l.g,e=$g(b.privateKey,d),B(l,Ni(b),3);case 3:return g=l.g,B(l,e,4);case 4:return f=l.g,B(l,c.h(f,g,a),5);case 5:return h=l.g,k={Fa:h,ha:g},l.return(k)}})};Xi.prototype.ga=function(a){var b=this,c,d,e,g;return C(function(f){switch(f.h){case 1:return B(f,ah(Sg(b.g)),2);case 2:return c=f.g,d=b,e=d.l,g=a,B(f,Pi(c),4);case 4:return B(f,e.call(d,g,f.g),3);case 3:return f.return(f.g)}})};Xi.prototype.ea=function(a,b){var c=this,d,e,g,f,h,k;return C(function(l){switch(l.h){case 1:return d=b.privateKey,B(l,Bi(Sg(c.g),a),2);case 2:return e=l.g,B(l,$g(d,e),3);case 3:return g=l.g,f=c,h=f.h,k=a,B(l,Ni(b),4);case 4:return l.return(h.call(f,g,k,l.g))}})};var Hi=function(a){switch(a.g){case 1:return pi;case 3:return qi}};/*

 Copyright 2023 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Zi=function(a){switch(P(a,1)){case 2:return Yi(1);case 4:return Yi(3);default:throw new W("Unrecognized HPKE KEM identifier");}},$i=function(a){switch(P(a,2)){case 1:return new Si("SHA-256");case 3:return new Si("SHA-512");default:throw new W("Unrecognized HPKE KDF identifier");}},aj=function(a){switch(a.M()){case 1:return new Ri(16);case 2:return new Ri(32);default:throw new W("Unrecognized HPKE AEAD identifier");}};var bj=function(a,b,c,d,e){if(!a)throw new W("Recipient private key must be non-null.");if(!b)throw new W("KEM algorithm must be non-null.");if(!c)throw new W("KDF algorithm must be non-null.");if(!d)throw new W("AEAD algorithm must be non-null.");this.u=a;this.l=b;this.i=c;this.h=d;this.g=e};z(bj,Nh);var cj=function(a){var b,c,d,e,g,f,h;return C(function(k){if(k.h==1){if(V(L(a,3)).length===0)throw new W("Recipient private key is empty.");b=M(a,Eg,2);if(!b)throw new W("Recipient private key is missing public key field.");c=M(b,zg,2);if(!c)throw new W("Public key is missing params field.");d=Zi(c);e=$i(c);g=aj(c);a:switch(P(c,1)){case 2:var l=65;break a;case 4:l=133;break a;default:throw new W("Unable to determine KEM-encoding length");}f=l;a:{l=M(a,Eg,2);if(!l)throw new W("Public key not set");var n=M(l,zg,2);if(!n)throw new W("Params not set");switch(P(n,1)){case 2:case 4:l=Oi({privateKey:V(L(a,3)),publicKey:V(L(l,3)),da:Ai(P(n,1))});break a;default:throw new W("Unrecognized HPKE KEM identifier");}}return B(k,l,2)}h=k.g;return k.return(new bj(h,d,e,g,f))})};bj.prototype.decrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1){if(a.length<=c.g)throw new T("Ciphertext is too short.");b||(b=new Uint8Array(0));d=a.slice(0,c.g);e=a.slice(c.g,a.length);return B(f,Li(d,c.u,c.l,c.i,c.h,b),2)}g=f.g;return f.return(g.open(e,dj))})};var dj=new Uint8Array(0);var ej=function(a,b,c,d){if(!a)throw new W("Recipient public key must be non-null.");if(!b)throw new W("KEM algorithm must be non-null.");if(!c)throw new W("KDF algorithm must be non-null.");if(!d)throw new W("AEAD algorithm must be non-null.");this.l=a;this.i=b;this.h=c;this.g=d};z(ej,Xh);var fj=function(a){var b,c,d,e;return C(function(g){if(V(L(a,3)).length===0)throw new W("Recipient public key is empty.");b=M(a,zg,2);if(!b)throw new W("Public key is missing params field.");c=Zi(b);d=$i(b);e=aj(b);return g.return(new ej(a,c,d,e))})};ej.prototype.encrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1)return b||(b=new Uint8Array(0)),d=V(L(c.l,3)),B(f,Ki(d,c.i,c.h,c.g,b),2);if(f.h!=3)return e=f.g,B(f,e.seal(a,gj),3);g=f.g;return f.return(X(e.ha,g))})};var gj=new Uint8Array(0);function hj(a){var b=P(a,1);if(b!==2&&b!==4)throw new T("Invalid hpke params - unknown KEM identifier.");b=P(a,2);if(b!==1&&b!==3)throw new T("Invalid hpke params - unknown KDF identifier.");a=a.M();if(a!==1&&a!==2)throw new T("Invalid hpke params - unknown AEAD identifier.");}function ij(a,b){sh(a.o(),b);b=M(a,zg,2);if(!b)throw new T("Invalid public key - missing key params.");hj(b);if(Ng(L(a,3))===0)throw new T("Invalid public key - missing public key value.");};var jj=function(){};jj.prototype.R=function(){throw new T("This operation is not supported for public keys. Use HpkePrivateKeyManager to generate new keys.");};var kj=function(){this.g=new jj};m=kj.prototype;m.D=function(a,b){var c=this,d;return C(function(e){if(a!==c.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.HpkePublicKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePublicKey.");try{var g=Ig(b.getValue())}catch(f){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePublicKey key-proto.");}if(!M(g,zg,2)||!L(g,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePublicKey key-proto.");}else if(b instanceof Eg)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePublicKey.");d=g;ij(d,c.o());return e.return(fj(d))})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.HpkePublicKey"};m.A=function(){return Xh};m.o=function(){return 0};m.P=function(){return this.g};var lj=function(){};lj.prototype.R=function(a){var b,c,d,e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:if(!a)throw new T("Key format must be non-null.");if(a instanceof Uint8Array){try{var r=Dg(a)}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");}if(!M(r,zg,1))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");}else if(a instanceof Bg)r=a;else throw new T("Expected type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");b=r;r=M(b,zg,1);if(!r)throw new T("Invalid key format - missing key params.");hj(r);c=M(b,zg,1);if(!c)throw new T("Params not set");d=Ai(P(c,1));e=Sg(d);return B(p,ah(e),2);case 2:return g=p.g,B(p,bh(g.privateKey),3);case 3:return f=p.g,B(p,Di(g.publicKey),4);case 4:h=p.g;r=new Eg;r=nd(r,1,0);r=ld(r,2,c);k=Q(r,3,h);l=f;n=l.d;if(n===void 0)throw new T("d must be set");r=p.return;var t=new Gg;t=nd(t,1,0);t=ld(t,2,k);var u=Y(n);t=Q(t,3,u);return r.call(p,t)}})};var mj=function(){this.g=new lj};m=mj.prototype;m.D=function(a,b){var c=this,d;return C(function(e){if(a!==c.A())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.HpkePrivateKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePrivateKey.");var g=b.getValue();try{var f=Hg(g)}catch(h){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key-proto.");}if(!M(f,Eg,2)||!L(f,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key-proto.");g=f}else if(b instanceof Gg)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePrivateKey.");d=g;sh(d.o(),0);if(Ng(L(d,3))===0)throw new T("Invalid private key - missing private key value.");g=M(d,Eg,2);if(!g)throw new T("Invalid private key - missing public key field.");ij(g,0);return e.return(cj(d))})};m.O=function(a){return a===this.B()};m.B=function(){return"type.googleapis.com/google.crypto.tink.HpkePrivateKey"};m.A=function(){return Nh};m.o=function(){return 0};m.P=function(){return this.g};function nj(a,b,c,d){var e=new Bg,g=new zg;a=qd(g,1,a);b=qd(a,2,b);c=qd(b,3,c);e=ld(e,1,c);c=new lg;c=pd(c,1,"type.googleapis.com/google.crypto.tink.HpkePrivateKey");e=e.h();e=Q(c,2,e);qd(e,3,d)};nj(2,1,1,3);nj(2,1,1,1);nj(2,1,2,3);nj(2,1,2,1);nj(4,3,2,3);nj(4,3,2,1);var oj=function(a,b,c){this.i=a;this.g=b;this.h=c};oj.prototype.D=function(){return this.i};var pj=function(a){this.i=a;this.h=null;this.g=new q.Map};pj.prototype.A=function(){return this.i};var Rh=function(a,b){return(a=qj(a,b))?a:[]},qj=function(a,b){b instanceof Uint8Array&&(b=[].concat(qa(b)).toString());return a.g.get(b)};var rj=function(a){if(!a||!Lg(a)||Lg(a).length<1)throw new T("Keyset should be non null and must contain at least one key.");for(var b=!1,c=Lg(a).length,d=0;d<c;d++){var e=Lg(a)[d];if(!e)throw new T("Key should be non null.");if(!M(e,U,1))throw new T("Key data are missing for key "+N(e,3)+".");if(P(e,4)===0)throw new T("Key "+N(e,3)+" has unknown output prefix type.");if(P(e,2)===0)throw new T("Key "+N(e,3)+" has unknown status.");if(N(a,1)===N(e,3)&&P(e,2)===1){if(b)throw new T("Primary key has to be unique.");b=!0}}if(!b)throw new T("Primary key has to be in the keyset and has to be enabled.");this.g=a};rj.prototype.D=function(a,b){var c=this,d;return C(function(e){if(e.h==1){if(!a)throw new W("primitive type must be non-null");return B(e,sj(c,a,b),2)}d=e.g;var g=e.return;if(!d)throw new T("primitive set cannot be null.");var f=d.A(),h=kh.get(f);if(!h)throw new T("no primitive wrapper found for type "+f);h=h.wrap(d);if(!(h instanceof f))throw new TypeError("Unexpected type");return g.call(e,h)})};var sj=function(a,b,c){var d,e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:d=new pj(b),e=Lg(a.g),g=e.length,f=0;case 2:if(!(f<g)){p.h=4;break}h=e[f];if(P(h,2)!==1){p.h=3;break}k=M(h,U,1);if(!k)throw new T("Key data has to be non null.");l=void 0;return c&&c.B()===O(k,1)?B(p,c.D(b,k),9):B(p,nh(b,k),8);case 8:l=p.g;p.h=7;break;case 9:l=p.g;case 7:var r=d,t=l;if(!t)throw new T("Primitive has to be non null.");if(!h)throw new T("Key has to be non null.");a:switch(P(h,4)){case 2:case 4:var u=Oh(N(h,3),0);break a;case 1:u=Oh(N(h,3),1);break a;case 3:u=Ph;break a;default:throw new T("Unsupported key prefix type.");}t=new oj(t,u,P(h,2),P(h,4));u=[].concat(qa(t.g)).toString();var v=qj(r,u);v?(v.push(t),r.g.set(u,v)):r.g.set(u,[t]);n=t;if(N(h,3)===N(a.g,1)){if(!n)throw new T("Primary cannot be set to null.");if(n.h!=1)throw new T("Primary has to be enabled.");r=Rh(d,n.g);t=!1;u=r.length;for(v=0;v<u;v++)if(r[v].h===1){t=!0;break}if(!t)throw new T("Primary cannot be set to an entry which is not held by this primitive set.");d.h=n}case 3:f++;p.h=2;break;case 4:return p.return(d)}})};rj.prototype.write=function(){return C(function(){throw new T("KeysetHandle -- write: Not implemented yet.");})};function tj(a){if(a===null)throw new T("Reader has to be non-null.");a=a.read();for(var b=A(Lg(a)),c=b.next();!c.done;c=b.next()){c=M(c.value,U,1);if(c==null)throw new T("Key data has to be non null.");c=P(c,3);if(c===0||c===1||c===2)throw new T("Keyset contains secret key material.");}return new rj(a)};function uj(a){return tj({read:function(){return Mg(a)},Ka:function(){throw new T("Not implemented yet.");}})};function vj(a){var b=new Tf;var c=a.topic;if(c!=null){if(typeof c!=="number")throw zb("int32");if(!sc(c))throw zb("int32");c|=0}b=id(b,1,c,0);b=pd(b,2,a.version);b=pd(b,3,a.configVersion);b=pd(b,4,a.modelVersion);return pd(b,5,a.taxonomyVersion)}function wj(a,b,c){c=c===void 0?document:c;var d,e,g,f,h;return C(function(k){if(k.h==1){d=c;if(typeof d.browsingTopics!=="function")return of(a.g,2),k.return();e=a.data;g=!!e.skipTopicsObservation;return B(k,d.browsingTopics({skipObservation:g}),2)}if(k.h!=3){f=k.g;if(!f.length)return of(a.g,3),k.return();var l=b.encrypt,n=new TextEncoder,p=n.encode,r=new Uf;var t=f.map(vj);bd(r);var u=r.j,v=u[E]|0;if(t==null)fd(u,v,1);else{for(var x=t===Lb?7:t[E]|0,K=x,ra=gd(x),ha=ra||Object.isFrozen(t),wb=!0,xb=!0,Ea=0;Ea<t.length;Ea++){var sa=t[Ea];ra||(sa=G(sa),wb&&(wb=!sa),xb&&(xb=sa))}ra||(x=wb?13:5,x=xb?x&-4097:x|4096);ha&&x===K||(t=Array.prototype.slice.call(t),K=0,x=md(x,v));x!==K&&F(t,x);v=fd(u,v,1,t);2&x||!(4096&x||16&x)||cd(u,v)}r=JSON.stringify(Qc(r));return B(k,l.call(b,p.call(n,r)),3)}h=k.g;of(a.g,h,[h.buffer]);k.h=0})};(function(a){var b=new If;Mf(b,977,function(){var c,d,e;return C(function(g){if(g.h==1){c=window.document.referrer;if(!c)return b.g(988,Error()),g.return();lh(new Gh);lh(new Mh);oh(new Th);lh(new ki);lh(new ei);oh(new mi);lh(new mj);lh(new kj);return B(g,uj(a).D(Xh),2)}d=g.g;e=Kf(b,function(f){var h=h===void 0?document:h;var k=f.data;if(k==="goog:topics:frame:handshake:ack")of(f.g,"goog:topics:frame:handshake:ack");else switch(k.message){case "goog:spam:client_age":Mf(b,1062,Sf(k.pvsid,k.source,k.useSavedQuery));of(f.g,"goog:spam:client_age:ack");break;case "goog:topics:frame:get:topics":Mf(b,1002,wj(f,d,h))}});rf({destination:window.parent,origin:c,ja:"goog:gRpYw:doubleclick",onMessage:e});g.h=0})}())})('[*********,[[["type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey","EkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARog5z+eqzXpk2SD9nfrNTaxxgy+IUFKl+FdJ63gJJGsZMoiIE5SAUd01WBkOowgnddBWBB/yDCFCoqOXYIQ5G9azhRe",3],1,*********,1],[["type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey","EkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARogJVp08rg6Vy/61mR3gbBGw8w3+JLaZnZPs9RrcQ0CDfQiILH3g6blf7mw4V7a9NdkNe0d1PVTGNtNUX7jZmXPnXZs",3],1,2623294582,1]]]');
    </script>
  </head>
  <body>

<fencedframe id="ps_caff" style="border: 0px;"></fencedframe></body></html>