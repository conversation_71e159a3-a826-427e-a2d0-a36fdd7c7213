<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Koszt kredytu hipotecznego kalkulator</title>
	<meta name="description" content="Koszt kredytu hipotecznego kalkulator - oblicz całkowity koszt kredytu mieszkaniowego, sprawdź wszystkie opłaty, odsetki i RRSO kredytu hipotecznego 2025.">
	<meta name="keywords" content="koszt kredytu hipotecznego kalkulator, całkowity koszt kredytu, opłaty kredytu hipotecznego, rrso kalkulator">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/koszt-kredytu-hipotecznego-kalkulator.html" itemprop="item"><span itemprop="name">koszt kredytu hipotecznego kalkulator</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Koszt kredytu hipotecznego kalkulator</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz całkowity koszt kredytu hipotecznego"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/koszt-kredytu-hipotecznego-kalkulator.html">
<table align="center">
<tbody><tr><td align="right">Kwota kredytu</td><td align="right"><input type="text" name="cloanamount" id="cloanamount" value="400000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Nominalne oprocentowanie kredytu hipotecznego w skali roku.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.5" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu hipotecznego w latach.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Typ analizy kosztów</td><td align="left" colspan="2"><select name="ccostanalysis" id="ccostanalysis"><option value="basic" selected="">Podstawowa analiza kosztów</option><option value="detailed">Szczegółowa analiza kosztów</option><option value="comparative">Porównanie z innymi bankami</option><option value="rrso">Analiza RRSO</option><option value="lifetime">Koszt przez cały okres</option></select></td></tr>
<tr><td align="right">Kategoria kosztów</td><td align="left" colspan="2"><select name="ccostcategory" id="ccostcategory"><option value="all" selected="">Wszystkie koszty</option><option value="mandatory">Tylko koszty obowiązkowe</option><option value="optional">Tylko koszty opcjonalne</option><option value="hidden">Ukryte koszty</option><option value="recurring">Koszty cykliczne</option></select></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Szczegółowe koszty kredytu</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Koszty jednorazowe</td></tr>
	<tr><td align="right">Prowizja za udzielenie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja banku za udzielenie kredytu hipotecznego.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" id="cprovisionunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
	<tr><td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cvaluation" id="cvaluation" value="2500" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td align="right">Notariusz <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszty notarialne związane z kredytem hipotecznym.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cnotary" id="cnotary" value="4000" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td align="right">Wpis do księgi wieczystej <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Opłata sądowa za wpis hipoteki do księgi wieczystej.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="clandregistry" id="clandregistry" value="200" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Koszty cykliczne</td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczny koszt ubezpieczenia nieruchomości.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1200" class="innormal indollar"></td><td>zł/rok</td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczny koszt ubezpieczenia spłaty kredytu.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="clifeins" id="clifeins" value="0.4" class="innormal inpct"></td><td>% rocznie</td></tr>
	<tr><td align="right">Prowadzenie rachunku <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="caccount" id="caccount" value="25" class="innormal indollar"></td><td>zł/miesiąc</td></tr>
	<tr><td align="right">Inne opłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty: wyciągi, przelewy, itp.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cother" id="cother" value="50" class="innormal indollar"></td><td>zł/miesiąc</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Scenariusze kosztów</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Gotowe scenariusze kosztów</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Niskie koszty</td><td><a href="#" onclick="setCostScenario('low');return false;">Bank internetowy</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Średnie koszty</td><td><a href="#" onclick="setCostScenario('medium');return false;">Bank tradycyjny</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wysokie koszty</td><td><a href="#" onclick="setCostScenario('high');return false;">Bank premium</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Tylko obowiązkowe</td><td><a href="#" onclick="setCostScenario('mandatory');return false;">Minimum kosztów</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wszystkie opcjonalne</td><td><a href="#" onclick="setCostScenario('full');return false;">Maksimum kosztów</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz koszt kredytu" onclick="calculateMortgageCost();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Całkowity koszt: &nbsp; 1 185 000 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S29zenQga3JlZHl0dSBoaXBvdGVjem5lZ28ga2Fsa3VsYXRvcg==', 0, 'S29zenQga3JlZHl0dSBoaXBvdGVjem5lZ28ga2Fsa3VsYXRvcg==', 'Q2HDFGtvd2l0eSBrb3N6dA==', 'MSAxODUgMDAwIHrFgg==');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Kwota</b></td><td align="right"><b>% kredytu</b></td><td align="right"><b>Kategoria</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Kwota kredytu</b></td><td align="right"><b>400 000 zł</b></td><td align="right"><b>100%</b></td><td align="right"><b>Kapitał</b></td></tr>
<tr><td>Odsetki łącznie</td><td align="right">685 000 zł</td><td align="right">171%</td><td align="right">Obowiązkowe</td></tr>
<tr><td>Prowizja</td><td align="right">8 000 zł</td><td align="right">2%</td><td align="right">Obowiązkowe</td></tr>
<tr><td>Ubezpieczenia</td><td align="right">70 000 zł</td><td align="right">18%</td><td align="right">Obowiązkowe/Opcjonalne</td></tr>
<tr><td>Prowadzenie rachunku</td><td align="right">7 500 zł</td><td align="right">2%</td><td align="right">Cykliczne</td></tr>
<tr><td>Koszty notarialne</td><td align="right">6 700 zł</td><td align="right">2%</td><td align="right">Jednorazowe</td></tr>
<tr><td>Inne opłaty</td><td align="right">15 000 zł</td><td align="right">4%</td><td align="right">Cykliczne</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Analiza kosztów kredytu hipotecznego:</h3>
<p><strong>RRSO:</strong> 8.95% (rzeczywista roczna stopa oprocentowania)</p>
<p><strong>Koszt pieniądza:</strong> 785 000 zł (odsetki + prowizja)</p>
<p><strong>Koszty dodatkowe:</strong> 99 200 zł (ubezpieczenia + opłaty)</p>
<p><strong>Efektywny koszt kredytu:</strong> 296% kwoty kredytu</p>
<p><strong>Miesięczny koszt:</strong> 3 950 zł (rata + ubezpieczenia + opłaty)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Struktura kosztów kredytu hipotecznego</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Struktura kosztów kredytu</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">100%</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">150%</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">200%</text>

<!-- Kapitał -->
<rect x="50" y="45" width="35" height="100" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Kapitał</text>
<text x="67" y="40" class="mcllabelx" style="fill:#000;">100%</text>

<!-- Odsetki -->
<rect x="95" y="25" width="35" height="120" fill="#e74c3c" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Odsetki</text>
<text x="112" y="20" class="mcllabelx" style="fill:#000;">171%</text>

<!-- Ubezpieczenia -->
<rect x="140" y="115" width="35" height="30" fill="#f39c12" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Ubezpieczenia</text>
<text x="157" y="110" class="mcllabelx" style="fill:#000;">18%</text>

<!-- Opłaty -->
<rect x="185" y="125" width="35" height="20" fill="#9b59b6" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Opłaty</text>
<text x="202" y="120" class="mcllabelx" style="fill:#000;">10%</text>

<!-- Inne -->
<rect x="230" y="135" width="35" height="10" fill="#1abc9c" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Inne</text>
<text x="247" y="130" class="mcllabelx" style="fill:#000;">4%</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Struktura kosztów &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie scenariuszy</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Scenariusz</th>
<th>Prowizja</th>
<th>Ubezpieczenia</th>
<th>Opłaty</th>
<th>Całkowity koszt</th>
</tr>
<tr>
<td>Niskie koszty</td>
<td>1.0%</td>
<td>Podstawowe</td>
<td>Minimalne</td>
<td>280%</td>
</tr>
<tr>
<td>Średnie koszty</td>
<td>2.0%</td>
<td>Standardowe</td>
<td>Średnie</td>
<td>296%</td>
</tr>
<tr>
<td>Wysokie koszty</td>
<td>3.0%</td>
<td>Pełne</td>
<td>Wysokie</td>
<td>320%</td>
</tr>
<tr>
<td>Tylko obowiązkowe</td>
<td>2.0%</td>
<td>Nieruchomość</td>
<td>Podstawowe</td>
<td>285%</td>
</tr>
<tr>
<td>Wszystkie opcjonalne</td>
<td>3.0%</td>
<td>Wszystkie</td>
<td>Wszystkie</td>
<td>330%</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Koszt kredytu hipotecznego kalkulator - analiza wszystkich kosztów kredytu mieszkaniowego 2025</h2>
            <p>Koszt kredytu hipotecznego kalkulator to kompleksowe narzędzie do analizy wszystkich kosztów związanych z kredytem mieszkaniowym. Nasz kalkulator uwzględnia nie tylko odsetki, ale wszystkie opłaty, ubezpieczenia i koszty dodatkowe, pozwalając na obliczenie rzeczywistego kosztu kredytu hipotecznego i RRSO.</p>

            <h3>Struktura kosztów kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>1. Koszt pieniądza (odsetki + prowizja):</h4>
                <ul>
                    <li><strong>Odsetki:</strong> 150-200% kwoty kredytu (przy 25-letnim okresie)</li>
                    <li><strong>Prowizja za udzielenie:</strong> 0.5-3.0% kwoty kredytu</li>
                    <li><strong>Łączny koszt pieniądza:</strong> 155-205% kwoty kredytu</li>
                    <li><strong>Przykład:</strong> Kredyt 400 000 zł = koszt pieniądza 620 000-820 000 zł</li>
                </ul>

                <h4>2. Koszty obowiązkowe:</h4>
                <ul>
                    <li><strong>Ubezpieczenie nieruchomości:</strong> 800-2 000 zł/rok</li>
                    <li><strong>Wycena nieruchomości:</strong> 1 500-3 500 zł</li>
                    <li><strong>Notariusz:</strong> 2 000-6 000 zł</li>
                    <li><strong>Wpis do księgi wieczystej:</strong> 200 zł</li>
                    <li><strong>Prowadzenie rachunku:</strong> 15-50 zł/miesiąc</li>
                    <li><strong>Łączne koszty obowiązkowe:</strong> 15-25% kwoty kredytu</li>
                </ul>

                <h4>3. Koszty opcjonalne:</h4>
                <ul>
                    <li><strong>Ubezpieczenie spłaty kredytu:</strong> 0.2-0.8% kwoty kredytu rocznie</li>
                    <li><strong>Ubezpieczenie tytułu prawnego:</strong> 0.2-0.5% kwoty kredytu</li>
                    <li><strong>Dodatkowe usługi bankowe:</strong> 20-150 zł/miesiąc</li>
                    <li><strong>Ekspresowa obsługa:</strong> 500-2 000 zł</li>
                    <li><strong>Łączne koszty opcjonalne:</strong> 5-15% kwoty kredytu</li>
                </ul>

                <h4>4. Ukryte koszty:</h4>
                <ul>
                    <li><strong>Wyciągi papierowe:</strong> 5-15 zł/miesiąc</li>
                    <li><strong>Przelewy związane z kredytem:</strong> 2-10 zł/przelew</li>
                    <li><strong>Zmiana warunków umowy:</strong> 100-500 zł</li>
                    <li><strong>Zaświadczenia:</strong> 20-100 zł/sztuka</li>
                    <li><strong>Łączne ukryte koszty:</strong> 1-3% kwoty kredytu</li>
                </ul>
            </div>

            <h3>Scenariusze kosztów kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Scenariusz niskich kosztów (280% kwoty kredytu):</h4>
                <ul>
                    <li><strong>Bank:</strong> Bank internetowy lub promocja</li>
                    <li><strong>Prowizja:</strong> 1.0% kwoty kredytu</li>
                    <li><strong>Ubezpieczenia:</strong> Tylko podstawowe</li>
                    <li><strong>Opłaty:</strong> Minimalne</li>
                    <li><strong>Przykład:</strong> 400 000 zł → całkowity koszt 1 120 000 zł</li>
                    <li><strong>RRSO:</strong> 7.8-8.2%</li>
                </ul>

                <h4>Scenariusz średnich kosztów (296% kwoty kredytu):</h4>
                <ul>
                    <li><strong>Bank:</strong> Bank tradycyjny, standardowe warunki</li>
                    <li><strong>Prowizja:</strong> 2.0% kwoty kredytu</li>
                    <li><strong>Ubezpieczenia:</strong> Standardowe</li>
                    <li><strong>Opłaty:</strong> Średnie</li>
                    <li><strong>Przykład:</strong> 400 000 zł → całkowity koszt 1 184 000 zł</li>
                    <li><strong>RRSO:</strong> 8.5-9.0%</li>
                </ul>

                <h4>Scenariusz wysokich kosztów (320% kwoty kredytu):</h4>
                <ul>
                    <li><strong>Bank:</strong> Bank premium lub niekorzystne warunki</li>
                    <li><strong>Prowizja:</strong> 3.0% kwoty kredytu</li>
                    <li><strong>Ubezpieczenia:</strong> Pełne pakiety</li>
                    <li><strong>Opłaty:</strong> Wysokie</li>
                    <li><strong>Przykład:</strong> 400 000 zł → całkowity koszt 1 280 000 zł</li>
                    <li><strong>RRSO:</strong> 9.5-10.5%</li>
                </ul>

                <h4>Scenariusz maksymalnych kosztów (330%+ kwoty kredytu):</h4>
                <ul>
                    <li><strong>Bank:</strong> Wszystkie opcjonalne usługi</li>
                    <li><strong>Prowizja:</strong> 3.0%+ kwoty kredytu</li>
                    <li><strong>Ubezpieczenia:</strong> Wszystkie dostępne</li>
                    <li><strong>Opłaty:</strong> Wszystkie dodatkowe usługi</li>
                    <li><strong>Przykład:</strong> 400 000 zł → całkowity koszt 1 320 000+ zł</li>
                    <li><strong>RRSO:</strong> 10.5%+</li>
                </ul>
            </div>

            <h3>RRSO - Rzeczywista Roczna Stopa Oprocentowania:</h3>
            <div style="margin: 15px 0;">
                <h4>Co to jest RRSO:</h4>
                <ul>
                    <li><strong>Definicja:</strong> Całkowity koszt kredytu wyrażony jako roczna stopa procentowa</li>
                    <li><strong>Zawiera:</strong> Oprocentowanie + wszystkie obowiązkowe opłaty</li>
                    <li><strong>Nie zawiera:</strong> Kosztów opcjonalnych (np. ubezpieczenie spłaty)</li>
                    <li><strong>Cel:</strong> Porównanie ofert różnych banków</li>
                </ul>

                <h4>Składniki RRSO:</h4>
                <ul>
                    <li><strong>Oprocentowanie nominalne:</strong> Podstawowa stopa kredytu</li>
                    <li><strong>Prowizja za udzielenie:</strong> Rozłożona na cały okres kredytu</li>
                    <li><strong>Opłata za prowadzenie rachunku:</strong> Jeśli obowiązkowa</li>
                    <li><strong>Obowiązkowe ubezpieczenia:</strong> Nieruchomości (jeśli wymagane przez bank)</li>
                    <li><strong>Koszty notarialne:</strong> Jeśli bank je narzuca</li>
                </ul>

                <h4>Interpretacja RRSO:</h4>
                <ul>
                    <li><strong>RRSO 7-8%:</strong> Bardzo dobra oferta</li>
                    <li><strong>RRSO 8-9%:</strong> Dobra oferta</li>
                    <li><strong>RRSO 9-10%:</strong> Średnia oferta rynkowa</li>
                    <li><strong>RRSO 10%+:</strong> Droga oferta</li>
                </ul>
            </div>

            <h3>Jak obniżyć koszt kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Negocjacje z bankiem:</h4>
                <ul>
                    <li><strong>Prowizja:</strong> Możliwość obniżenia o 0.5-1.0%</li>
                    <li><strong>Oprocentowanie:</strong> Zniżka za przeniesienie rachunku</li>
                    <li><strong>Opłaty:</strong> Bezpłatne prowadzenie rachunku</li>
                    <li><strong>Ubezpieczenia:</strong> Wybór własnego ubezpieczyciela</li>
                </ul>

                <h4>Wybór odpowiedniego banku:</h4>
                <ul>
                    <li><strong>Banki internetowe:</strong> Niższe koszty operacyjne</li>
                    <li><strong>Promocje:</strong> Czasowe obniżki prowizji</li>
                    <li><strong>Pakiety usług:</strong> Zniżki za kompleksową obsługę</li>
                    <li><strong>Status klienta:</strong> Preferencje dla stałych klientów</li>
                </ul>

                <h4>Optymalizacja struktury kredytu:</h4>
                <ul>
                    <li><strong>Wyższy wkład własny:</strong> Niższe LTV = niższe oprocentowanie</li>
                    <li><strong>Krótszy okres:</strong> Mniej odsetek, ale wyższa rata</li>
                    <li><strong>Nadpłaty:</strong> Systematyczne zmniejszanie kapitału</li>
                    <li><strong>Refinansowanie:</strong> Zmiana na lepsze warunki</li>
                </ul>
            </div>

            <h3>Porównanie kosztów w różnych bankach:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Typ banku</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Prowizja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Opłaty miesięczne</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">RRSO typowe</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Całkowity koszt</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bank internetowy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.0-1.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">15-25 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.8-8.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">280-290%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bank tradycyjny</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.5-2.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">20-40 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8.5-9.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">290-310%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bank premium</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.0-3.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30-60 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">9.0-10.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">310-330%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Spółdzielczy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.5-2.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">15-30 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8.0-9.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">285-300%</td>
                    </tr>
                </table>
            </div>

            <h3>Wskazówki dotyczące kosztów kredytu:</h3>
            <ul>
                <li><strong>Sprawdź RRSO:</strong> Najważniejszy wskaźnik do porównania ofert</li>
                <li><strong>Uwzględnij wszystkie koszty:</strong> Nie tylko oprocentowanie</li>
                <li><strong>Negocjuj warunki:</strong> Szczególnie prowizję i opłaty</li>
                <li><strong>Porównaj oferty:</strong> Minimum 3-4 banków</li>
                <li><strong>Uważaj na ukryte koszty:</strong> Sprawdź regulamin</li>
                <li><strong>Rozważ koszty opcjonalne:</strong> Czy są rzeczywiście potrzebne</li>
                <li><strong>Planuj długoterminowo:</strong> Koszt przez cały okres kredytu</li>
                <li><strong>Monitoruj rynek:</strong> Możliwość refinansowania</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Scenariusze kosztów</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Sprawdź koszty:</strong><br>
<a href="#" onclick="return setCostScenario('low');">Niskie koszty</a><br>
<a href="#" onclick="return setCostScenario('medium');">Średnie koszty</a><br>
<a href="#" onclick="return setCostScenario('high');">Wysokie koszty</a><br>
<a href="#" onclick="return setCostScenario('mandatory');">Tylko obowiązkowe</a><br>
<a href="#" onclick="return setCostScenario('full');">Wszystkie opcjonalne</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Koszt kredytu hipotecznego kalkulator - oblicz całkowity koszt kredytu mieszkaniowego z wszystkimi opłatami.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateMortgageCost() {
    // 获取基本信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var costAnalysis = document.getElementById('ccostanalysis').value;
    var costCategory = document.getElementById('ccostcategory').value;
    
    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 计算总利息
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    
    // 获取所有费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.notary = parseFloat(document.getElementById('cnotary').value) || 0;
        costs.landRegistry = parseFloat(document.getElementById('clandregistry').value) || 0;
        costs.homeInsurance = parseFloat(document.getElementById('chomeins').value) || 0;
        costs.lifeInsurance = loanAmount * (parseFloat(document.getElementById('clifeins').value) || 0) / 100;
        costs.account = parseFloat(document.getElementById('caccount').value) || 0;
        costs.other = parseFloat(document.getElementById('cother').value) || 0;
    } else {
        costs = {provision: 0, valuation: 0, notary: 0, landRegistry: 0, homeInsurance: 0, lifeInsurance: 0, account: 0, other: 0};
    }
    
    // 根据类别过滤费用
    var filteredCosts = filterCostsByCategory(costs, costCategory);
    
    // 计算总费用
    var oneTimeCosts = filteredCosts.provision + filteredCosts.valuation + filteredCosts.notary + filteredCosts.landRegistry;
    var annualCosts = filteredCosts.homeInsurance + filteredCosts.lifeInsurance;
    var monthlyCosts = filteredCosts.account + filteredCosts.other;
    
    var totalAnnualCosts = annualCosts * loanTerm;
    var totalMonthlyCosts = (monthlyCosts * 12) * loanTerm;
    var totalAdditionalCosts = oneTimeCosts + totalAnnualCosts + totalMonthlyCosts;
    
    // 计算总成本
    var totalCost = loanAmount + totalInterest + totalAdditionalCosts;
    var monthlyTotal = monthlyPayment + (annualCosts/12) + monthlyCosts;
    
    // 计算RRSO
    var rrso = calculateRRSO(loanAmount, monthlyTotal, numPayments, oneTimeCosts);
    
    // 计算成本分析
    var costAnalysisResult = performCostAnalysis(loanAmount, totalInterest, totalAdditionalCosts, costAnalysis);
    
    // 更新显示
    updateMortgageCostResults(loanAmount, totalInterest, filteredCosts, totalCost, 
                             monthlyTotal, rrso, loanTerm, costAnalysis, costAnalysisResult);
}

function filterCostsByCategory(costs, category) {
    var filtered = Object.assign({}, costs);
    
    switch(category) {
        case 'mandatory':
            // 只保留强制费用
            filtered.lifeInsurance = 0; // 生命保险通常是可选的
            filtered.other = 0; // 其他费用通常是可选的
            break;
        case 'optional':
            // 只保留可选费用
            filtered.provision = 0;
            filtered.valuation = 0;
            filtered.notary = 0;
            filtered.landRegistry = 0;
            filtered.homeInsurance = 0;
            filtered.account = 0;
            break;
        case 'hidden':
            // 只显示隐藏费用
            filtered.provision = 0;
            filtered.homeInsurance = 0;
            break;
        case 'recurring':
            // 只显示循环费用
            filtered.provision = 0;
            filtered.valuation = 0;
            filtered.notary = 0;
            filtered.landRegistry = 0;
            break;
    }
    
    return filtered;
}

function calculateRRSO(loanAmount, monthlyPayment, numPayments, oneTimeCosts) {
    // 简化的RRSO计算
    var totalPaid = (monthlyPayment * numPayments) + oneTimeCosts;
    var effectiveRate = Math.pow(totalPaid / loanAmount, 1 / (numPayments / 12)) - 1;
    return effectiveRate * 100;
}

function performCostAnalysis(loanAmount, totalInterest, totalAdditionalCosts, analysisType) {
    var analysis = {
        costOfMoney: totalInterest,
        additionalCosts: totalAdditionalCosts,
        effectiveCostRatio: ((loanAmount + totalInterest + totalAdditionalCosts) / loanAmount) * 100,
        recommendation: ''
    };
    
    switch(analysisType) {
        case 'detailed':
            analysis.recommendation = 'Szczegółowa analiza pokazuje wszystkie składniki kosztów kredytu';
            break;
        case 'comparative':
            analysis.recommendation = 'Porównaj z ofertami innych banków aby znaleźć najlepsze warunki';
            break;
        case 'rrso':
            analysis.recommendation = 'RRSO uwzględnia wszystkie koszty kredytu w jednej wartości';
            break;
        case 'lifetime':
            analysis.recommendation = 'Analiza całożyciowa pokazuje pełny koszt kredytu przez cały okres';
            break;
        default:
            analysis.recommendation = 'Podstawowa analiza kosztów kredytu hipotecznego';
    }
    
    return analysis;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;
    
    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateMortgageCostResults(loanAmount, totalInterest, costs, totalCost, 
                                  monthlyTotal, rrso, loanTerm, costAnalysis, costAnalysisResult) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Całkowity koszt: &nbsp; ' + formatNumber(totalCost) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新analiza kosztów
    var analysisSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (analysisSection) {
        var totalAdditionalCosts = costs.provision + costs.valuation + costs.notary + costs.landRegistry + 
                                  (costs.homeInsurance * loanTerm) + (costs.lifeInsurance * loanTerm) + 
                                  ((costs.account + costs.other) * 12 * loanTerm);
        
        analysisSection.innerHTML = 
            '<h3>Analiza kosztów kredytu hipotecznego:</h3>' +
            '<p><strong>RRSO:</strong> ' + rrso.toFixed(2) + '% (rzeczywista roczna stopa oprocentowania)</p>' +
            '<p><strong>Koszt pieniądza:</strong> ' + formatNumber(totalInterest + costs.provision) + ' zł (odsetki + prowizja)</p>' +
            '<p><strong>Koszty dodatkowe:</strong> ' + formatNumber(totalAdditionalCosts - costs.provision) + ' zł (ubezpieczenia + opłaty)</p>' +
            '<p><strong>Efektywny koszt kredytu:</strong> ' + Math.round(costAnalysisResult.effectiveCostRatio) + '% kwoty kredytu</p>' +
            '<p><strong>Miesięczny koszt:</strong> ' + formatNumber(monthlyTotal) + ' zł (rata + ubezpieczenia + opłaty)</p>' +
            '<p><strong>Typ analizy:</strong> ' + getCostAnalysisName(costAnalysis) + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + costAnalysisResult.recommendation + '</p>';
    }
}

function getCostAnalysisName(analysis) {
    switch(analysis) {
        case 'detailed': return 'Szczegółowa analiza kosztów';
        case 'comparative': return 'Porównanie z innymi bankami';
        case 'rrso': return 'Analiza RRSO';
        case 'lifetime': return 'Koszt przez cały okres';
        default: return 'Podstawowa analiza kosztów';
    }
}

function setCostScenario(scenario) {
    switch(scenario) {
        case 'low':
            document.getElementById('cprovision').value = '1.0';
            document.getElementById('chomeins').value = '800';
            document.getElementById('clifeins').value = '0.2';
            document.getElementById('caccount').value = '15';
            document.getElementById('cother').value = '20';
            break;
        case 'medium':
            document.getElementById('cprovision').value = '2.0';
            document.getElementById('chomeins').value = '1200';
            document.getElementById('clifeins').value = '0.4';
            document.getElementById('caccount').value = '25';
            document.getElementById('cother').value = '50';
            break;
        case 'high':
            document.getElementById('cprovision').value = '3.0';
            document.getElementById('chomeins').value = '1800';
            document.getElementById('clifeins').value = '0.6';
            document.getElementById('caccount').value = '40';
            document.getElementById('cother').value = '100';
            break;
        case 'mandatory':
            document.getElementById('cprovision').value = '2.0';
            document.getElementById('chomeins').value = '1200';
            document.getElementById('clifeins').value = '0';
            document.getElementById('caccount').value = '20';
            document.getElementById('cother').value = '0';
            document.getElementById('ccostcategory').value = 'mandatory';
            break;
        case 'full':
            document.getElementById('cprovision').value = '3.0';
            document.getElementById('chomeins').value = '2000';
            document.getElementById('clifeins').value = '0.8';
            document.getElementById('caccount').value = '50';
            document.getElementById('cother').value = '150';
            document.getElementById('ccostcategory').value = 'all';
            break;
    }
    calculateMortgageCost();
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Szczegółowe koszty kredytu';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Szczegółowe koszty kredytu';
    }
    calculateMortgageCost();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Scenariusze kosztów</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Scenariusze kosztów</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('cloanamount').value = '400000';
    document.getElementById('cinterestrate').value = '7.5';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('ccostanalysis').value = 'basic';
    document.getElementById('ccostcategory').value = 'all';
    
    document.getElementById('cprovision').value = '2.0';
    document.getElementById('cvaluation').value = '2500';
    document.getElementById('cnotary').value = '4000';
    document.getElementById('clandregistry').value = '200';
    document.getElementById('chomeins').value = '1200';
    document.getElementById('clifeins').value = '0.4';
    document.getElementById('caccount').value = '25';
    document.getElementById('cother').value = '50';
    
    calculateMortgageCost();
}

function saveCalResult() {
    alert('Analiza kosztów kredytu została zapisana!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie scenariuszy &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Struktura kosztów</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Struktura kosztów &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie scenariuszy</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateMortgageCost();
};
</script>

</body></html>
