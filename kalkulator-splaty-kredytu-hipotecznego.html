<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator spłaty kredytu hipotecznego</title>
    <meta name="description" content="Kalkulator spłaty kredytu hipotecznego - oblicz harmonogram spłat, sprawdź strukturę rat, porównaj różne strategie spłaty kredytu mieszkaniowego.">
    <meta name="keywords" content="kalkulator spłaty kredytu hipotecznego, harmonogram spłat, struktura raty, spłata kredytu mieszkaniowego, plan spłaty">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-splaty-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator spłaty kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator spłaty kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz harmonogram spłaty kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-splaty-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Kwota kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota kredytu hipotecznego do spłaty. Wprowadź aktualną kwotę kredytu lub planowaną kwotę nowego kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanamount" id="cloanamount" value="450000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Okres spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w latach. Wpływa na wysokość miesięcznej raty i całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu hipotecznego. Sprawdź aktualne stawki w bankach.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.3" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ spłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype" onchange="updatePaymentType();">
                                        <option value="annuity" selected="">Spłata annuitetowa (równe raty)</option>
                                        <option value="linear">Spłata liniowa (malejące raty)</option>
                                        <option value="progressive">Spłata progresywna (rosnące raty)</option>
                                        <option value="seasonal">Spłata sezonowa</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="progressivedetails" style="display:none;">
                                <td align="right">Wzrost raty rocznie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Procentowy wzrost raty każdego roku w przypadku spłaty progresywnej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cprogressiverate" id="cprogressiverate" value="3" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="seasonaldetails" style="display:none;">
                                <td align="right">Miesiące podwyższonej raty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba miesięcy w roku z podwyższoną ratą (np. po otrzymaniu premii).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cseasonalmonths" id="cseasonalmonths" value="2" class="inhalf"></td>
                                <td>miesięcy</td>
                            </tr>
                            <tr>
                                <td align="right">Data rozpoczęcia spłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cstartmonth" id="cstartmonth">
                                        <option value="1">Styczeń</option>
                                        <option value="2">Luty</option>
                                        <option value="3">Marzec</option>
                                        <option value="4">Kwiecień</option>
                                        <option value="5">Maj</option>
                                        <option value="6">Czerwiec</option>
                                        <option value="7">Lipiec</option>
                                        <option value="8" selected="">Sierpień</option>
                                        <option value="9">Wrzesień</option>
                                        <option value="10">Październik</option>
                                        <option value="11">Listopad</option>
                                        <option value="12">Grudzień</option>
                                    </select> 
                                    <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddoverpayments" class="cbcontainer">
                                        <input type="checkbox" name="caddoverpayments" id="caddoverpayments" value="1" onclick="cshoverpayments();">
                                        <span class="cbmark"></span>
                                        <b><span id="coverpaymentsdesc">Uwzględnij nadpłaty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="coverpayments" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Nadpłaty kredytu</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Miesięczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Stała miesięczna nadpłata kredytu ponad obowiązkową ratę.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmonthlyoverpayment" id="cmonthlyoverpayment" value="500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Roczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa nadpłata raz w roku (np. z premii rocznej, zwrotu podatku).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cyearlyoverpayment" id="cyearlyoverpayment" value="10000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Efekt nadpłaty</td>
                                                    <td align="left" colspan="2">
                                                        <select name="coverpaymenteffect" id="coverpaymenteffect">
                                                            <option value="reduce_term" selected="">Skrócenie okresu spłaty</option>
                                                            <option value="reduce_payment">Zmniejszenie raty</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="cadddetails" class="cbcontainer">
                                        <input type="checkbox" name="cadddetails" id="cadddetails" value="1" checked="" onclick="cshdetails();">
                                        <span class="cbmark"></span>
                                        <b><span id="cdetailsdesc">Szczegółowy harmonogram</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cdetails" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Widok harmonogramu</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cscheduleview" id="cscheduleview">
                                                            <option value="monthly" selected="">Miesięczny</option>
                                                            <option value="quarterly">Kwartalny</option>
                                                            <option value="yearly">Roczny</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Liczba okresów do wyświetlenia</td>
                                                    <td align="right"><input type="text" name="cperiodstoshow" id="cperiodstoshow" value="12" class="innormal"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz harmonogram spłaty" onclick="calculatePaymentSchedule();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 3 385 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBTcMWCYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 0, 'S2Fsa3VsYXRvciBTcMWCYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MyAzODUgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez nadpłat</b></td>
                                        <td align="right"><b>Z nadpłatami</b></td>
                                        <td align="right"><b>Oszczędność</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>3 385 zł</b></td>
                                        <td align="right"><b>3 885 zł</b></td>
                                        <td align="right"><b>+500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty</td>
                                        <td align="right">25 lat</td>
                                        <td align="right">18 lat 4 miesiące</td>
                                        <td align="right">6 lat 8 miesięcy</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">1 015 500 zł</td>
                                        <td align="right">847 200 zł</td>
                                        <td align="right">168 300 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">565 500 zł</td>
                                        <td align="right">397 200 zł</td>
                                        <td align="right">168 300 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Łączne nadpłaty</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">131 000 zł</td>
                                        <td align="right">-131 000 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza spłaty kredytu:</h3>
                                            <p><strong>Typ spłaty:</strong> Annuitetowa (równe raty)</p>
                                            <p><strong>Efektywność nadpłat:</strong> Każda złotówka nadpłaty oszczędza 1,28 zł odsetek</p>
                                            <p><strong>ROI nadpłat:</strong> 128% zwrotu z inwestycji</p>
                                            <p><strong>Skrócenie okresu:</strong> 6 lat 8 miesięcy wcześniejsze zakończenie</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Nadpłaty są bardzo opłacalne przy tym oprocentowaniu</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Harmonogram spłaty kredytu hipotecznego</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Rok</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">0 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">200K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">400K zł</text>
                    
                    <!-- Saldo kredytu -->
                    <path d="M 48 25 L 292 145" fill="none" stroke="#e74c3c" stroke-width="4"></path>
                    <!-- Spłacony kapitał -->
                    <path d="M 48 145 L 292 25" fill="none" stroke="#27ae60" stroke-width="3"></path>
                    <!-- Skumulowane odsetki -->
                    <path d="M 48 145 L 292 73" fill="none" stroke="#f39c12" stroke-width="3"></path>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                    <rect x="53" y="17" width="20" height="6" style="fill:#e74c3c;"></rect>
                    <text x="78" y="24" class="mcllegend">Saldo kredytu</text>
                    <rect x="53" y="35" width="20" height="6" style="fill:#27ae60;"></rect>
                    <text x="78" y="42" class="mcllegend">Spłacony kapitał</text>
                    <rect x="53" y="53" width="20" height="6" style="fill:#f39c12;"></rect>
                    <text x="78" y="60" class="mcllegend">Skumulowane odsetki</text>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Harmonogram miesięczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram roczny</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Rok</th>
                            <th>Rata łącznie</th>
                            <th>Kapitał</th>
                            <th>Odsetki</th>
                            <th>Saldo</th>
                            <th>% spłaty</th>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>40 620 zł</td>
                            <td>8 355 zł</td>
                            <td>32 265 zł</td>
                            <td>441 645 zł</td>
                            <td>1.9%</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>203 100 zł</td>
                            <td>48 720 zł</td>
                            <td>154 380 zł</td>
                            <td>401 280 zł</td>
                            <td>10.8%</td>
                        </tr>
                        <tr>
                            <td>10</td>
                            <td>406 200 zł</td>
                            <td>115 850 zł</td>
                            <td>290 350 zł</td>
                            <td>334 150 zł</td>
                            <td>25.7%</td>
                        </tr>
                        <tr>
                            <td>15</td>
                            <td>609 300 zł</td>
                            <td>205 420 zł</td>
                            <td>403 880 zł</td>
                            <td>244 580 zł</td>
                            <td>45.6%</td>
                        </tr>
                        <tr>
                            <td>20</td>
                            <td>812 400 zł</td>
                            <td>325 680 zł</td>
                            <td>486 720 zł</td>
                            <td>124 320 zł</td>
                            <td>72.4%</td>
                        </tr>
                        <tr>
                            <td>25</td>
                            <td>1 015 500 zł</td>
                            <td>450 000 zł</td>
                            <td>565 500 zł</td>
                            <td>0 zł</td>
                            <td>100%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator spłaty kredytu hipotecznego - kompleksowy harmonogram i analiza</h2>
            <p>Spłata kredytu hipotecznego to długoterminowy proces, który wymaga świadomego planowania i zrozumienia mechanizmów finansowych. Nasz kalkulator spłaty kredytu hipotecznego nie tylko generuje szczegółowy harmonogram spłat, ale także analizuje różne strategie spłaty i pokazuje wpływ nadpłat na całkowity koszt kredytu.</p>

            <h3>Rodzaje spłaty kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Spłata annuitetowa (równe raty):</h4>
                <ul>
                    <li><strong>Stała miesięczna rata</strong> - przez cały okres kredytowania</li>
                    <li><strong>Przewidywalność budżetu</strong> - łatwe planowanie wydatków</li>
                    <li><strong>Zmienna struktura</strong> - początkowo więcej odsetek, później więcej kapitału</li>
                    <li><strong>Najpopularniejsza</strong> - wybierana przez 95% kredytobiorców</li>
                    <li><strong>Stabilność finansowa</strong> - brak niespodzianek w budżecie domowym</li>
                </ul>

                <h4>Spłata liniowa (malejące raty):</h4>
                <ul>
                    <li><strong>Równa spłata kapitału</strong> - stała kwota kapitału każdego miesiąca</li>
                    <li><strong>Malejące raty</strong> - rata zmniejsza się z każdym miesiącem</li>
                    <li><strong>Niższy całkowity koszt</strong> - mniejsze odsetki łącznie</li>
                    <li><strong>Wyższa rata na początku</strong> - większe obciążenie w pierwszych latach</li>
                    <li><strong>Wymaga wyższej zdolności</strong> - bank sprawdza możliwość spłaty pierwszych rat</li>
                    <li><strong>Szybsza spłata kapitału</strong> - saldo kredytu szybciej maleje</li>
                </ul>

                <h4>Spłata progresywna (rosnące raty):</h4>
                <ul>
                    <li><strong>Wzrastające raty</strong> - rata rośnie co roku o określony procent</li>
                    <li><strong>Niższa rata na początku</strong> - łatwiejszy start dla młodych kredytobiorców</li>
                    <li><strong>Dostosowana do inflacji</strong> - wzrost wraz z przewidywanym wzrostem dochodów</li>
                    <li><strong>Ryzykowna opcja</strong> - wymaga pewności wzrostu dochodów</li>
                    <li><strong>Rzadko oferowana</strong> - tylko niektóre banki w Polsce</li>
                </ul>

                <h4>Spłata sezonowa:</h4>
                <ul>
                    <li><strong>Zmienna w roku</strong> - różne raty w różnych miesiącach</li>
                    <li><strong>Dostosowana do dochodów</strong> - wyższe raty po premii rocznej</li>
                    <li><strong>Elastyczność płatności</strong> - możliwość dopasowania do cyklu zarobków</li>
                    <li><strong>Wymaga dyscypliny</strong> - oszczędzanie na okresy wyższych rat</li>
                    <li><strong>Specjalistyczna oferta</strong> - dla klientów o sezonowych dochodach</li>
                </ul>
            </div>

            <h3>Harmonogram spłaty - kluczowe elementy:</h3>
            <div style="margin: 15px 0;">
                <h4>Struktura miesięcznej raty:</h4>
                <ul>
                    <li><strong>Kapitał</strong> - część raty spłacająca kwotę kredytu</li>
                    <li><strong>Odsetki</strong> - koszt korzystania z kredytu</li>
                    <li><strong>Ubezpieczenie</strong> - ochrona kredytu (opcjonalne)</li>
                    <li><strong>Prowizje</strong> - dodatkowe opłaty bankowe</li>
                </ul>

                <h4>Ewolucja struktury raty w czasie:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Okres</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Udział kapitału</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Udział odsetek</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Saldo kredytu</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">1. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">20.6%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">79.4%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">98.1%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">5. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">31.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">68.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">89.2%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">10. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">45.8%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">54.2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">74.3%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">15. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">62.4%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">37.6%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">54.4%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">20. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">78.2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">21.8%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">27.6%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">25. rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">95.1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4.9%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0%</td>
                    </tr>
                </table>
            </div>

            <h3>Strategie nadpłat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Rodzaje nadpłat:</h4>
                <ul>
                    <li><strong>Miesięczne nadpłaty</strong> - stała dodatkowa kwota każdego miesiąca</li>
                    <li><strong>Roczne nadpłaty</strong> - jednorazowa nadpłata raz w roku</li>
                    <li><strong>Nadpłaty okazjonalne</strong> - nieregularne nadpłaty z dodatkowych dochodów</li>
                    <li><strong>Nadpłaty progresywne</strong> - rosnące nadpłaty wraz z dochodami</li>
                </ul>

                <h4>Efekty nadpłat:</h4>
                <ul>
                    <li><strong>Skrócenie okresu</strong> - wcześniejsze zakończenie spłaty kredytu</li>
                    <li><strong>Zmniejszenie raty</strong> - obniżenie miesięcznych zobowiązań</li>
                    <li><strong>Efekt mieszany</strong> - częściowo okres, częściowo rata</li>
                </ul>

                <h4>Opłacalność nadpłat:</h4>
                <p>Nadpłaty są opłacalne, gdy:</p>
                <ul>
                    <li><strong>Oprocentowanie kredytu > stopa zwrotu z inwestycji</strong></li>
                    <li><strong>Prowizja za nadpłatę < 2% nadpłacanej kwoty</strong></li>
                    <li><strong>Masz stabilną sytuację finansową</strong></li>
                    <li><strong>Zachowujesz rezerwę na nieprzewidziane wydatki</strong></li>
                </ul>
            </div>

            <h3>Analiza kosztów spłaty kredytu:</h3>
            <ul>
                <li><strong>Całkowity koszt kredytu</strong> - suma wszystkich rat przez cały okres</li>
                <li><strong>Całkowite odsetki</strong> - różnica między kosztem a kwotą kredytu</li>
                <li><strong>Efektywna stopa procentowa</strong> - rzeczywisty koszt kredytu</li>
                <li><strong>Koszt alternatywny</strong> - utracone korzyści z innych inwestycji</li>
                <li><strong>Wpływ inflacji</strong> - realny koszt kredytu w czasie</li>
            </ul>

            <h3>Optymalizacja spłaty kredytu:</h3>
            <div style="margin: 15px 0;">
                <h4>Sposoby obniżenia kosztów:</h4>
                <ul>
                    <li><strong>Regularne nadpłaty</strong> - nawet 200-500 zł miesięcznie</li>
                    <li><strong>Refinansowanie</strong> - zmiana banku na lepsze warunki</li>
                    <li><strong>Renegocjacja warunków</strong> - negocjacje z obecnym bankiem</li>
                    <li><strong>Wykorzystanie premii</strong> - nadpłaty z premii rocznej</li>
                    <li><strong>Zwrot podatku</strong> - nadpłaty z rozliczenia PIT</li>
                </ul>

                <h4>Błędy w spłacie kredytu:</h4>
                <ul>
                    <li><strong>Brak nadpłat</strong> - przepłacanie niepotrzebnych odsetek</li>
                    <li><strong>Nadpłaty z prowizją</strong> - płacenie za nadpłaty powyżej limitu</li>
                    <li><strong>Ignorowanie refinansowania</strong> - brak monitorowania rynku</li>
                    <li><strong>Brak planowania</strong> - chaotyczne podejście do spłaty</li>
                    <li><strong>Wykorzystanie całej zdolności</strong> - brak rezerwy na nadpłaty</li>
                </ul>
            </div>

            <h3>Porady ekspertów dotyczące spłaty:</h3>
            <ul>
                <li>Zawsze analizuj harmonogram spłaty przed podpisaniem umowy</li>
                <li>Planuj nadpłaty już od pierwszego roku kredytowania</li>
                <li>Monitoruj rynek i rozważ refinansowanie co 2-3 lata</li>
                <li>Wykorzystuj dodatkowe dochody na nadpłaty kredytu</li>
                <li>Zachowaj równowagę między nadpłatami a rezerwą finansową</li>
                <li>Sprawdzaj warunki nadpłat w umowie kredytowej</li>
                <li>Rozważ ubezpieczenie spłaty kredytu w pierwszych latach</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/rata-kredytu-hipotecznego-kalkulator.html">Rata kredytu kalkulator</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">Wcześniejsza spłata</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Typy spłaty</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz typ spłaty:</strong><br>
                        <a href="#" onclick="return setPaymentType('annuity');">Annuitetowa</a><br>
                        <a href="#" onclick="return setPaymentType('linear');">Liniowa</a><br>
                        <a href="#" onclick="return setPaymentType('progressive');">Progresywna</a><br>
                        <a href="#" onclick="return setPaymentType('seasonal');">Sezonowa</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator spłaty kredytu hipotecznego - oblicz harmonogram spłat, sprawdź strukturę rat i porównaj różne strategie spłaty kredytu mieszkaniowego.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePaymentType() {
    var type = document.getElementById('cpaymenttype').value;
    var progressive = document.getElementById('progressivedetails');
    var seasonal = document.getElementById('seasonaldetails');
    
    progressive.style.display = (type === 'progressive') ? 'table-row' : 'none';
    seasonal.style.display = (type === 'seasonal') ? 'table-row' : 'none';
}

function cshoverpayments() {
    var checkbox = document.getElementById('caddoverpayments');
    var overpaymentsDiv = document.getElementById('coverpayments');
    if (checkbox.checked) {
        overpaymentsDiv.style.display = 'block';
    } else {
        overpaymentsDiv.style.display = 'none';
    }
}

function cshdetails() {
    var checkbox = document.getElementById('cadddetails');
    var detailsDiv = document.getElementById('cdetails');
    if (checkbox.checked) {
        detailsDiv.style.display = 'block';
    } else {
        detailsDiv.style.display = 'none';
    }
}

function setPaymentType(type) {
    var select = document.getElementById('cpaymenttype');
    select.value = type;
    updatePaymentType();
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Harmonogram miesięczny</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Harmonogram miesięczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram roczny</a>';
    }
    return false;
}
</script>

<script>
function calculatePaymentSchedule() {
    // 获取输入值
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 生成还款计划
    var schedule = [];
    var remainingBalance = loanAmount;
    var totalInterest = 0;
    var totalPrincipal = 0;

    for (var i = 1; i <= numPayments; i++) {
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;

        // 确保最后一期不会出现负余额
        if (i === numPayments) {
            monthlyPrincipal = remainingBalance;
            monthlyPayment = monthlyPrincipal + monthlyInterest;
        }

        remainingBalance -= monthlyPrincipal;
        totalInterest += monthlyInterest;
        totalPrincipal += monthlyPrincipal;

        // 只保存关键月份的数据
        if (i <= 12 || i % 12 === 0 || i === numPayments) {
            schedule.push({
                month: i,
                payment: monthlyPayment,
                principal: monthlyPrincipal,
                interest: monthlyInterest,
                balance: Math.max(0, remainingBalance)
            });
        }
    }

    // 更新显示
    updatePaymentScheduleResults(loanAmount, monthlyPayment, totalInterest, totalPrincipal,
                               schedule, loanTerm);
}

function updatePaymentScheduleResults(loanAmount, monthlyPayment, totalInterest, totalPrincipal,
                                    schedule, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Harmonogram spłaty: &nbsp; ' + formatNumber(monthlyPayment) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 3) {
                switch(i) {
                    case 2: // Miesięczna rata
                        cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment * loanTerm * 12) + ' zł</b>';
                        break;
                    case 3: // Kwota kredytu
                        cells[2].innerHTML = formatNumber(loanAmount) + ' zł';
                        break;
                    case 4: // Łączne odsetki
                        cells[2].innerHTML = formatNumber(totalInterest) + ' zł';
                        break;
                    case 5: // Łączna spłata kapitału
                        cells[2].innerHTML = formatNumber(totalPrincipal) + ' zł';
                        break;
                }
            }
        }
    }

    // 更新harmonogram spłaty
    var scheduleSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (scheduleSection) {
        var scheduleHtml = '<h3>Harmonogram spłaty kredytu:</h3>';
        scheduleHtml += '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';
        scheduleHtml += '<tr style="background: #e0e0e0;"><th style="border: 1px solid #ccc; padding: 5px;">Miesiąc</th><th style="border: 1px solid #ccc; padding: 5px;">Rata</th><th style="border: 1px solid #ccc; padding: 5px;">Kapitał</th><th style="border: 1px solid #ccc; padding: 5px;">Odsetki</th><th style="border: 1px solid #ccc; padding: 5px;">Saldo</th></tr>';

        for (var i = 0; i < Math.min(schedule.length, 10); i++) {
            var item = schedule[i];
            scheduleHtml += '<tr>';
            scheduleHtml += '<td style="border: 1px solid #ccc; padding: 5px; text-align: center;">' + item.month + '</td>';
            scheduleHtml += '<td style="border: 1px solid #ccc; padding: 5px; text-align: right;">' + formatNumber(item.payment) + ' zł</td>';
            scheduleHtml += '<td style="border: 1px solid #ccc; padding: 5px; text-align: right;">' + formatNumber(item.principal) + ' zł</td>';
            scheduleHtml += '<td style="border: 1px solid #ccc; padding: 5px; text-align: right;">' + formatNumber(item.interest) + ' zł</td>';
            scheduleHtml += '<td style="border: 1px solid #ccc; padding: 5px; text-align: right;">' + formatNumber(item.balance) + ' zł</td>';
            scheduleHtml += '</tr>';
        }

        scheduleHtml += '</table>';
        scheduleHtml += '<p><strong>Podsumowanie:</strong> Harmonogram pokazuje rozkład spłat przez ' + loanTerm + ' lat (' + (loanTerm * 12) + ' miesięcy)</p>';

        scheduleSection.innerHTML = scheduleHtml;
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    calculatePaymentSchedule();
}

function saveCalResult() {
    alert('Harmonogram spłaty kredytu został zapisany!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePaymentSchedule();
};
</script>

</body>
</html>
