<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator rat kredytu hipotecznego</title>
    <meta name="description" content="Kalkulator rat kredytu hipotecznego - oblicz miesięczną ratę kredytu mieszkaniowego, porównaj różne okresy spłaty i sprawdź wpływ oprocentowania na wysokość raty.">
    <meta name="keywords" content="kalkulator rat kredytu hipotecznego, miesięczna rata kredytu, rata kredytu mieszkaniowego, oblicz ratę kredytu, wysoko<PERSON><PERSON> raty">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-rat-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator rat kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator rat kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz miesięczną ratę kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-rat-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Kwota kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota kredytu hipotecznego, którą chcesz wziąć. To różnica między ceną nieruchomości a wkładem własnym.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanamount" id="cloanamount" value="500000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czas spłaty kredytu w latach. Dłuższy okres oznacza niższą ratę, ale wyższy całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu hipotecznego. Sprawdź aktualne stawki w bankach.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.5" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ raty</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype" onchange="updatePaymentType();">
                                        <option value="annuity" selected="">Rata annuitetowa (stała)</option>
                                        <option value="decreasing">Rata malejąca</option>
                                        <option value="interest_only">Tylko odsetki (na początku)</option>
                                        <option value="balloon">Rata balonowa</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="balloonpayment" style="display:none;">
                                <td align="right">Płatność końcowa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Duża płatność na końcu okresu kredytowania w przypadku raty balonowej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cballoonamount" id="cballoonamount" value="100000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="interestonlyperiod" style="display:none;">
                                <td align="right">Okres tylko odsetki <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba lat na początku, gdy płacisz tylko odsetki bez spłaty kapitału.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestonlyterm" id="cinterestonlyterm" value="3" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="ccomparerates" class="cbcontainer">
                                        <input type="checkbox" name="ccomparerates" id="ccomparerates" value="1" onclick="cshrates();">
                                        <span class="cbmark"></span>
                                        <b><span id="cratesdesc">Porównaj różne oprocentowania</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="crates" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Porównanie stawek</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Oprocentowanie 1 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwsza stawka do porównania - np. oferta banku A.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="crate1" id="crate1" value="7.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Oprocentowanie 2 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Druga stawka do porównania - np. oferta banku B.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="crate2" id="crate2" value="8.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Oprocentowanie 3 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzecia stawka do porównania - np. oferta banku C.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="crate3" id="crate3" value="7.8" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="ccompareterms" class="cbcontainer">
                                        <input type="checkbox" name="ccompareterms" id="ccompareterms" value="1" onclick="cshterms();">
                                        <span class="cbmark"></span>
                                        <b><span id="ctermsdesc">Porównaj różne okresy spłaty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cterms" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Porównanie okresów</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Okres 1 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwszy okres do porównania - np. 15 lat.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cterm1" id="cterm1" value="15" class="innormal"></td>
                                                    <td>lat</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Okres 2 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Drugi okres do porównania - np. 20 lat.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cterm2" id="cterm2" value="20" class="innormal"></td>
                                                    <td>lat</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Okres 3 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzeci okres do porównania - np. 30 lat.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cterm3" id="cterm3" value="30" class="innormal"></td>
                                                    <td>lat</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz ratę kredytu" onclick="calculateLoanPayment();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 3 721 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBSYXQgS3JlZHl0dSBIaXBvdGVjem5lZ28=', 0, 'S2Fsa3VsYXRvciBSYXQgS3JlZHl0dSBIaXBvdGVjem5lZ28=', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MyA3MjEgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Wartość</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata kredytu</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>3 721 zł</b></td>
                                        <td align="right"><b>1 116 300 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał (spłata)</td>
                                        <td align="right">500 000 zł</td>
                                        <td align="right">1 667 zł</td>
                                        <td align="right">500 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Odsetki</td>
                                        <td align="right">-</td>
                                        <td align="right">2 054 zł</td>
                                        <td align="right">616 300 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Stosunek odsetek do kapitału</td>
                                        <td align="right">123.3%</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr>
                                        <td>Efektywne oprocentowanie</td>
                                        <td align="right">7.5%</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza raty kredytu:</h3>
                                            <p><strong>Typ raty:</strong> Annuitetowa (stała przez cały okres)</p>
                                            <p><strong>Udział kapitału w racie:</strong> 44.8% (wzrasta w czasie)</p>
                                            <p><strong>Udział odsetek w racie:</strong> 55.2% (maleje w czasie)</p>
                                            <p><strong>Całkowity koszt kredytu:</strong> 1 116 300 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 616 300 zł (123% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie rat dla różnych parametrów</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Oprocentowanie</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3000 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">3500 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4000 zł</text>
                    <line x1="45" y1="37.542" x2="292" y2="37.542" class="mclgrid"></line>
                    <text x="43" y="37.542" class="mcllabely">4500 zł</text>
                    
                    <!-- 7.0% -->
                    <rect x="60" y="95" width="40" height="50" fill="#4ecdc4" opacity="0.8"></rect>
                    <text x="80" y="155" class="mcllabelx">7.0%</text>
                    <text x="80" y="90" class="mcllabelx" style="fill:#000;">3542 zł</text>
                    
                    <!-- 7.5% -->
                    <rect x="120" y="85" width="40" height="60" fill="#45b7d1" opacity="0.8"></rect>
                    <text x="140" y="155" class="mcllabelx">7.5%</text>
                    <text x="140" y="80" class="mcllabelx" style="fill:#000;">3721 zł</text>
                    
                    <!-- 8.0% -->
                    <rect x="180" y="75" width="40" height="70" fill="#f39c12" opacity="0.8"></rect>
                    <text x="200" y="155" class="mcllabelx">8.0%</text>
                    <text x="200" y="70" class="mcllabelx" style="fill:#000;">3905 zł</text>
                    
                    <!-- 7.8% -->
                    <rect x="240" y="78" width="40" height="67" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="260" y="155" class="mcllabelx">7.8%</text>
                    <text x="260" y="73" class="mcllabelx" style="fill:#000;">3851 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Porównanie oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie okresów</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Okres</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Całkowite odsetki</th>
                            <th>% odsetek</th>
                        </tr>
                        <tr>
                            <td>15 lat</td>
                            <td>4 635 zł</td>
                            <td>834 300 zł</td>
                            <td>334 300 zł</td>
                            <td>66.9%</td>
                        </tr>
                        <tr>
                            <td>20 lat</td>
                            <td>4 028 zł</td>
                            <td>966 720 zł</td>
                            <td>466 720 zł</td>
                            <td>93.3%</td>
                        </tr>
                        <tr>
                            <td>25 lat</td>
                            <td>3 721 zł</td>
                            <td>1 116 300 zł</td>
                            <td>616 300 zł</td>
                            <td>123.3%</td>
                        </tr>
                        <tr>
                            <td>30 lat</td>
                            <td>3 496 zł</td>
                            <td>1 258 560 zł</td>
                            <td>758 560 zł</td>
                            <td>151.7%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator rat kredytu hipotecznego - jak obliczyć miesięczną ratę?</h2>
            <p>Miesięczna rata kredytu hipotecznego to najważniejszy parametr przy planowaniu zakupu nieruchomości. Nasz kalkulator rat kredytu hipotecznego pomoże Ci precyzyjnie obliczyć wysokość miesięcznej raty, porównać różne opcje spłaty i wybrać najlepsze warunki kredytu mieszkaniowego.</p>

            <h3>Rodzaje rat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Rata annuitetowa (stała):</h4>
                <ul>
                    <li><strong>Stała wysokość</strong> - przez cały okres kredytowania</li>
                    <li><strong>Łatwość planowania</strong> - przewidywalny miesięczny budżet</li>
                    <li><strong>Zmienny udział</strong> - początkowo więcej odsetek, potem więcej kapitału</li>
                    <li><strong>Najpopularniejsza</strong> - wybierana przez 90% kredytobiorców</li>
                    <li><strong>Stabilność</strong> - brak zmian wysokości raty w czasie</li>
                </ul>

                <h4>Rata malejąca:</h4>
                <ul>
                    <li><strong>Zmniejszająca się</strong> - rata maleje z każdym miesiącem</li>
                    <li><strong>Stały kapitał</strong> - równa spłata kapitału każdego miesiąca</li>
                    <li><strong>Malejące odsetki</strong> - odsetki liczone od malejącego salda</li>
                    <li><strong>Wyższa na początku</strong> - większe obciążenie w pierwszych latach</li>
                    <li><strong>Niższy całkowity koszt</strong> - mniejsze odsetki łącznie</li>
                </ul>

                <h4>Rata tylko odsetki:</h4>
                <ul>
                    <li><strong>Początkowy okres</strong> - płacisz tylko odsetki (2-5 lat)</li>
                    <li><strong>Niższa rata</strong> - na początku kredytowania</li>
                    <li><strong>Brak spłaty kapitału</strong> - saldo kredytu nie maleje</li>
                    <li><strong>Wyższa rata później</strong> - po okresie tylko odsetek</li>
                    <li><strong>Ryzykowna opcja</strong> - wymaga dyscypliny finansowej</li>
                </ul>

                <h4>Rata balonowa:</h4>
                <ul>
                    <li><strong>Niskie raty</strong> - przez większość okresu kredytowania</li>
                    <li><strong>Duża płatność końcowa</strong> - znaczna kwota na końcu</li>
                    <li><strong>Refinansowanie</strong> - często wymaga nowego kredytu</li>
                    <li><strong>Ryzyko</strong> - trudności z płatnością końcową</li>
                    <li><strong>Rzadko stosowana</strong> - w Polsce mało popularna</li>
                </ul>
            </div>

            <h3>Czynniki wpływające na wysokość raty:</h3>
            <div style="margin: 15px 0;">
                <h4>Kwota kredytu:</h4>
                <ul>
                    <li><strong>Bezpośredni wpływ</strong> - wyższa kwota = wyższa rata</li>
                    <li><strong>Proporcjonalność</strong> - podwojenie kwoty podwaja ratę</li>
                    <li><strong>Optymalizacja</strong> - zwiększ wkład własny dla niższej raty</li>
                </ul>

                <h4>Okres kredytowania:</h4>
                <ul>
                    <li><strong>Odwrotna zależność</strong> - dłuższy okres = niższa rata</li>
                    <li><strong>Znaczący wpływ</strong> - różnica 5 lat może zmienić ratę o 20%</li>
                    <li><strong>Kompromis</strong> - niższa rata vs wyższy całkowity koszt</li>
                </ul>

                <h4>Oprocentowanie:</h4>
                <ul>
                    <li><strong>Kluczowy czynnik</strong> - każdy 1% to około 10% różnicy w racie</li>
                    <li><strong>Zmienność</strong> - oprocentowanie zmienne może się zmieniać</li>
                    <li><strong>Negocjacje</strong> - marża bankowa często do negocjacji</li>
                </ul>
            </div>

            <h3>Porównanie rat dla różnych parametrów:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Kwota kredytu</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Okres</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Miesięczna rata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Całkowity koszt</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">300 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>2 233 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">669 900 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">400 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>2 977 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">893 100 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">500 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>3 721 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 116 300 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">500 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">20 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>4 028 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">966 720 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">500 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>3 496 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 258 560 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Jak obniżyć miesięczną ratę kredytu?</h3>
            <ul>
                <li><strong>Wydłuż okres kredytowania</strong> - 30 lat zamiast 20 lat</li>
                <li><strong>Zwiększ wkład własny</strong> - mniejsza kwota kredytu</li>
                <li><strong>Negocjuj oprocentowanie</strong> - porównaj oferty banków</li>
                <li><strong>Wybierz ratę malejącą</strong> - niższa rata pod koniec</li>
                <li><strong>Rozważ współkredytobiorcę</strong> - wyższa zdolność kredytowa</li>
                <li><strong>Poczekaj na spadek stóp</strong> - niższe oprocentowanie</li>
                <li><strong>Skorzystaj z promocji</strong> - specjalne oferty banków</li>
            </ul>

            <h3>Wzór na obliczenie raty annuitetowej:</h3>
            <p>Rata annuitetowa obliczana jest według wzoru:</p>
            <div style="background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba;">
                <strong>R = K × [r × (1 + r)^n] / [(1 + r)^n - 1]</strong><br><br>
                Gdzie:<br>
                • R = miesięczna rata<br>
                • K = kwota kredytu<br>
                • r = miesięczna stopa procentowa (roczna ÷ 12)<br>
                • n = liczba miesięcy spłaty
            </div>

            <h3>Struktura raty kredytu w czasie:</h3>
            <div style="margin: 15px 0;">
                <h4>Początek kredytowania:</h4>
                <ul>
                    <li><strong>Wysokie odsetki</strong> - 70-80% raty to odsetki</li>
                    <li><strong>Niski kapitał</strong> - 20-30% raty to spłata kapitału</li>
                    <li><strong>Powolne zmniejszanie salda</strong> - kredyt spłaca się wolno</li>
                </ul>

                <h4>Środek okresu kredytowania:</h4>
                <ul>
                    <li><strong>Równowaga</strong> - około 50% odsetki, 50% kapitał</li>
                    <li><strong>Przyspieszenie</strong> - saldo zaczyna szybciej maleć</li>
                    <li><strong>Punkt zwrotny</strong> - kapitał zaczyna dominować</li>
                </ul>

                <h4>Koniec kredytowania:</h4>
                <ul>
                    <li><strong>Niskie odsetki</strong> - 20-30% raty to odsetki</li>
                    <li><strong>Wysoki kapitał</strong> - 70-80% raty to spłata kapitału</li>
                    <li><strong>Szybka spłata</strong> - saldo szybko maleje</li>
                </ul>
            </div>

            <h3>Porady dotyczące wyboru raty:</h3>
            <ul>
                <li><strong>Nie wykorzystuj całej zdolności kredytowej</strong> - zostaw rezerwę 20-30%</li>
                <li><strong>Uwzględnij inne wydatki</strong> - podatki, ubezpieczenia, utrzymanie</li>
                <li><strong>Planuj na przyszłość</strong> - zmiany dochodów, dzieci, emerytury</li>
                <li><strong>Rozważ nadpłaty</strong> - możliwość wcześniejszej spłaty</li>
                <li><strong>Sprawdź elastyczność</strong> - możliwość zawieszenia rat</li>
                <li><strong>Porównaj całkowity koszt</strong> - nie tylko wysokość raty</li>
                <li><strong>Symuluj różne scenariusze</strong> - użyj kalkulatora do porównań</li>
            </ul>

            <h3>Błędy przy wyborze raty kredytu:</h3>
            <ul>
                <li><strong>Skupienie tylko na racie</strong> - ignorowanie całkowitego kosztu</li>
                <li><strong>Maksymalne wykorzystanie zdolności</strong> - brak rezerwy finansowej</li>
                <li><strong>Ignorowanie inflacji</strong> - rata może być łatwiejsza w przyszłości</li>
                <li><strong>Brak porównania ofert</strong> - wybór pierwszej propozycji</li>
                <li><strong>Nieuwzględnienie kosztów dodatkowych</strong> - podatki, ubezpieczenia</li>
                <li><strong>Zbyt krótki okres</strong> - niepotrzebnie wysoka rata</li>
                <li><strong>Zbyt długi okres</strong> - przepłacanie odsetek</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/szczegolowy-kalkulator-kredytu-hipotecznego.html">Szczegółowy kalkulator</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">Wcześniejsza spłata</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Szybkie porównanie</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Różne okresy:</strong><br>
                        15 lat: <a href="#" onclick="return setTerm('15');">4 635 zł</a><br>
                        20 lat: <a href="#" onclick="return setTerm('20');">4 028 zł</a><br>
                        25 lat: <a href="#" onclick="return setTerm('25');">3 721 zł</a><br>
                        30 lat: <a href="#" onclick="return setTerm('30');">3 496 zł</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator rat kredytu hipotecznego - oblicz miesięczną ratę kredytu mieszkaniowego i porównaj różne opcje spłaty.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePaymentType() {
    var type = document.getElementById('cpaymenttype').value;
    var balloon = document.getElementById('balloonpayment');
    var interestOnly = document.getElementById('interestonlyperiod');
    
    balloon.style.display = (type === 'balloon') ? 'table-row' : 'none';
    interestOnly.style.display = (type === 'interest_only') ? 'table-row' : 'none';
}

function cshrates() {
    var checkbox = document.getElementById('ccomparerates');
    var ratesDiv = document.getElementById('crates');
    if (checkbox.checked) {
        ratesDiv.style.display = 'block';
    } else {
        ratesDiv.style.display = 'none';
    }
}

function cshterms() {
    var checkbox = document.getElementById('ccompareterms');
    var termsDiv = document.getElementById('cterms');
    if (checkbox.checked) {
        termsDiv.style.display = 'block';
    } else {
        termsDiv.style.display = 'none';
    }
}

function setTerm(term) {
    document.getElementById('cloanterm').value = term;
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie okresów &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Porównanie oprocentowania</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Porównanie oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie okresów</a>';
    }
    return false;
}
</script>

<script>
function calculateLoanPayment() {
    // 获取输入值
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }

    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // 更新显示
    updateLoanPaymentResults(loanAmount, monthlyPayment, totalInterest, costs,
                           totalCost, monthlyTotal, rrso, loanTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateLoanPaymentResults(loanAmount, monthlyPayment, totalInterest, costs,
                                totalCost, monthlyTotal, rrso, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Rata kredytu: &nbsp; ' + formatNumber(monthlyPayment) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 3) {
                switch(i) {
                    case 2: // 主要月供行
                        cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment * loanTerm * 12) + ' zł</b>';
                        break;
                    case 3: // 贷款金额
                        cells[2].innerHTML = formatNumber(loanAmount) + ' zł';
                        break;
                    case 4: // 利息
                        cells[2].innerHTML = formatNumber(totalInterest) + ' zł';
                        break;
                    case 5: // 手续费
                        cells[2].innerHTML = formatNumber(costs.provision) + ' zł';
                        break;
                    case 6: // 房产保险
                        cells[1].innerHTML = formatNumber(costs.homeInsurance/12) + ' zł';
                        cells[2].innerHTML = formatNumber(costs.homeInsurance * loanTerm) + ' zł';
                        break;
                    case 7: // 还款保险
                        cells[1].innerHTML = formatNumber(costs.lifeInsurance/12) + ' zł';
                        cells[2].innerHTML = formatNumber(costs.lifeInsurance * loanTerm) + ' zł';
                        break;
                    case 8: // 账户费
                        cells[1].innerHTML = formatNumber(costs.accountFee) + ' zł';
                        cells[2].innerHTML = formatNumber(costs.accountFee * 12 * loanTerm) + ' zł';
                        break;
                }
            }
        }
    }

    // 更新szczegóły raty kredytu
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegóły raty kredytu:</h3>' +
            '<p><strong>RRSO:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi kosztami)</p>' +
            '<p><strong>Miesięczna rata kapitałowo-odsetkowa:</strong> ' + formatNumber(monthlyPayment) + ' zł</p>' +
            '<p><strong>Miesięczne koszty dodatkowe:</strong> ' + formatNumber(monthlyTotal - monthlyPayment) + ' zł</p>' +
            '<p><strong>Całkowita miesięczna rata:</strong> ' + formatNumber(monthlyTotal) + ' zł</p>' +
            '<p><strong>Całkowity koszt kredytu:</strong> ' + formatNumber(totalCost) + ' zł</p>';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';

    if (document.getElementById('cprovision')) document.getElementById('cprovision').value = '2.0';
    if (document.getElementById('chomeins')) document.getElementById('chomeins').value = '1200';
    if (document.getElementById('cpmi')) document.getElementById('cpmi').value = '0.35';
    if (document.getElementById('choa')) document.getElementById('choa').value = '20';
    if (document.getElementById('cothercost')) document.getElementById('cothercost').value = '5000';

    calculateLoanPayment();
}

function saveCalResult() {
    alert('Wyniki kalkulatora rat kredytu zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateLoanPayment();
};
</script>

</body>
</html>
