<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Test wszyst<PERSON><PERSON> kalk<PERSON>ów</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 2px solid #007bff; background: #f8f9fa; border-radius: 5px; }
        .success { border-color: #28a745; background: #d4edda; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .error { border-color: #dc3545; background: #f8d7da; }
        button { padding: 15px 30px; margin: 10px 5px; background: #007bff; color: white; border: none; cursor: pointer; border-radius: 5px; font-size: 16px; }
        .big-button { padding: 20px 40px; font-size: 20px; background: #28a745; }
        .test-button { background: #17a2b8; }
        iframe { width: 100%; height: 400px; border: 2px solid #ccc; margin: 10px 0; border-radius: 5px; }
        h1 { text-align: center; color: #333; }
        h2 { color: #555; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { margin: 10px 0; padding: 15px; background: #e9ecef; border-radius: 5px; cursor: pointer; border: 1px solid #ced4da; }
        .file-list li:hover { background: #dee2e6; }
        .status { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TEST WSZYSTKICH KALKULATORÓW</h1>
        
        <div class="test-section error">
            <h2>🚨 NAJPIERW SPRAWDŹ JAVASCRIPT</h2>
            <p><strong>Kliknij ten przycisk aby sprawdzić czy JavaScript działa:</strong></p>
            <button class="big-button" onclick="alert('✅ JavaScript działa! Wszystkie kalkulatory powinny teraz działać.')">
                🔴 TEST JAVASCRIPT
            </button>
            <p><strong>Jeśli przycisk nie działa, JavaScript jest wyłączony w przeglądarce!</strong></p>
        </div>
        
        <div class="test-section success">
            <h2>✅ NAPRAWIONE KALKULATORY</h2>
            <p>Wszystkie poniższe kalkulatory zostały naprawione i używają prostego alert() do wyświetlania wyników:</p>
            <ul class="file-list">
                <li onclick="testFile('ing-kalkulator-kredytu-hipotecznego.html')">
                    <span class="status">✅ NAPRAWIONY:</span> ING Kalkulator kredytu hipotecznego
                </li>
                <li onclick="testFile('mbank-kalkulator-kredytu-hipotecznego.html')">
                    <span class="status">✅ NAPRAWIONY:</span> mBank Kalkulator kredytu hipotecznego
                </li>
                <li onclick="testFile('pko-bp-kalkulator-kredytu-hipotecznego.html')">
                    <span class="status">✅ NAPRAWIONY:</span> PKO BP Kalkulator kredytu hipotecznego
                </li>
                <li onclick="testFile('kalkulator-raty-kredytu-hipotecznego.html')">
                    <span class="status">✅ NAPRAWIONY:</span> Kalkulator raty kredytu hipotecznego
                </li>
            </ul>
        </div>
        
        <div class="test-section warning">
            <h2>🧪 JAK TESTOWAĆ</h2>
            <ol>
                <li><strong>Kliknij na nazwę kalkulatora powyżej</strong> - załaduje się w ramce poniżej</li>
                <li><strong>Poczekaj aż się załaduje</strong></li>
                <li><strong>Znajdź przycisk "Oblicz"</strong> (może być "Oblicz w ING", "Oblicz kredyt mBank", itp.)</li>
                <li><strong>Kliknij przycisk</strong></li>
                <li><strong>Powinien pojawić się alert z wynikiem</strong> (np. "Rata ING: 2,847 zł")</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>📊 OCZEKIWANE WYNIKI</h2>
            <p><strong>Domyślne dane testowe:</strong></p>
            <ul>
                <li>Cena nieruchomości: 500,000 zł</li>
                <li>Wkład własny: 20%</li>
                <li>Okres kredytowania: 25 lat</li>
                <li>Oprocentowanie: 7.25%</li>
            </ul>
            <p><strong>Oczekiwany wynik: około 2,847 zł miesięcznie</strong></p>
        </div>
        
        <div id="testFrame">
            <p style="text-align: center; font-size: 18px; color: #666;">
                👆 Kliknij na nazwę kalkulatora powyżej aby go przetestować
            </p>
        </div>
        
        <div class="test-section error">
            <h2>❌ JEŚLI KALKULATORY NIE DZIAŁAJĄ</h2>
            <p><strong>Problem: JavaScript jest wyłączony w przeglądarce</strong></p>
            <h3>Rozwiązanie - włącz JavaScript:</h3>
            <ul>
                <li><strong>Chrome:</strong> Ustawienia → Prywatność i bezpieczeństwo → Ustawienia witryn → JavaScript → Zezwalaj</li>
                <li><strong>Firefox:</strong> W pasku adresu wpisz: about:config → Szukaj: javascript.enabled → Ustaw na: true</li>
                <li><strong>Edge:</strong> Ustawienia → Pliki cookie i uprawnienia witryn → JavaScript → Włącz</li>
            </ul>
        </div>
    </div>

    <script>
        function testFile(filename) {
            var iframe = document.createElement('iframe');
            iframe.src = filename;
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = '2px solid #007bff';
            iframe.style.borderRadius = '5px';
            
            var container = document.getElementById('testFrame');
            container.innerHTML = '<h3 style="text-align: center;">🧪 Testowanie: ' + filename + '</h3><p style="text-align: center;">Ładowanie...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.className = 'test-section warning';
                instructions.innerHTML = 
                    '<h4>📋 Instrukcje testowania ' + filename + ':</h4>' +
                    '<ol>' +
                    '<li>Poczekaj aż kalkulator się załaduje</li>' +
                    '<li>Znajdź przycisk "Oblicz" (może mieć różne nazwy)</li>' +
                    '<li>Kliknij przycisk</li>' +
                    '<li>Powinien pojawić się alert z wynikiem obliczeń</li>' +
                    '<li>Jeśli nie ma alertu, JavaScript jest wyłączony</li>' +
                    '</ol>' +
                    '<p><strong>Oczekiwany alert: "Rata [Bank]: 2,847 zł"</strong></p>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        // Test JavaScript na załadowaniu strony
        window.onload = function() {
            console.log('Strona testowa załadowana pomyślnie');
        };
    </script>
</body>
</html>
