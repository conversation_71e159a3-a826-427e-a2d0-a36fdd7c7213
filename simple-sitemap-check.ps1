# 简单的sitemap验证脚本
Write-Host "=== 验证sitemap.xml ===" -ForegroundColor Green

if (-not (Test-Path "sitemap.xml")) {
    Write-Host "sitemap.xml文件不存在！" -ForegroundColor Red
    exit 1
}

$content = Get-Content "sitemap.xml" -Raw
$pattern = '<loc>https://kalkulator-kredytu-hipotecznego\.pl/([^<]*)</loc>'
$matches = [regex]::Matches($content, $pattern)

Write-Host "找到 $($matches.Count) 个URL" -ForegroundColor Yellow

$existing = 0
$missing = 0
$missingFiles = @()

foreach ($match in $matches) {
    $filename = $match.Groups[1].Value
    
    if ($filename -eq "") {
        $filename = "index.html"
    }
    
    if (Test-Path $filename) {
        Write-Host "✅ $filename" -ForegroundColor Green
        $existing++
    } else {
        Write-Host "❌ $filename" -ForegroundColor Red
        $missing++
        $missingFiles += $filename
    }
}

Write-Host ""
Write-Host "总结:" -ForegroundColor Cyan
Write-Host "存在: $existing" -ForegroundColor Green
Write-Host "缺失: $missing" -ForegroundColor Red

if ($missing -gt 0) {
    Write-Host ""
    Write-Host "缺失的文件:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "- $file" -ForegroundColor Red
    }
}

Write-Host ""
if ($missing -eq 0) {
    Write-Host "🎉 所有URL都有对应的文件！" -ForegroundColor Green
} else {
    Write-Host "⚠️  需要修复 $missing 个缺失的文件" -ForegroundColor Yellow
}
