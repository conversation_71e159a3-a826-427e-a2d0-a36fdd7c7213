<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Exact Copy Test</title>
	<meta name="description" content="Test">
	<style>
		body { font-family: Arial, sans-serif; margin: 20px; }
		.clefthalf { float: left; width: 45%; }
		.crighthalf { float: right; width: 45%; }
		.h2result { color: #333; font-size: 18px; margin: 10px 0; }
		table { border-collapse: collapse; width: 100%; }
		td { padding: 5px; border: 1px solid #ddd; }
		input, select { padding: 5px; margin: 2px; }
		input[type="button"] { background: #007bff; color: white; border: none; padding: 10px 15px; cursor: pointer; }
		#clear { clear: both; }
	</style>
</head><body>

<h1>Exact Copy Test</h1>

<div class="clefthalf">
<form name="calform">
<table align="center">
<tbody><tr><td align="right"><PERSON><PERSON></td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000"></td><td>zł</td></tr>
<tr><td align="right">Wkład własny</td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20"></td>
<td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania</td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie</td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25"></td><td>%</td></tr>
<tr><td colspan="3" align="center">
<input type="button" name="x" value="Oblicz" onclick="calculateMortgage();">
<input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
</td></tr>
</tbody></table>
</form>
</div>

<div class="crighthalf">
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Miesięczna rata: &nbsp; 0 zł</h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
<tr><td>Podatek od nieruchomości</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie domu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie kredytu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Opłaty administracyjne</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr bgcolor="#dddddd"><td><b>Łącznie</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<div style="margin-top: 20px; padding: 15px; background: #f0f0f0; border: 1px solid #ccc;">
<h3>Debug Information:</h3>
<div id="debug">Click "Oblicz" to see debug information</div>
</div>

<script>
function calculateMortgage() {
    var debug = document.getElementById('debug');
    debug.innerHTML = "🔄 Function called at " + new Date().toLocaleTimeString() + "<br>";
    
    try {
        // Get input values
        var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
        var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
        var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
        var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
        var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
        
        debug.innerHTML += "📊 Input values: House=" + housePrice + ", Down=" + downPayment + downPaymentUnit + ", Term=" + loanTerm + ", Rate=" + interestRate + "<br>";
        
        // Calculate down payment amount
        var downPaymentAmount;
        if (downPaymentUnit === 'p') {
            downPaymentAmount = housePrice * (downPayment / 100);
        } else {
            downPaymentAmount = downPayment;
        }
        var loanAmount = housePrice - downPaymentAmount;
        
        debug.innerHTML += "💰 Loan amount: " + loanAmount + " (House: " + housePrice + " - Down: " + downPaymentAmount + ")<br>";
        
        // Calculate monthly payment
        var monthlyRate = interestRate / 100 / 12;
        var numPayments = loanTerm * 12;
        var monthlyPayment = 0;
        
        if (monthlyRate > 0) {
            monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                            (Math.pow(1 + monthlyRate, numPayments) - 1);
        } else {
            monthlyPayment = loanAmount / numPayments;
        }
        
        debug.innerHTML += "📈 Monthly payment calculated: " + Math.round(monthlyPayment) + " zł<br>";
        
        // Update h2result
        var resultHeader = document.querySelector('.h2result');
        debug.innerHTML += "🔍 Looking for .h2result element...<br>";
        if (resultHeader) {
            debug.innerHTML += "✅ Found .h2result element<br>";
            var newContent = 'Miesięczna rata: &nbsp; ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
            resultHeader.innerHTML = newContent;
            debug.innerHTML += "✅ Updated h2result to: " + newContent + "<br>";
        } else {
            debug.innerHTML += "❌ .h2result element NOT FOUND<br>";
        }
        
        // Update table
        var resultTable = document.querySelector('.crighthalf table table tbody');
        debug.innerHTML += "🔍 Looking for .crighthalf table table tbody...<br>";
        if (resultTable) {
            debug.innerHTML += "✅ Found result table<br>";
            var rows = resultTable.querySelectorAll('tr');
            debug.innerHTML += "📋 Found " + rows.length + " rows in table<br>";
            
            if (rows.length > 1 && rows[1].cells.length >= 3) {
                rows[1].cells[1].innerHTML = '<b>' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł</b>';
                rows[1].cells[2].innerHTML = '<b>' + Math.round(monthlyPayment * numPayments).toLocaleString('pl-PL') + ' zł</b>';
                debug.innerHTML += "✅ Updated table row 2, cells 2 and 3<br>";
            } else {
                debug.innerHTML += "❌ Table structure problem: not enough rows or cells<br>";
            }
        } else {
            debug.innerHTML += "❌ Result table NOT FOUND<br>";
        }
        
        debug.innerHTML += "🎉 Function completed successfully!<br>";
        
    } catch (error) {
        debug.innerHTML += "❌ ERROR: " + error.message + "<br>";
        debug.innerHTML += "Stack: " + error.stack + "<br>";
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.25';
    
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 'Miesięczna rata: &nbsp; 0 zł';
    }
    
    document.getElementById('debug').innerHTML = 'Form cleared. Click "Oblicz" to calculate.';
}

// Test on page load
window.onload = function() {
    document.getElementById('debug').innerHTML = '🟢 Page loaded successfully. JavaScript is working. Click "Oblicz" to test calculator.';
};
</script>

</body></html>
