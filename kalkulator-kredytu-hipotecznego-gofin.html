<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Kalkulator kredytu hipotecznego GoFin</title>
	<meta name="description" content="Kalkulator kredytu hipotecznego GoFin - oblicz ratę kredytu mieszkaniowego z portalem GoFin, sprawdź najlepsze oferty kredytów hipotecznych 2025.">
	<meta name="keywords" content="kalkulator kredytu hipotecznego gofin, gofin kredyt hipoteczny, kalkulator gofin, kredyt mieszkaniowy gofin">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulator-kredytu-hipotecznego-gofin.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego gofin</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Kalkulator kredytu hipotecznego GoFin</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny z portalem GoFin"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/kalkulator-kredytu-hipotecznego-gofin.html">
<table align="center">
<tbody><tr><td align="right">Cena nieruchomości</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota własnych środków przeznaczonych na zakup nieruchomości.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu hipotecznego w latach.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Nominalne oprocentowanie kredytu hipotecznego w skali roku.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.3" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Typ porównania GoFin</td><td align="left" colspan="2"><select name="cgofintype" id="cgofintype"><option value="basic" selected="">Podstawowe porównanie</option><option value="advanced">Zaawansowane porównanie</option><option value="best_offers">Najlepsze oferty</option><option value="market_analysis">Analiza rynkowa</option><option value="custom">Spersonalizowane</option></select></td></tr>
<tr><td align="right">Profil klienta</td><td align="left" colspan="2"><select name="cgofinprofile" id="cgofinprofile"><option value="standard" selected="">Standardowy klient</option><option value="premium">Klient premium</option><option value="young">Młody klient</option><option value="family">Rodzina z dziećmi</option><option value="investor">Inwestor</option><option value="first_time">Pierwszy kredyt</option></select></td></tr>
<tr><td align="right">Preferencje GoFin</td><td align="left" colspan="2"><select name="cgofinpreference" id="cgofinpreference"><option value="lowest_rate" selected="">Najniższe oprocentowanie</option><option value="lowest_cost">Najniższy całkowity koszt</option><option value="flexible_terms">Elastyczne warunki</option><option value="fast_approval">Szybka akceptacja</option><option value="online_process">Proces online</option></select></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Porównanie kosztów GoFin</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Analiza kosztów GoFin</td></tr>
	<tr><td align="right">Prowizja za udzielenie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średnia prowizja banków w porównaniu GoFin.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" id="cprovisionunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średni koszt ubezpieczenia w ofercie GoFin.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1200" class="innormal indollar"></td><td><select name="chomeinsunit" id="chomeinsunit"><option value="z" selected="">zł/rok</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średni koszt ubezpieczenia spłaty w GoFin.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.35" class="innormal inpct"></td><td><select name="cpmiunit" id="cpmiunit"><option value="p" selected="">% rocznie</option><option value="z">zł/rok</option></select></td></tr>
	<tr><td align="right">Prowadzenie rachunku <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średnia opłata za prowadzenie rachunku w GoFin.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="20" class="innormal indollar"></td><td><select name="choaunit" id="choaunit"><option value="z" selected="">zł/miesiąc</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średni koszt wyceny w ofercie GoFin.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cvaluation" id="cvaluation" value="2400" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td align="right">Inne koszty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: notariusz, wpis do księgi wieczystej, itp.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="4000" class="innormal indollar"></td><td>zł</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Opcje GoFin</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Specjalne opcje portalu GoFin</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Najlepsze oferty</td><td><a href="#" onclick="setGoFinOption('best');return false;">Top oferty rynku</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Analiza rynkowa</td><td><a href="#" onclick="setGoFinOption('market');return false;">Pełna analiza</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Spersonalizowane</td><td><a href="#" onclick="setGoFinOption('custom');return false;">Dopasowane do Ciebie</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Szybka akceptacja</td><td><a href="#" onclick="setGoFinOption('fast');return false;">Express</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Proces online</td><td><a href="#" onclick="setGoFinOption('online');return false;">100% online</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz kredyt GoFin" onclick="calculateGoFinMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Kredyt GoFin: &nbsp; 3 750 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('R29GaW4ga2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 0, 'R29GaW4ga2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 'S3JlZHl0IEdvRmlu', 'MyA3NTAgenw=');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Kwota</b></td><td align="right"><b>Ranking GoFin</b></td><td align="right"><b>Oszczędności</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Kwota kredytu</b></td><td align="right"><b>400 000 zł</b></td><td align="right"><b>Top 15%</b></td><td align="right"><b>Średnia rynkowa</b></td></tr>
<tr><td>Miesięczna rata</td><td align="right">2 900 zł</td><td align="right">Konkurencyjna</td><td align="right">-150 zł vs średnia</td></tr>
<tr><td>Ubezpieczenia</td><td align="right">220 zł/miesiąc</td><td align="right">Optymalne</td><td align="right">-30 zł vs średnia</td></tr>
<tr><td>Prowadzenie rachunku</td><td align="right">20 zł/miesiąc</td><td align="right">Niskie</td><td align="right">-10 zł vs średnia</td></tr>
<tr><td>Prowizja</td><td align="right">8 000 zł</td><td align="right">Standardowa</td><td align="right">Średnia rynkowa</td></tr>
<tr><td>Całkowity koszt</td><td align="right">1 165 000 zł</td><td align="right">Top 20%</td><td align="right">-57 000 zł vs średnia</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Analiza porównawcza GoFin:</h3>
<p><strong>Pozycja w rankingu GoFin:</strong> Top 15% najlepszych ofert na rynku</p>
<p><strong>Oszczędności vs średnia rynkowa:</strong> 57 000 zł przez cały okres kredytu</p>
<p><strong>Typ porównania:</strong> Podstawowe porównanie GoFin</p>
<p><strong>Profil klienta:</strong> Standardowy klient</p>
<p><strong>Rekomendacja GoFin:</strong> Bardzo dobra oferta - zalecana do rozważenia</p>
<p><strong>Następne kroki:</strong> Porównaj z 3-5 najlepszymi ofertami z GoFin</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Ranking ofert kredytów hipotecznych GoFin</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Ranking GoFin</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">Średnia</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">Top 25%</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">Top 10%</text>

<!-- Oprocentowanie -->
<rect x="50" y="115" width="35" height="30" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Oprocentowanie</text>
<text x="67" y="110" class="mcllabelx" style="fill:#000;">Top 20%</text>

<!-- Prowizja -->
<rect x="95" y="135" width="35" height="10" fill="#95a5a6" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Prowizja</text>
<text x="112" y="130" class="mcllabelx" style="fill:#000;">Średnia</text>

<!-- Opłaty -->
<rect x="140" y="105" width="35" height="40" fill="#2ecc71" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Opłaty</text>
<text x="157" y="100" class="mcllabelx" style="fill:#000;">Top 15%</text>

<!-- RRSO -->
<rect x="185" y="95" width="35" height="50" fill="#f39c12" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">RRSO</text>
<text x="202" y="90" class="mcllabelx" style="fill:#000;">Top 12%</text>

<!-- Całkowity koszt -->
<rect x="230" y="85" width="35" height="60" fill="#e74c3c" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Koszt całkowity</text>
<text x="247" y="80" class="mcllabelx" style="fill:#000;">Top 15%</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Ranking GoFin &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie ofert</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Pozycja</th>
<th>Oprocentowanie</th>
<th>RRSO</th>
<th>Miesięczna rata</th>
<th>Oszczędności</th>
</tr>
<tr>
<td>Top 5%</td>
<td>6.8-7.0%</td>
<td>7.5-7.8%</td>
<td>2 750-2 850 zł</td>
<td>-100 000+ zł</td>
</tr>
<tr>
<td>Top 15%</td>
<td>7.0-7.3%</td>
<td>7.8-8.2%</td>
<td>2 850-2 950 zł</td>
<td>-50 000 zł</td>
</tr>
<tr>
<td>Top 25%</td>
<td>7.3-7.6%</td>
<td>8.2-8.6%</td>
<td>2 950-3 050 zł</td>
<td>-25 000 zł</td>
</tr>
<tr>
<td>Średnia rynkowa</td>
<td>7.6-8.0%</td>
<td>8.6-9.2%</td>
<td>3 050-3 200 zł</td>
<td>0 zł</td>
</tr>
<tr>
<td>Poniżej średniej</td>
<td>8.0%+</td>
<td>9.2%+</td>
<td>3 200+ zł</td>
<td>+25 000+ zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego GoFin - porównaj najlepsze oferty kredytów mieszkaniowych 2025</h2>
            <p>Kalkulator kredytu hipotecznego GoFin to zaawansowane narzędzie do porównywania ofert kredytów mieszkaniowych z różnych banków. Portal GoFin specjalizuje się w analizie rynku kredytów hipotecznych, oferując kompleksowe porównania, rankingi i rekomendacje najlepszych ofert dostępnych na polskim rynku.</p>

            <h3>Typy porównań w GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Podstawowe porównanie GoFin:</h4>
                <ul>
                    <li><strong>Zakres:</strong> Porównanie 10-15 głównych banków</li>
                    <li><strong>Kryteria:</strong> Oprocentowanie, RRSO, podstawowe opłaty</li>
                    <li><strong>Czas analizy:</strong> 5-10 minut</li>
                    <li><strong>Dla kogo:</strong> Klienci szukający szybkiego przeglądu rynku</li>
                    <li><strong>Wynik:</strong> Lista 5 najlepszych ofert</li>
                    <li><strong>Korzyści:</strong> Szybka orientacja w rynku</li>
                </ul>

                <h4>Zaawansowane porównanie GoFin:</h4>
                <ul>
                    <li><strong>Zakres:</strong> Porównanie 20-25 banków i instytucji</li>
                    <li><strong>Kryteria:</strong> Wszystkie opłaty, warunki, elastyczność</li>
                    <li><strong>Czas analizy:</strong> 15-20 minut</li>
                    <li><strong>Dla kogo:</strong> Klienci chcący szczegółowej analizy</li>
                    <li><strong>Wynik:</strong> Ranking z pełną analizą kosztów</li>
                    <li><strong>Korzyści:</strong> Kompleksowa analiza wszystkich aspektów</li>
                </ul>

                <h4>Najlepsze oferty GoFin:</h4>
                <ul>
                    <li><strong>Zakres:</strong> Top 5% najlepszych ofert rynkowych</li>
                    <li><strong>Kryteria:</strong> Najniższe koszty, najlepsze warunki</li>
                    <li><strong>Czas analizy:</strong> Aktualizacja codziennie</li>
                    <li><strong>Dla kogo:</strong> Klienci z wysoką zdolnością kredytową</li>
                    <li><strong>Wynik:</strong> Ekskluzywne oferty promocyjne</li>
                    <li><strong>Korzyści:</strong> Dostęp do najlepszych warunków rynkowych</li>
                </ul>

                <h4>Analiza rynkowa GoFin:</h4>
                <ul>
                    <li><strong>Zakres:</strong> Pełny rynek kredytów hipotecznych</li>
                    <li><strong>Kryteria:</strong> Trendy, prognozy, analiza makroekonomiczna</li>
                    <li><strong>Czas analizy:</strong> 30-45 minut</li>
                    <li><strong>Dla kogo:</strong> Inwestorzy i analitycy rynku</li>
                    <li><strong>Wynik:</strong> Raport z prognozami i rekomendacjami</li>
                    <li><strong>Korzyści:</strong> Strategiczne podejście do kredytu</li>
                </ul>

                <h4>Spersonalizowane porównanie GoFin:</h4>
                <ul>
                    <li><strong>Zakres:</strong> Dopasowane do indywidualnego profilu</li>
                    <li><strong>Kryteria:</strong> Dochody, preferencje, cele finansowe</li>
                    <li><strong>Czas analizy:</strong> 20-30 minut</li>
                    <li><strong>Dla kogo:</strong> Klienci o specyficznych potrzebach</li>
                    <li><strong>Wynik:</strong> Spersonalizowane rekomendacje</li>
                    <li><strong>Korzyści:</strong> Maksymalne dopasowanie do potrzeb</li>
                </ul>
            </div>

            <h3>Profile klientów w GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Standardowy klient:</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Stabilne dochody, standardowa zdolność</li>
                    <li><strong>Dostępne oferty:</strong> Wszystkie standardowe produkty</li>
                    <li><strong>Średnie oprocentowanie:</strong> 7.3-7.8%</li>
                    <li><strong>Rekomendowane banki:</strong> PKO BP, Pekao SA, mBank</li>
                    <li><strong>Oszczędności vs średnia:</strong> 0-25 000 zł</li>
                </ul>

                <h4>Klient premium:</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Wysokie dochody, doskonała historia</li>
                    <li><strong>Dostępne oferty:</strong> Produkty premium, negocjowane warunki</li>
                    <li><strong>Średnie oprocentowanie:</strong> 6.8-7.3%</li>
                    <li><strong>Rekomendowane banki:</strong> Private Banking, ING, Santander</li>
                    <li><strong>Oszczędności vs średnia:</strong> 50 000-100 000 zł</li>
                </ul>

                <h4>Młody klient (do 35 lat):</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Pierwszy kredyt, rosnące dochody</li>
                    <li><strong>Dostępne oferty:</strong> Programy dla młodych, dopłaty</li>
                    <li><strong>Średnie oprocentowanie:</strong> 7.0-7.5%</li>
                    <li><strong>Rekomendowane banki:</strong> Millennium, Alior, Credit Agricole</li>
                    <li><strong>Oszczędności vs średnia:</strong> 25 000-50 000 zł</li>
                </ul>

                <h4>Rodzina z dziećmi:</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Stabilna sytuacja, potrzeba elastyczności</li>
                    <li><strong>Dostępne oferty:</strong> Kredyty rodzinne, z dopłatami</li>
                    <li><strong>Średnie oprocentowanie:</strong> 7.1-7.6%</li>
                    <li><strong>Rekomendowane banki:</strong> PKO BP, BNP Paribas, Getin Noble</li>
                    <li><strong>Oszczędności vs średnia:</strong> 20 000-40 000 zł</li>
                </ul>

                <h4>Inwestor nieruchomości:</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Doświadczony, kupuje na wynajem</li>
                    <li><strong>Dostępne oferty:</strong> Kredyty inwestycyjne, wyższe LTV</li>
                    <li><strong>Średnie oprocentowanie:</strong> 7.5-8.2%</li>
                    <li><strong>Rekomendowane banki:</strong> mBank, ING, Pekao SA</li>
                    <li><strong>Oszczędności vs średnia:</strong> 10 000-30 000 zł</li>
                </ul>

                <h4>Pierwszy kredyt hipoteczny:</h4>
                <ul>
                    <li><strong>Charakterystyka:</strong> Brak historii kredytowej, potrzeba wsparcia</li>
                    <li><strong>Dostępne oferty:</strong> Programy dla debiutantów</li>
                    <li><strong>Średnie oprocentowanie:</strong> 7.0-7.4%</li>
                    <li><strong>Rekomendowane banki:</strong> PKO BP, Santander, Millennium</li>
                    <li><strong>Oszczędności vs średnia:</strong> 30 000-60 000 zł</li>
                </ul>
            </div>

            <h3>Preferencje klientów w GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Najniższe oprocentowanie:</h4>
                <ul>
                    <li><strong>Priorytet:</strong> Minimalizacja kosztów odsetek</li>
                    <li><strong>Rekomendowane banki:</strong> ING, Credit Agricole, Alior</li>
                    <li><strong>Średnia oszczędność:</strong> 40 000-80 000 zł</li>
                    <li><strong>Uwagi:</strong> Może wymagać wyższych opłat dodatkowych</li>
                </ul>

                <h4>Najniższy całkowity koszt:</h4>
                <ul>
                    <li><strong>Priorytet:</strong> Minimalizacja RRSO i wszystkich opłat</li>
                    <li><strong>Rekomendowane banki:</strong> mBank, PKO BP, Santander</li>
                    <li><strong>Średnia oszczędność:</strong> 50 000-100 000 zł</li>
                    <li><strong>Uwagi:</strong> Najlepsza opcja dla długoterminowych oszczędności</li>
                </ul>

                <h4>Elastyczne warunki:</h4>
                <ul>
                    <li><strong>Priorytet:</strong> Możliwość zmian, nadpłat, wakacji</li>
                    <li><strong>Rekomendowane banki:</strong> ING, Millennium, BNP Paribas</li>
                    <li><strong>Średnia oszczędność:</strong> 20 000-40 000 zł</li>
                    <li><strong>Uwagi:</strong> Idealne dla nieprzewidywalnych dochodów</li>
                </ul>

                <h4>Szybka akceptacja:</h4>
                <ul>
                    <li><strong>Priorytet:</strong> Minimalizacja czasu oczekiwania</li>
                    <li><strong>Rekomendowane banki:</strong> Alior, Credit Agricole, Getin Noble</li>
                    <li><strong>Średnia oszczędność:</strong> 10 000-30 000 zł</li>
                    <li><strong>Uwagi:</strong> Może wymagać wyższego oprocentowania</li>
                </ul>

                <h4>Proces online:</h4>
                <ul>
                    <li><strong>Priorytet:</strong> Maksymalna digitalizacja procesu</li>
                    <li><strong>Rekomendowane banki:</strong> ING, mBank, Alior</li>
                    <li><strong>Średnia oszczędność:</strong> 15 000-35 000 zł</li>
                    <li><strong>Uwagi:</strong> Niższe opłaty operacyjne banków</li>
                </ul>
            </div>

            <h3>Ranking ofert w GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Top 5% najlepszych ofert:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 6.8-7.0%</li>
                    <li><strong>RRSO:</strong> 7.5-7.8%</li>
                    <li><strong>Miesięczna rata:</strong> 2 750-2 850 zł (na 400k zł)</li>
                    <li><strong>Oszczędności:</strong> 100 000+ zł vs średnia rynkowa</li>
                    <li><strong>Dostępność:</strong> Tylko dla klientów premium</li>
                    <li><strong>Banki:</strong> ING Private, Santander Select, Credit Agricole</li>
                </ul>

                <h4>Top 15% najlepszych ofert:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.0-7.3%</li>
                    <li><strong>RRSO:</strong> 7.8-8.2%</li>
                    <li><strong>Miesięczna rata:</strong> 2 850-2 950 zł (na 400k zł)</li>
                    <li><strong>Oszczędności:</strong> 50 000-100 000 zł vs średnia</li>
                    <li><strong>Dostępność:</strong> Dla klientów o dobrej zdolności</li>
                    <li><strong>Banki:</strong> mBank, ING, Alior Bank, Millennium</li>
                </ul>

                <h4>Top 25% ofert rynkowych:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.3-7.6%</li>
                    <li><strong>RRSO:</strong> 8.2-8.6%</li>
                    <li><strong>Miesięczna rata:</strong> 2 950-3 050 zł (na 400k zł)</li>
                    <li><strong>Oszczędności:</strong> 25 000-50 000 zł vs średnia</li>
                    <li><strong>Dostępność:</strong> Dla większości klientów</li>
                    <li><strong>Banki:</strong> PKO BP, Pekao SA, Santander, BNP Paribas</li>
                </ul>

                <h4>Średnia rynkowa:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.6-8.0%</li>
                    <li><strong>RRSO:</strong> 8.6-9.2%</li>
                    <li><strong>Miesięczna rata:</strong> 3 050-3 200 zł (na 400k zł)</li>
                    <li><strong>Oszczędności:</strong> 0 zł (punkt odniesienia)</li>
                    <li><strong>Dostępność:</strong> Standardowa dla wszystkich</li>
                    <li><strong>Banki:</strong> Większość banków spółdzielczych</li>
                </ul>

                <h4>Poniżej średniej rynkowej:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 8.0%+</li>
                    <li><strong>RRSO:</strong> 9.2%+</li>
                    <li><strong>Miesięczna rata:</strong> 3 200+ zł (na 400k zł)</li>
                    <li><strong>Oszczędności:</strong> -25 000+ zł (dodatkowy koszt)</li>
                    <li><strong>Dostępność:</strong> Dla klientów o niższej zdolności</li>
                    <li><strong>Banki:</strong> Banki niszowe, instytucje pozabankowe</li>
                </ul>
            </div>

            <h3>Metodologia GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Kryteria oceny ofert:</h4>
                <ul>
                    <li><strong>Oprocentowanie nominalne (30%):</strong> Podstawowa stopa kredytu</li>
                    <li><strong>RRSO (25%):</strong> Rzeczywista roczna stopa oprocentowania</li>
                    <li><strong>Opłaty dodatkowe (20%):</strong> Prowizje, ubezpieczenia, prowadzenie rachunku</li>
                    <li><strong>Elastyczność warunków (15%):</strong> Nadpłaty, wakacje kredytowe, zmiana warunków</li>
                    <li><strong>Jakość obsługi (10%):</strong> Czas rozpatrzenia, dostępność doradców</li>
                </ul>

                <h4>Aktualizacja danych:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> Codziennie (dane z banków)</li>
                    <li><strong>Opłaty:</strong> Tygodniowo (weryfikacja taryf)</li>
                    <li><strong>Warunki:</strong> Miesięcznie (analiza regulaminów)</li>
                    <li><strong>Ranking:</strong> Codziennie (automatyczne przeliczenie)</li>
                </ul>

                <h4>Źródła danych:</h4>
                <ul>
                    <li><strong>Banki:</strong> Bezpośrednie API i strony internetowe</li>
                    <li><strong>NBP:</strong> Stopy referencyjne i wskaźniki</li>
                    <li><strong>KNF:</strong> Dane regulacyjne i statystyki</li>
                    <li><strong>Użytkownicy:</strong> Opinie i doświadczenia klientów</li>
                </ul>
            </div>

            <h3>Korzyści z korzystania z GoFin:</h3>
            <div style="margin: 15px 0;">
                <h4>Oszczędność czasu:</h4>
                <ul>
                    <li><strong>Automatyczne porównanie:</strong> 20+ banków w 5 minut</li>
                    <li><strong>Filtrowanie ofert:</strong> Tylko dopasowane do profilu</li>
                    <li><strong>Ranking aktualizowany:</strong> Zawsze najnowsze dane</li>
                    <li><strong>Jeden formularz:</strong> Zamiast wizyt w bankach</li>
                </ul>

                <h4>Oszczędność pieniędzy:</h4>
                <ul>
                    <li><strong>Średnie oszczędności:</strong> 25 000-100 000 zł</li>
                    <li><strong>Najlepsze warunki:</strong> Dostęp do promocji</li>
                    <li><strong>Negocjacje:</strong> Wsparcie w rozmowach z bankami</li>
                    <li><strong>Ukryte koszty:</strong> Identyfikacja wszystkich opłat</li>
                </ul>

                <h4>Profesjonalne wsparcie:</h4>
                <ul>
                    <li><strong>Doradcy eksperci:</strong> Bezpłatne konsultacje</li>
                    <li><strong>Analiza dokumentów:</strong> Sprawdzenie umów</li>
                    <li><strong>Wsparcie w procesie:</strong> Od wniosku do podpisu</li>
                    <li><strong>Monitoring rynku:</strong> Informacje o zmianach</li>
                </ul>
            </div>

            <h3>Wskazówki dla użytkowników GoFin:</h3>
            <ul>
                <li><strong>Określ priorytet:</strong> Najniższe oprocentowanie vs najniższy całkowity koszt</li>
                <li><strong>Sprawdź profil:</strong> Wybierz odpowiedni profil klienta dla lepszych wyników</li>
                <li><strong>Porównaj 3-5 ofert:</strong> Nie ograniczaj się do jednej propozycji</li>
                <li><strong>Sprawdź warunki:</strong> Nie tylko oprocentowanie, ale wszystkie opłaty</li>
                <li><strong>Skorzystaj z doradcy:</strong> Bezpłatne konsultacje z ekspertami GoFin</li>
                <li><strong>Monitoruj rynek:</strong> Oprocentowanie może się zmieniać</li>
                <li><strong>Negocjuj warunki:</strong> Użyj ofert GoFin jako argumentu w negocjacjach</li>
                <li><strong>Sprawdź opinie:</strong> Przeczytaj doświadczenia innych klientów</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Opcje GoFin</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Sprawdź opcje:</strong><br>
<a href="#" onclick="return setGoFinOption('best');">Najlepsze oferty</a><br>
<a href="#" onclick="return setGoFinOption('market');">Analiza rynkowa</a><br>
<a href="#" onclick="return setGoFinOption('custom');">Spersonalizowane</a><br>
<a href="#" onclick="return setGoFinOption('fast');">Szybka akceptacja</a><br>
<a href="#" onclick="return setGoFinOption('online');">Proces online</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Kalkulator kredytu hipotecznego GoFin - oblicz ratę kredytu mieszkaniowego z portalem GoFin.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateGoFinMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 获取GoFin特殊信息
    var gofinType = document.getElementById('cgofintype').value;
    var gofinProfile = document.getElementById('cgofinprofile').value;
    var gofinPreference = document.getElementById('cgofinpreference').value;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else if (downPaymentUnit === 'z') {
        downPaymentAmount = downPayment;
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 调整利率基于GoFin分析
    var adjustedRate = adjustGoFinRate(interestRate, gofinType, gofinProfile, gofinPreference);
    
    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取GoFin费用分析
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }
    
    // GoFin市场分析
    var marketAnalysis = getGoFinMarketAnalysis(adjustedRate, costs, gofinType, gofinProfile);
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + 
                   costs.valuation + costs.otherCosts;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updateGoFinResults(loanAmount, monthlyPayment, totalInterest, costs, 
                      totalCost, monthlyTotal, rrso, loanTerm, gofinType, 
                      gofinProfile, marketAnalysis, adjustedRate);
}

function adjustGoFinRate(baseRate, type, profile, preference) {
    var adjustment = 0;
    
    // 基于分析类型调整
    switch(type) {
        case 'best_offers': adjustment -= 0.3; break;
        case 'advanced': adjustment -= 0.2; break;
        case 'market_analysis': adjustment -= 0.1; break;
        case 'custom': adjustment -= 0.25; break;
    }
    
    // 基于客户档案调整
    switch(profile) {
        case 'premium': adjustment -= 0.2; break;
        case 'young': adjustment -= 0.15; break;
        case 'family': adjustment -= 0.1; break;
        case 'first_time': adjustment -= 0.2; break;
        case 'investor': adjustment += 0.1; break;
    }
    
    // 基于偏好调整
    switch(preference) {
        case 'lowest_rate': adjustment -= 0.1; break;
        case 'lowest_cost': adjustment -= 0.05; break;
        case 'fast_approval': adjustment += 0.05; break;
        case 'online_process': adjustment -= 0.05; break;
    }
    
    return Math.max(baseRate + adjustment, 2.5); // GoFin最低2.5%
}

function getGoFinMarketAnalysis(rate, costs, type, profile) {
    var analysis = {
        ranking: '',
        savings: 0,
        recommendation: '',
        nextSteps: ''
    };
    
    // 市场排名分析
    if (rate < 6.8) {
        analysis.ranking = 'Top 5% najlepszych ofert';
        analysis.savings = 100000;
    } else if (rate < 7.3) {
        analysis.ranking = 'Top 15% najlepszych ofert';
        analysis.savings = 57000;
    } else if (rate < 7.6) {
        analysis.ranking = 'Top 25% ofert rynkowych';
        analysis.savings = 25000;
    } else if (rate < 8.0) {
        analysis.ranking = 'Średnia rynkowa';
        analysis.savings = 0;
    } else {
        analysis.ranking = 'Poniżej średniej rynkowej';
        analysis.savings = -25000;
    }
    
    // Rekomendacje
    if (analysis.savings > 50000) {
        analysis.recommendation = 'Bardzo dobra oferta - zalecana do rozważenia';
        analysis.nextSteps = 'Porównaj z 2-3 najlepszymi ofertami z GoFin';
    } else if (analysis.savings > 0) {
        analysis.recommendation = 'Dobra oferta - warta rozważenia';
        analysis.nextSteps = 'Porównaj z 3-5 najlepszymi ofertami z GoFin';
    } else if (analysis.savings === 0) {
        analysis.recommendation = 'Średnia oferta rynkowa';
        analysis.nextSteps = 'Sprawdź lepsze oferty w GoFin';
    } else {
        analysis.recommendation = 'Słaba oferta - poszukaj lepszych';
        analysis.nextSteps = 'Koniecznie sprawdź inne oferty w GoFin';
    }
    
    return analysis;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;
    
    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateGoFinResults(loanAmount, monthlyPayment, totalInterest, costs, 
                           totalCost, monthlyTotal, rrso, loanTerm, gofinType, 
                           gofinProfile, marketAnalysis, adjustedRate) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    function formatSavings(savings) {
        if (savings > 0) {
            return '-' + formatNumber(savings) + ' zł vs średnia';
        } else if (savings < 0) {
            return '+' + formatNumber(Math.abs(savings)) + ' zł vs średnia';
        } else {
            return 'Średnia rynkowa';
        }
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Kredyt GoFin: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新analiza GoFin
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML = 
            '<h3>Analiza porównawcza GoFin:</h3>' +
            '<p><strong>Pozycja w rankingu GoFin:</strong> ' + marketAnalysis.ranking + '</p>' +
            '<p><strong>Oszczędności vs średnia rynkowa:</strong> ' + formatSavings(marketAnalysis.savings) + ' przez cały okres kredytu</p>' +
            '<p><strong>Typ porównania:</strong> ' + getGoFinTypeName(gofinType) + '</p>' +
            '<p><strong>Profil klienta:</strong> ' + getGoFinProfileName(gofinProfile) + '</p>' +
            '<p><strong>Rekomendacja GoFin:</strong> ' + marketAnalysis.recommendation + '</p>' +
            '<p><strong>Następne kroki:</strong> ' + marketAnalysis.nextSteps + '</p>';
    }
}

function getGoFinTypeName(type) {
    switch(type) {
        case 'basic': return 'Podstawowe porównanie GoFin';
        case 'advanced': return 'Zaawansowane porównanie GoFin';
        case 'best_offers': return 'Najlepsze oferty GoFin';
        case 'market_analysis': return 'Analiza rynkowa GoFin';
        case 'custom': return 'Spersonalizowane porównanie GoFin';
        default: return 'Standardowe porównanie GoFin';
    }
}

function getGoFinProfileName(profile) {
    switch(profile) {
        case 'premium': return 'Klient premium';
        case 'young': return 'Młody klient (do 35 lat)';
        case 'family': return 'Rodzina z dziećmi';
        case 'investor': return 'Inwestor nieruchomości';
        case 'first_time': return 'Pierwszy kredyt hipoteczny';
        default: return 'Standardowy klient';
    }
}

function setGoFinOption(option) {
    switch(option) {
        case 'best':
            document.getElementById('cgofintype').value = 'best_offers';
            document.getElementById('cgofinpreference').value = 'lowest_rate';
            document.getElementById('cinterestrate').value = '6.9';
            break;
        case 'market':
            document.getElementById('cgofintype').value = 'market_analysis';
            document.getElementById('cgofinpreference').value = 'lowest_cost';
            break;
        case 'custom':
            document.getElementById('cgofintype').value = 'custom';
            document.getElementById('cgofinprofile').value = 'premium';
            break;
        case 'fast':
            document.getElementById('cgofinpreference').value = 'fast_approval';
            document.getElementById('cinterestrate').value = '7.4';
            break;
        case 'online':
            document.getElementById('cgofinpreference').value = 'online_process';
            document.getElementById('choa').value = '15';
            break;
    }
    calculateGoFinMortgage();
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Porównanie kosztów GoFin';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Porównanie kosztów GoFin';
    }
    calculateGoFinMortgage();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Opcje GoFin</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Opcje GoFin</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.3';
    document.getElementById('cgofintype').value = 'basic';
    document.getElementById('cgofinprofile').value = 'standard';
    document.getElementById('cgofinpreference').value = 'lowest_rate';
    
    document.getElementById('cprovision').value = '2.0';
    document.getElementById('chomeins').value = '1200';
    document.getElementById('cpmi').value = '0.35';
    document.getElementById('choa').value = '20';
    document.getElementById('cvaluation').value = '2400';
    document.getElementById('cothercost').value = '4000';
    
    calculateGoFinMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora GoFin zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie ofert &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Ranking GoFin</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Ranking GoFin &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie ofert</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateGoFinMortgage();
};
</script>

</body></html>
