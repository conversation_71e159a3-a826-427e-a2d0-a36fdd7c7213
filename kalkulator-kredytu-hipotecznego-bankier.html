<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego Bankier</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego Bankier - oblicz ratę kredytu mieszkaniowego z portalem Bankier.pl, sprawdź najlepsze oferty banków i porównaj warunki kredytów hipotecznych 2025.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego bankier, bankier kredyt hipoteczny, porównanie kredytów bankier, najlepsze oferty kredytowe">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-bankier.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego bankier</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego Bankier</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny z portalem Bankier.pl"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-bankier.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości Bankier <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości według wyceny Bankier. Na podstawie tej kwoty banki obliczą maksymalną kwotę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cbankierpropertyvalue" id="cbankierpropertyvalue" value="650000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny Bankier <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny według standardów Bankier. Minimum 10% wartości nieruchomości. Wyższy wkład = lepsza marża.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cbankierdownpayment" id="cbankierdownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="cbankierdownpaymentunit" onchange="cunitchangebankier('cbankierdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania Bankier <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu według analiz Bankier. Maksymalnie 35 lat. Dłuższy okres = niższa rata.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cbankirloanterm" id="cbankirloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie Bankier <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średnie oprocentowanie kredytu hipotecznego według danych Bankier. WIBOR 3M + średnia marża rynkowa.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cbankierinterestrate" id="cbankierinterestrate" value="7.15" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Bank według Bankier</td>
                                <td align="left" colspan="2">
                                    <select name="cbankierbank" id="cbankierbank" onchange="updateBankierRate();">
                                        <option value="average_bankier" selected="">Średnia rynkowa Bankier</option>
                                        <option value="pko_bankier">PKO BP (według Bankier)</option>
                                        <option value="mbank_bankier">mBank (według Bankier)</option>
                                        <option value="santander_bankier">Santander (według Bankier)</option>
                                        <option value="ing_bankier">ING (według Bankier)</option>
                                        <option value="pekao_bankier">Pekao (według Bankier)</option>
                                        <option value="millennium_bankier">Millennium (według Bankier)</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania Bankier</td>
                                <td align="left" colspan="2">
                                    <select name="cbankierinteresttype" id="cbankierinteresttype" onchange="updateBankierInterestType();">
                                        <option value="variable_bankier" selected="">Zmienne (WIBOR + marża)</option>
                                        <option value="fixed_3y_bankier">Stałe na 3 lata</option>
                                        <option value="fixed_5y_bankier">Stałe na 5 lat</option>
                                        <option value="fixed_10y_bankier">Stałe na 10 lat</option>
                                        <option value="mixed_bankier">Mieszane</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddbankierfees" class="cbcontainer">
                                        <input type="checkbox" name="caddbankierfees" id="caddbankierfees" value="1" checked="" onclick="cshbankierfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cbankierfeesdesc">Opłaty według Bankier</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cbankierfees" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Średnie opłaty rynkowe Bankier</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przygotowawcza <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średnia prowizja za przygotowanie kredytu według danych Bankier. 0-2% kwoty kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankirprovision" id="cbankirprovision" value="1.7" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rachunek kredytowy <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średnia miesięczna opłata za prowadzenie rachunku kredytowego według Bankier. 0-25 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankiraccount" id="cbankiraccount" value="15" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średni koszt ubezpieczenia spłaty kredytu według Bankier. 0.20-0.40% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankirinsurance" id="cbankirinsurance" value="0.30" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Średni koszt wyceny nieruchomości przez rzeczoznawcę według Bankier. 1000-2000 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankirvaluation" id="cbankirvaluation" value="1500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty według analiz Bankier: notariusz, wpis hipoteki, ubezpieczenie nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankirother" id="cbankirother" value="3500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddbankiercomparison" class="cbcontainer">
                                        <input type="checkbox" name="caddbankiercomparison" id="caddbankiercomparison" value="1" checked="" onclick="cshbankiercomparison();">
                                        <span class="cbmark"></span>
                                        <b><span id="cbankiercomparisondesc">Porównanie banków Bankier</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cbankiercomparison" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Porównanie ofert według Bankier</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Najlepsza oferta</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cbankirbestoffer" id="cbankirbestoffer">
                                                            <option value="pko_best" selected="">PKO BP - najniższa rata</option>
                                                            <option value="mbank_best">mBank - najszybsza obsługa</option>
                                                            <option value="ing_best">ING - najlepsze warunki</option>
                                                            <option value="santander_best">Santander - promocje</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ranking Bankier</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cbankirranking" id="cbankirranking">
                                                            <option value="rate_ranking" selected="">Ranking według raty</option>
                                                            <option value="cost_ranking">Ranking według kosztu</option>
                                                            <option value="service_ranking">Ranking według obsługi</option>
                                                            <option value="overall_ranking">Ranking ogólny</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Filtr Bankier</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cbankirfilter" id="cbankirfilter">
                                                            <option value="all_banks" selected="">Wszystkie banki</option>
                                                            <option value="big_banks">Tylko duże banki</option>
                                                            <option value="digital_banks">Banki cyfrowe</option>
                                                            <option value="promo_banks">Z promocjami</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz według Bankier" onclick="calculateBankierMortgage();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                    <input type="button" value="Porównaj banki" onclick="compareBanks();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata Bankier: &nbsp; 3 925 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBCYW5raWVy', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBCYW5raWVy', 'UmF0YSBCYW5raWVy', 'MyA5MjUgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bankier</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata według Bankier</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>3 925 zł</b></td>
                                        <td align="right"><b>1 177 500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu</td>
                                        <td align="right">520 000 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">520 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał + odsetki</td>
                                        <td align="right">-</td>
                                        <td align="right">3 800 zł</td>
                                        <td align="right">1 140 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie</td>
                                        <td align="right">-</td>
                                        <td align="right">130 zł</td>
                                        <td align="right">39 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Rachunek kredytowy</td>
                                        <td align="right">-</td>
                                        <td align="right">15 zł</td>
                                        <td align="right">4 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty jednorazowe</td>
                                        <td align="right">13 840 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">13 840 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza według Bankier:</h3>
                                            <p><strong>RRSO średnie:</strong> 7.95% (średnia rynkowa według Bankier)</p>
                                            <p><strong>Ranking Bankier:</strong> Średnia oferta rynkowa</p>
                                            <p><strong>Rekomendacja:</strong> Porównaj z najlepszymi ofertami</p>
                                            <p><strong>Całkowity koszt:</strong> 1 201 340 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 681 340 zł (131% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie banków według Bankier</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Banki według Bankier</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3600 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">3800 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4000 zł</text>
                    
                    <!-- PKO BP -->
                    <rect x="50" y="125" width="35" height="20" fill="#c0392b" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">PKO BP</text>
                    <text x="67" y="120" class="mcllabelx" style="fill:#000;">3685 zł</text>
                    
                    <!-- mBank -->
                    <rect x="95" y="115" width="35" height="30" fill="#00a651" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">mBank</text>
                    <text x="112" y="110" class="mcllabelx" style="fill:#000;">3785 zł</text>
                    
                    <!-- ING -->
                    <rect x="140" y="105" width="35" height="40" fill="#ff6200" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">ING</text>
                    <text x="157" y="100" class="mcllabelx" style="fill:#000;">3825 zł</text>
                    
                    <!-- Średnia -->
                    <rect x="185" y="95" width="35" height="50" fill="#3498db" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">Średnia</text>
                    <text x="202" y="90" class="mcllabelx" style="fill:#000;">3925 zł</text>
                    
                    <!-- Santander -->
                    <rect x="230" y="85" width="35" height="60" fill="#ec0000" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">Santander</text>
                    <text x="247" y="80" class="mcllabelx" style="fill:#000;">4025 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Ranking banków Bankier &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Bank</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Ranking</th>
                        </tr>
                        <tr>
                            <td>PKO BP</td>
                            <td>7.05%</td>
                            <td>3 685 zł</td>
                            <td>1 166 300 zł</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>mBank</td>
                            <td>7.25%</td>
                            <td>3 785 zł</td>
                            <td>1 196 500 zł</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>ING</td>
                            <td>7.35%</td>
                            <td>3 825 zł</td>
                            <td>1 211 500 zł</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>Średnia rynkowa</td>
                            <td>7.15%</td>
                            <td>3 925 zł</td>
                            <td>1 201 340 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Santander</td>
                            <td>7.45%</td>
                            <td>4 025 zł</td>
                            <td>1 241 500 zł</td>
                            <td>4</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego Bankier - portal porównawczy nr 1 w Polsce 2025</h2>
            <p>Bankier.pl to wiodący portal finansowy w Polsce, oferujący kompleksowe porównania kredytów hipotecznych wszystkich banków. Nasz kalkulator kredytu hipotecznego Bankier wykorzystuje aktualne dane rynkowe, pomoże Ci znaleźć najlepszą ofertę kredytu mieszkaniowego i porównać warunki wszystkich banków w jednym miejscu.</p>

            <h3>Ranking banków według Bankier.pl:</h3>
            <div style="margin: 15px 0;">
                <h4>PKO BP (według Bankier - pozycja #1):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.05% (najniższa rata według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 4.5/5 - najlepszy stosunek jakości do ceny</li>
                    <li><strong>Zalety:</strong> Największy bank w Polsce, stabilność, najniższe oprocentowanie</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2% (konkurencyjna według Bankier)</li>
                    <li><strong>Obsługa:</strong> Szeroka sieć oddziałów, doświadczeni doradcy</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐⭐⭐ Najlepsza oferta rynkowa</li>
                </ul>

                <h4>mBank (według Bankier - pozycja #2):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.25% (bardzo konkurencyjne według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 4.3/5 - lider bankowości cyfrowej</li>
                    <li><strong>Zalety:</strong> Najlepsza aplikacja mobilna, szybka obsługa online</li>
                    <li><strong>Prowizja:</strong> 1.8% (średnia rynkowa według Bankier)</li>
                    <li><strong>Obsługa:</strong> Cyfrowa, nowoczesna, 24/7 online</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐⭐⭐ Najlepszy dla cyfrowych</li>
                </ul>

                <h4>ING (według Bankier - pozycja #3):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.35% (elastyczne warunki według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 4.2/5 - elastyczność i innowacyjność</li>
                    <li><strong>Zalety:</strong> Elastyczne warunki, innowacyjne rozwiązania</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2.5% (zależna od profilu)</li>
                    <li><strong>Obsługa:</strong> Profesjonalna, indywidualne podejście</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐⭐ Dobra dla wymagających</li>
                </ul>

                <h4>Santander (według Bankier - pozycja #4):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.45% (promocje według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 4.0/5 - międzynarodowe doświadczenie</li>
                    <li><strong>Zalety:</strong> Międzynarodowa grupa, atrakcyjne promocje</li>
                    <li><strong>Prowizja:</strong> 1.6% (często promocje według Bankier)</li>
                    <li><strong>Obsługa:</strong> Profesjonalna, międzynarodowe standardy</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐⭐ Dobra z promocjami</li>
                </ul>

                <h4>Pekao (według Bankier - pozycja #5):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.55% (tradycyjny bank według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 3.8/5 - tradycja i stabilność</li>
                    <li><strong>Zalety:</strong> Długa tradycja, stabilność, szeroka oferta</li>
                    <li><strong>Prowizja:</strong> 2% (wyższa według Bankier)</li>
                    <li><strong>Obsługa:</strong> Tradycyjna, konserwatywna</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐ Dla konserwatywnych</li>
                </ul>

                <h4>Millennium (według Bankier - pozycja #6):</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.65% (wyższe według Bankier)</li>
                    <li><strong>Ocena Bankier:</strong> 3.7/5 - specjalistyczne podejście</li>
                    <li><strong>Zalety:</strong> Specjalistyczne doradztwo, indywidualne warunki</li>
                    <li><strong>Prowizja:</strong> 1.8% - 2.2% (średnia według Bankier)</li>
                    <li><strong>Obsługa:</strong> Specjalistyczna, dla wymagających klientów</li>
                    <li><strong>Rekomendacja Bankier:</strong> ⭐⭐⭐ Dla specjalistycznych potrzeb</li>
                </ul>
            </div>

            <h3>Metodologia rankingu Bankier.pl:</h3>
            <div style="margin: 15px 0;">
                <h4>Kryteria oceny według Bankier:</h4>
                <ul>
                    <li><strong>Oprocentowanie (40%):</strong> WIBOR + marża, konkurencyjność</li>
                    <li><strong>Opłaty (25%):</strong> Prowizje, koszty prowadzenia, ukryte opłaty</li>
                    <li><strong>Obsługa (20%):</strong> Szybkość, jakość, dostępność</li>
                    <li><strong>Warunki (10%):</strong> Elastyczność, wymagania, LTV</li>
                    <li><strong>Innowacyjność (5%):</strong> Technologia, rozwiązania cyfrowe</li>
                </ul>

                <h4>Aktualizacja danych Bankier:</h4>
                <ul>
                    <li><strong>Częstotliwość:</strong> Codziennie - najświeższe dane rynkowe</li>
                    <li><strong>Źródła:</strong> Bezpośrednio od banków, oficjalne cenniki</li>
                    <li><strong>Weryfikacja:</strong> Zespół ekspertów Bankier.pl</li>
                    <li><strong>Transparentność:</strong> Pełna metodologia dostępna publicznie</li>
                </ul>
            </div>

            <h3>Średnie rynkowe według Bankier.pl:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Parametr</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Średnia rynkowa</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Najlepsza oferta</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Najgorsza oferta</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.15%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6.85% (PKO BP Premium)</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.95% (banki regionalne)</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.7%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0% (promocje)</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.5%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rachunek kredytowy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">15 zł/miesiąc</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0 zł (Premium)</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 zł/miesiąc</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wycena nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">RRSO</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.95%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8.75%</td>
                    </tr>
                </table>
            </div>

            <h3>Narzędzia porównawcze Bankier.pl:</h3>
            <ul>
                <li><strong>Kalkulator porównawczy:</strong> Wszystkie banki w jednym miejscu</li>
                <li><strong>Ranking miesięczny:</strong> Aktualizowany co miesiąc ranking ofert</li>
                <li><strong>Alerty cenowe:</strong> Powiadomienia o zmianach oprocentowania</li>
                <li><strong>Symulator scenariuszy:</strong> Porównanie różnych wariantów</li>
                <li><strong>Kalkulator RRSO:</strong> Rzeczywisty koszt kredytu</li>
                <li><strong>Analiza trendów:</strong> Prognozy zmian na rynku</li>
                <li><strong>Porady ekspertów:</strong> Rekomendacje zespołu Bankier.pl</li>
            </ul>

            <h3>Trendy rynkowe według Bankier.pl 2025:</h3>
            <ul>
                <li><strong>Oprocentowanie:</strong> Stabilizacja na poziomie 7.0-7.5%</li>
                <li><strong>Konkurencja:</strong> Wzrost konkurencji między bankami</li>
                <li><strong>Cyfryzacja:</strong> Przyspieszenie procesów online</li>
                <li><strong>Promocje:</strong> Więcej atrakcyjnych promocji dla nowych klientów</li>
                <li><strong>Wymagania:</strong> Łagodzenie kryteriów zdolności kredytowej</li>
                <li><strong>Innowacje:</strong> Nowe produkty i usługi dodatkowe</li>
                <li><strong>Regulacje:</strong> Nowe przepisy wspierające kredytobiorców</li>
            </ul>

            <h3>Porady ekspertów Bankier.pl:</h3>
            <ul>
                <li>Zawsze porównuj oferty minimum 3-4 banków</li>
                <li>Sprawdzaj RRSO, nie tylko oprocentowanie nominalne</li>
                <li>Negocjuj warunki - banki często idą na ustępstwa</li>
                <li>Wykorzystuj promocje i programy lojalnościowe</li>
                <li>Rozważ wyższy wkład własny dla lepszych warunków</li>
                <li>Sprawdzaj ukryte opłaty i koszty dodatkowe</li>
                <li>Korzystaj z narzędzi porównawczych Bankier.pl</li>
                <li>Śledź trendy rynkowe i moment na złożenie wniosku</li>
            </ul>

            <h3>Jak korzystać z kalkulatora Bankier:</h3>
            <ol>
                <li><strong>Wprowadź dane</strong> - wartość nieruchomości, wkład własny, okres</li>
                <li><strong>Wybierz bank</strong> - lub zostaw "średnia rynkowa" dla porównania</li>
                <li><strong>Ustaw parametry</strong> - typ oprocentowania, opłaty</li>
                <li><strong>Porównaj wyniki</strong> - sprawdź ranking i rekomendacje</li>
                <li><strong>Analizuj szczegóły</strong> - RRSO, całkowity koszt, oszczędności</li>
                <li><strong>Zapisz wyniki</strong> - do późniejszego porównania</li>
                <li><strong>Skontaktuj się z bankiem</strong> - złóż wniosek o najlepszą ofertę</li>
            </ol>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-kredytu-hipotecznego-pko-bp.html">PKO BP kalkulator kredytu</a>
                <a href="/mbank-kalkulator-kredytu-hipotecznego.html">mBank kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Ranking banków Bankier</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz bank:</strong><br>
                        <a href="#" onclick="return setBankierBank('pko_bankier');">PKO BP (najlepszy)</a><br>
                        <a href="#" onclick="return setBankierBank('mbank_bankier');">mBank (cyfrowy)</a><br>
                        <a href="#" onclick="return setBankierBank('ing_bankier');">ING (elastyczny)</a><br>
                        <a href="#" onclick="return setBankierBank('santander_bankier');">Santander (promocje)</a><br>
                        <a href="#" onclick="return setBankierBank('average_bankier');">Średnia rynkowa</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego Bankier - oblicz ratę kredytu mieszkaniowego z portalem Bankier.pl, porównaj najlepsze oferty banków.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateBankierRate() {
    var bank = document.getElementById('cbankierbank').value;
    var rateField = document.getElementById('cbankierinterestrate');
    
    switch(bank) {
        case 'pko_bankier':
            rateField.value = '7.05';
            break;
        case 'mbank_bankier':
            rateField.value = '7.25';
            break;
        case 'santander_bankier':
            rateField.value = '7.45';
            break;
        case 'ing_bankier':
            rateField.value = '7.35';
            break;
        case 'pekao_bankier':
            rateField.value = '7.55';
            break;
        case 'millennium_bankier':
            rateField.value = '7.65';
            break;
        case 'average_bankier':
            rateField.value = '7.15';
            break;
    }
}

function updateBankierInterestType() {
    var type = document.getElementById('cbankierinteresttype').value;
    var rateField = document.getElementById('cbankierinterestrate');
    var currentRate = parseFloat(rateField.value);
    
    switch(type) {
        case 'variable_bankier':
            // Keep current rate
            break;
        case 'fixed_3y_bankier':
            rateField.value = (currentRate + 0.30).toFixed(2);
            break;
        case 'fixed_5y_bankier':
            rateField.value = (currentRate + 0.60).toFixed(2);
            break;
        case 'fixed_10y_bankier':
            rateField.value = (currentRate + 1.00).toFixed(2);
            break;
        case 'mixed_bankier':
            rateField.value = (currentRate + 0.20).toFixed(2);
            break;
    }
}

function cshbankierfees() {
    var checkbox = document.getElementById('caddbankierfees');
    var feesDiv = document.getElementById('cbankierfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshbankiercomparison() {
    var checkbox = document.getElementById('caddbankiercomparison');
    var comparisonDiv = document.getElementById('cbankiercomparison');
    if (checkbox.checked) {
        comparisonDiv.style.display = 'block';
    } else {
        comparisonDiv.style.display = 'none';
    }
}

function setBankierBank(bank) {
    document.getElementById('cbankierbank').value = bank;
    updateBankierRate();
    return false;
}

function compareBanks() {
    alert('Porównanie banków według danych Bankier.pl zostanie uruchomione!');
    return false;
}

function cunitchangebankier(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Ranking banków Bankier</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Ranking banków Bankier &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania</a>';
    }
    return false;
}
</script>

<script>
function calculateBankierMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 获取Bankier特殊信息
    var bankierAnalysis = document.getElementById('cbankieranalysis').value;
    var bankierComparison = document.getElementById('cbankiercomparison').value;
    var bankierRating = document.getElementById('cbankierrating').value;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // Bankier分析调整
    var bankierAdjustment = getBankierAnalysisAdjustment(bankierAnalysis, bankierComparison, bankierRating);
    var adjustedRate = Math.max(interestRate + bankierAdjustment.rateAdjustment, 2.0);

    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }

    // 应用Bankier调整
    costs.provision *= bankierAdjustment.costMultiplier;
    costs.accountFee *= bankierAdjustment.costMultiplier;

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees +
                   costs.valuation + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // Bankier市场分析
    var marketAnalysis = getBankierMarketAnalysis(adjustedRate, rrso, bankierRating);

    // 更新显示
    updateBankierResults(loanAmount, monthlyPayment, totalInterest, costs,
                        totalCost, monthlyTotal, rrso, loanTerm, bankierAnalysis,
                        marketAnalysis, adjustedRate);
}

function getBankierAnalysisAdjustment(analysis, comparison, rating) {
    var adjustment = {
        rateAdjustment: 0,
        costMultiplier: 1.0,
        riskLevel: 'średnie'
    };

    // 基于Bankier分析调整
    switch(analysis) {
        case 'conservative':
            adjustment.rateAdjustment = 0.2;
            adjustment.costMultiplier = 1.1;
            adjustment.riskLevel = 'niskie';
            break;
        case 'optimistic':
            adjustment.rateAdjustment = -0.1;
            adjustment.costMultiplier = 0.95;
            adjustment.riskLevel = 'wysokie';
            break;
        case 'realistic':
            adjustment.rateAdjustment = 0.05;
            adjustment.costMultiplier = 1.0;
            adjustment.riskLevel = 'średnie';
            break;
    }

    // 基于比较调整
    switch(comparison) {
        case 'best_market':
            adjustment.rateAdjustment -= 0.15;
            break;
        case 'average_market':
            adjustment.rateAdjustment += 0.0;
            break;
        case 'worst_market':
            adjustment.rateAdjustment += 0.25;
            break;
    }

    // 基于评级调整
    switch(rating) {
        case 'aaa':
            adjustment.rateAdjustment -= 0.1;
            adjustment.costMultiplier *= 0.9;
            break;
        case 'aa':
            adjustment.rateAdjustment -= 0.05;
            adjustment.costMultiplier *= 0.95;
            break;
        case 'a':
            adjustment.rateAdjustment += 0.0;
            break;
        case 'bbb':
            adjustment.rateAdjustment += 0.1;
            adjustment.costMultiplier *= 1.05;
            break;
    }

    return adjustment;
}

function getBankierMarketAnalysis(rate, rrso, rating) {
    var analysis = {
        marketPosition: '',
        recommendation: '',
        riskAssessment: '',
        futureOutlook: ''
    };

    // Pozycja rynkowa
    if (rate < 6.0) {
        analysis.marketPosition = 'Bardzo konkurencyjne oprocentowanie';
    } else if (rate < 7.0) {
        analysis.marketPosition = 'Konkurencyjne oprocentowanie';
    } else if (rate < 8.0) {
        analysis.marketPosition = 'Średnie oprocentowanie rynkowe';
    } else {
        analysis.marketPosition = 'Wysokie oprocentowanie';
    }

    // Rekomendacja
    if (rrso < 7.0) {
        analysis.recommendation = 'Zdecydowanie zalecane przez Bankier.pl';
    } else if (rrso < 8.0) {
        analysis.recommendation = 'Zalecane przez ekspertów Bankier.pl';
    } else if (rrso < 9.0) {
        analysis.recommendation = 'Umiarkowanie zalecane';
    } else {
        analysis.recommendation = 'Rozważ inne opcje';
    }

    // Ocena ryzyka
    switch(rating) {
        case 'aaa':
            analysis.riskAssessment = 'Bardzo niskie ryzyko';
            break;
        case 'aa':
            analysis.riskAssessment = 'Niskie ryzyko';
            break;
        case 'a':
            analysis.riskAssessment = 'Średnie ryzyko';
            break;
        case 'bbb':
            analysis.riskAssessment = 'Podwyższone ryzyko';
            break;
        default:
            analysis.riskAssessment = 'Średnie ryzyko';
    }

    // Prognoza
    analysis.futureOutlook = 'Stabilne warunki kredytowe w perspektywie 12 miesięcy';

    return analysis;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateBankierResults(loanAmount, monthlyPayment, totalInterest, costs,
                             totalCost, monthlyTotal, rrso, loanTerm, bankierAnalysis,
                             marketAnalysis, adjustedRate) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Analiza Bankier: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新analiza Bankier.pl
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Analiza ekspertów Bankier.pl:</h3>' +
            '<p><strong>RRSO według Bankier:</strong> ' + rrso.toFixed(2) + '% (analiza rynkowa)</p>' +
            '<p><strong>Pozycja rynkowa:</strong> ' + marketAnalysis.marketPosition + '</p>' +
            '<p><strong>Rekomendacja Bankier.pl:</strong> ' + marketAnalysis.recommendation + '</p>' +
            '<p><strong>Ocena ryzyka:</strong> ' + marketAnalysis.riskAssessment + '</p>' +
            '<p><strong>Prognoza rynkowa:</strong> ' + marketAnalysis.futureOutlook + '</p>' +
            '<p><strong>Typ analizy:</strong> ' + getBankierAnalysisName(bankierAnalysis) + '</p>' +
            '<p><strong>Całkowity koszt:</strong> ' + formatNumber(totalCost) + ' zł (z korektami rynkowymi)</p>';
    }
}

function getBankierAnalysisName(analysis) {
    switch(analysis) {
        case 'conservative': return 'Analiza konserwatywna (wyższe koszty)';
        case 'optimistic': return 'Analiza optymistyczna (niższe koszty)';
        case 'realistic': return 'Analiza realistyczna (warunki rynkowe)';
        default: return 'Analiza standardowa Bankier.pl';
    }
}

function compareBanks() {
    alert('Porównanie banków według metodologii Bankier.pl zostanie uruchomione!');
    return false;
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '500000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.1';
    if (document.getElementById('cbankieranalysis')) document.getElementById('cbankieranalysis').value = 'realistic';
    if (document.getElementById('cbankiercomparison')) document.getElementById('cbankiercomparison').value = 'average_market';
    if (document.getElementById('cbankierrating')) document.getElementById('cbankierrating').value = 'a';
    calculateBankierMortgage();
}

function saveCalResult() {
    alert('Analiza Bankier.pl została zapisana!');
    return false;
}

function saveBankierAnalysis() {
    alert('Analiza została zapisana w serwisie Bankier.pl!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateBankierMortgage();
};
</script>

</body>
</html>
