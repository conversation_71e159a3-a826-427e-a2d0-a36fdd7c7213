/*
(C) kalkulator.pl wszystkie prawa zastrzeżone.
*/
function gObj(obj) {return document.getElementById(obj);}
function trimAll(sString){return sString.trim();}
function isNumber(val){val=val+"";if (val.length<1) return false;if (isNaN(val)){return false;}else{return true;}}
function isInteger(val){if (isNumber(val)){return val % 1 === 0;}else{return false;}}
function formatAsMoney(num){return formatAsMoneyFull(num, 1);}
function formatAsMoneyFull(num, hascents) {
	num = num.toString().replace(/zł|\,/g, '');
	if (isNaN(num)) num = "0";
	sign = (num == (num = Math.abs(num)));
	cents = '';
	if (hascents==1){
		num = Math.floor(num * 100 + 0.50000000001);
		cents = num % 100;
		num = Math.floor(num / 100).toString();
		if (cents < 10) cents = "0" + cents;
		cents = "," + cents;
	}else{
		num = Math.floor(num + 0.50000000001).toString();
	}
	for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++) num = num.substring(0, num.length - (4 * i + 3)) + ' ' + num.substring(num.length - (4 * i + 3));
	return (((sign) ? '' : '-') + num + cents + ' zł');
}
function clearForm(formObj){var allElements = formObj.elements; for (i = 0; i < allElements.length; i++){if ( allElements[i].type == 'text'||allElements[i].type == 'number'||allElements[i].type == 'date'||allElements[i].type == 'textarea' ) allElements[i].value= '';}}
function formatNum(inNum){outStr = ""+inNum;inNum = parseFloat(outStr);if ((outStr.length)>10){outStr = "" + inNum.toPrecision(10);}if (outStr.indexOf(".")>-1){while (outStr.charAt(outStr.length-1) == "0"){outStr = outStr.substr(0,(outStr.length-1));}if (outStr.charAt(outStr.length-1) == ".")outStr = outStr.substr(0,(outStr.length-1));return outStr;}else{return outStr;}}
function showquickmsg(inStr, isError){if (isError){inStr = "<font color=red>" + inStr + "</font>";}gObj("coutput").innerHTML = inStr;}

var tooltip=function(){
	var id = 'tt';
	var top = 3;
	var left = 3;
	var maxw = 300;
	var speed = 10;
	var timer = 20;
	var endalpha = 95;
	var alpha = 0;
	var tt,t,c,b,h;
	var isvisible = 1;
	var ismobile = false;
	var ie = document.all ? true : false;
	var htmlV = '<div onclick="tooltip.hide(); return false;" style="text-align:right;color:#fff;"><u><b>ZAMKNIJ</b></u></div>';
	return{
		show:function(v,ism){
			if (ism=='m'){ismobile = true;}else{ismobile = false;}
			if(tt == null){
				tt = document.createElement('div');
				tt.setAttribute('id',id);
				document.body.appendChild(tt);
				tt.style.opacity = 0;
				tt.style.filter = 'alpha(opacity=0)';
				document.onmousemove = this.pos;
			}
			isvisible = 0;
			tt.style.display = 'block';
			tt.innerHTML = v;
			if (ismobile) tt.innerHTML = v + htmlV;
			if(ie){tt.style.width = tt.offsetWidth;}
			if(tt.offsetWidth > maxw){tt.style.width = maxw + 'px'}
			if (ismobile) tt.style.width = '';
			h = parseInt(tt.offsetHeight) + top;
			clearInterval(tt.timer);
			tt.timer = setInterval(function(){tooltip.fade(1)},timer);
		},
		pos:function(e){
			if (isvisible==0){
				var x=0, y=0;
				if (document.all) {
					x = (document.documentElement && document.documentElement.scrollLeft) ? document.documentElement.scrollLeft : document.body.scrollLeft;
					y = (document.documentElement && document.documentElement.scrollTop) ? document.documentElement.scrollTop : document.body.scrollTop;
					x += window.event.clientX;
					y += window.event.clientY;
				} else {
					x = e.pageX;
					y = e.pageY;
				}
				if (ismobile){
					tt.style.left = "0px";
				}else{
					tt.style.left = (x+5) + "px";
				}
				tt.style.top = (y+10) + "px";
			}
			isvisible = 1;
		},
		fade:function(d){
			var a = alpha;
			if((a != endalpha && d == 1) || (a != 0 && d == -1)){
				var i = speed;
				if(endalpha - a < speed && d == 1){
					i = endalpha - a;
				}else if(alpha < speed && d == -1){
					i = a;
				}
				alpha = a + (i * d);
				tt.style.opacity = alpha * .01;
				tt.style.filter = 'alpha(opacity=' + alpha + ')';
			}else{
				clearInterval(tt.timer);
				if(d == -1){tt.style.display = 'none'}
			}
		},
		hide:function(){
			clearInterval(tt.timer);
			isvisible = 0;
			tt.style.display = 'none';
		}
	};
}();

function iptErrmsg(iptInputObj, iptMsg){
	var iptFName = iptInputObj.name+'ifcErr';
	var iptErrObj = document.getElementById(iptFName);
	if (iptMsg.length<1){
		iptInputObj.style.borderColor = "#417516";
		if (iptErrObj !== null) iptErrObj.style.visibility = 'hidden';
	}else{
		if (iptErrObj !== null){
			iptErrObj.innerHTML = iptMsg;
		}else{
			iptErrObj = document.createElement("div");
			iptErrObj.setAttribute("class", "inputErrMsg");
			iptErrObj.setAttribute("id", iptFName);
			iptErrObj.innerHTML = iptMsg;
			iptInputObj.parentNode.insertBefore(iptErrObj, iptInputObj.nextSibling);
		}
		iptErrObj.style.visibility = 'visible';
		iptInputObj.style.borderColor = "red";
	}
}

function iptfieldCheck(ifcInput, ifcRequired, ifcType){
	var ifcIVal = trimAll("" + ifcInput.value);
	var ifcErrMsg = "";
	if ("r"==ifcRequired.toLowerCase()){
		if (ifcIVal.length<1){
			ifcInput.addEventListener("blur", function(){
				var ifcrIVal = trimAll("" + ifcInput.value);
				if (ifcrIVal.length<1) iptErrmsg(ifcInput, "pole wymagane");
			});
		}
	}
	if (ifcIVal.length>0){
		var ifcTemp = ifcType.toLowerCase();
		ifcIVal = ifcIVal.replace(/	/g, "").replace(/ /g, "").replace(/,/g, "");
		if (ifcTemp=="n"){
			if ((!isNumber(ifcIVal))&&(ifcIVal!="-")&&(ifcIVal!=".")) ifcErrMsg = "tylko liczby";
		}else if (ifcTemp=="pn"){
			if (!(isNumber(ifcIVal)&&(Number(ifcIVal)>0))) ifcErrMsg = "tylko liczby dodatnie";
		}else if (ifcTemp=="pzn"){
			if (!(isNumber(ifcIVal)&&(Number(ifcIVal)>=0))) ifcErrMsg = "tylko liczby nieujemne";
		}else if (ifcTemp=="i"){
			if ((!isInteger(ifcIVal))&&(ifcIVal!="-")&&(ifcIVal!=".")) ifcErrMsg = "tylko liczby całkowite";
		}else if (ifcTemp=="pi"){
			if (!(isInteger(ifcIVal)&&(Number(ifcIVal)>0))) ifcErrMsg = "tylko liczby całkowite dodatnie";
		}else if (ifcTemp=="pzi"){
			if (!(isNumber(ifcIVal)&&(Number(ifcIVal)>=0))) ifcErrMsg = "tylko liczby całkowite nieujemne";
		}
	}
	iptErrmsg(ifcInput, ifcErrMsg);
}

function insertComma(e,l){
	let t=document.getElementById(e),d=t.value.toString().replaceAll(",",""),a="";
	if("i"==l)a=(d=d.replace(/[^\d.-]/g,"")).toString().split(".")[0].replace(/\B(?=(\d{3})+(?!\d))/g,",");
	else if("d"==l){
		let g=(d=d.replace(/[^\d.-]/g,"")).toString().split(".");
		a=g[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),g.length>1&&(a+="."+g[1])
	}else"c"==l&&(a=d.replace(/\B(?=(\d{3})+(?!\d))/g,","));
	t.value=a
}

function calcSearch(){
	var calculators = "kalkulator-finansowy|Kalkulatory finansowe|kalkulator-kredytu-hipotecznego|Kalkulator kredytu hipotecznego|kalkulator-kredytu|Kalkulator kredytu|kalkulator-leasingu|Kalkulator leasingu|kalkulator-oszczednosci|Kalkulator oszczędności|kalkulator-emerytury|Kalkulator emerytury|kalkulator-inwestycji|Kalkulator inwestycji|kalkulator-podatku|Kalkulator podatku|kalkulator-zdolnosci-kredytowej|Kalkulator zdolności kredytowej|kalkulator-raty-kredytu|Kalkulator raty kredytu|kalkulator-oprocentowania|Kalkulator oprocentowania|kalkulator-splaty-kredytu|Kalkulator spłaty kredytu".split("|");
	var searchTerm = trimAll(gObj("calcSearchTerm").value+"").replace("-"," ").replace("  "," ").replace("  "," ").toLowerCase();
	var results = "";
	var exactMatches = [], startMatches = [], containsMatches = [];
	
	if (searchTerm.length > 0) {
		var numCalcs = calculators.length / 2;
		for (i = 0; i < numCalcs; i++) {
			var calcName = "  " + calculators[2*i+1].replace("-"," ").toLowerCase();
			if (calcName.indexOf(" " + searchTerm) >= 0 && calcName.indexOf(" " + searchTerm) < 2) {
				exactMatches.push('<div><a href="/' + calculators[2*i] + '.html">' + calculators[2*i+1] + '</a></div>');
			} else if (calcName.indexOf(" " + searchTerm) > 0) {
				startMatches.push('<div><a href="/' + calculators[2*i] + '.html">' + calculators[2*i+1] + '</a></div>');
			} else if (calcName.indexOf(searchTerm) > 0) {
				containsMatches.push('<div><a href="/' + calculators[2*i] + '.html">' + calculators[2*i+1] + '</a></div>');
			}
		}
		
		var allMatches = exactMatches.concat(startMatches).concat(containsMatches);
		var matchCount = allMatches.length;
		
		if (matchCount > 0) {
			if (matchCount > 6) {
				for (i = 0; i < 6; i++) results += allMatches[i];
				results += "<div>...</div>";
			} else {
				for (i = 0; i < matchCount; i++) results += allMatches[i];
			}
		} else {
			results = 'Nie znaleziono kalkulatora dla "' + gObj("calcSearchTerm").value + '".';
		}
	}
	
	gObj("calcSearchOut").innerHTML = results;
	return false;
}

function saveCalResult(scrName, scrNum, scrSubName, scrRName, scrRVal){
	let scrUrl = window.location.pathname + "";
	let scrParam = window.location.search + "" + window.location.hash;
	let scrItems = [['surl', btoa(scrUrl)],['sparam', btoa(scrParam)],['sname', scrName],['snum', scrNum],['ssubnum', scrSubName],['srname', scrRName],['srval', scrRVal]];
	let scrForm = document.createElement("form");
	scrForm.setAttribute("method", "post");
	scrForm.setAttribute("action", "/konto/zapisz.php");
	for (let i = 0; i < scrItems.length; i++) {
		let scrfInput = document.createElement("input");
		scrfInput.type = "hidden";
		scrfInput.name = scrItems[i][0];
		scrfInput.value = scrItems[i][1];
		scrForm.appendChild(scrfInput);
	}
	document.body.appendChild(scrForm);
	scrForm.submit();
}

// Funkcje specyficzne dla kalkulatora kredytu hipotecznego
function cunitchange(fieldName, unit) {
	var field = document.getElementById(fieldName);
	if (unit === 'p') {
		field.className = field.className.replace('indollar', 'inpct');
	} else {
		field.className = field.className.replace('inpct', 'indollar');
	}
}

function cshtaxcost() {
	var checkbox = document.getElementById('caddoptional');
	var taxCostDiv = document.getElementById('ctaxcost');
	if (checkbox.checked) {
		taxCostDiv.style.display = 'block';
	} else {
		taxCostDiv.style.display = 'none';
	}
}

function amoChange(type) {
	var monthlyDiv = document.getElementById('monthlyamo');
	var selectDiv = document.getElementById('amoselect');
	
	if (type === 1) {
		monthlyDiv.style.display = 'block';
		selectDiv.innerHTML = 'Harmonogram miesięczny &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Harmonogram roczny</a>';
	} else {
		monthlyDiv.style.display = 'none';
		selectDiv.innerHTML = 'Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram miesięczny</a>';
	}
	return false;
}

// 核心房贷计算函数
function calculateMortgage(principal, annualRate, years) {
	var monthlyRate = annualRate / 100 / 12;
	var numPayments = years * 12;

	if (monthlyRate === 0) {
		return principal / numPayments;
	}

	var monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
						(Math.pow(1 + monthlyRate, numPayments) - 1);

	return monthlyPayment;
}

// 计算总利息
function calculateTotalInterest(monthlyPayment, years, principal) {
	return (monthlyPayment * years * 12) - principal;
}

// 生成摊销表
function generateAmortizationSchedule(principal, annualRate, years, monthlyPayment) {
	var schedule = [];
	var balance = principal;
	var monthlyRate = annualRate / 100 / 12;
	var numPayments = years * 12;

	for (var i = 1; i <= numPayments; i++) {
		var interestPayment = balance * monthlyRate;
		var principalPayment = monthlyPayment - interestPayment;
		balance = balance - principalPayment;

		if (balance < 0) balance = 0;

		schedule.push({
			month: i,
			payment: monthlyPayment,
			principal: principalPayment,
			interest: interestPayment,
			balance: balance
		});

		if (balance <= 0) break;
	}

	return schedule;
}

// 计算nadpłaty效果
function calculatePrepaymentEffect(currentBalance, currentPayment, rate, remainingYears, prepaymentAmount, prepaymentType) {
	var monthlyRate = rate / 100 / 12;
	var remainingMonths = remainingYears * 12;

	// 计算当前情况下的总成本
	var originalTotalCost = currentPayment * remainingMonths;
	var originalInterest = originalTotalCost - currentBalance;

	// 计算有nadpłaty的情况
	var newBalance = currentBalance;
	var totalPrepayments = 0;
	var monthsToPayoff = 0;
	var totalInterestWithPrepayment = 0;

	for (var month = 1; month <= remainingMonths; month++) {
		var interestPayment = newBalance * monthlyRate;
		var principalPayment = currentPayment - interestPayment;

		// 添加nadpłaty
		var prepayment = 0;
		if (prepaymentType === 'monthly') {
			prepayment = prepaymentAmount;
		} else if (prepaymentType === 'yearly' && month % 12 === 0) {
			prepayment = prepaymentAmount;
		} else if (prepaymentType === 'onetime' && month === 1) {
			prepayment = prepaymentAmount;
		}

		totalPrepayments += prepayment;
		principalPayment += prepayment;

		newBalance = newBalance - principalPayment;
		totalInterestWithPrepayment += interestPayment;

		if (newBalance <= 0) {
			monthsToPayoff = month;
			break;
		}
	}

	var newTotalCost = (currentPayment * monthsToPayoff) + totalPrepayments;
	var savings = originalTotalCost - newTotalCost;
	var timeSaved = remainingMonths - monthsToPayoff;

	return {
		originalTotalCost: originalTotalCost,
		newTotalCost: newTotalCost,
		savings: savings,
		timeSavedMonths: timeSaved,
		originalInterest: originalInterest,
		newInterest: totalInterestWithPrepayment,
		interestSavings: originalInterest - totalInterestWithPrepayment,
		totalPrepayments: totalPrepayments
	};
}

// 计算zdolność kredytowa
function calculateCreditCapacity(netIncome, spouseIncome, monthlyExpenses, familySize, loanTermYears, interestRate) {
	var totalIncome = netIncome + spouseIncome;
	var livingCostsPerPerson = 800; // Domyślny koszt utrzymania na osobę
	var totalLivingCosts = familySize * livingCostsPerPerson;
	var totalExpenses = monthlyExpenses + totalLivingCosts;

	// Maksymalny wskaźnik DTI (Debt-to-Income) - 40%
	var maxDTI = 0.40;
	var availableIncome = totalIncome - totalExpenses;
	var maxMonthlyPayment = Math.min(availableIncome, totalIncome * maxDTI);

	if (maxMonthlyPayment <= 0) {
		return {
			maxLoanAmount: 0,
			monthlyPayment: 0,
			dtiRatio: 0,
			availableIncome: availableIncome
		};
	}

	// Oblicz maksymalną kwotę kredytu
	var monthlyRate = interestRate / 100 / 12;
	var numPayments = loanTermYears * 12;

	var maxLoanAmount = 0;
	if (monthlyRate > 0) {
		maxLoanAmount = maxMonthlyPayment * (Math.pow(1 + monthlyRate, numPayments) - 1) /
						(monthlyRate * Math.pow(1 + monthlyRate, numPayments));
	} else {
		maxLoanAmount = maxMonthlyPayment * numPayments;
	}

	var actualPayment = calculateMortgage(maxLoanAmount, interestRate, loanTermYears);
	var dtiRatio = (actualPayment / totalIncome) * 100;

	return {
		maxLoanAmount: Math.floor(maxLoanAmount),
		monthlyPayment: actualPayment,
		dtiRatio: dtiRatio,
		availableIncome: availableIncome,
		totalIncome: totalIncome,
		totalExpenses: totalExpenses
	};
}

// Funkcja do aktualizacji wyników na stronie
function updateMortgageResults() {
	try {
		var housePrice = parseFloat(document.getElementById('chouseprice').value.replace(/[^\d.-]/g, '')) || 0;
		var downPaymentPercent = parseFloat(document.getElementById('cdownpayment').value) || 0;
		var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
		var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

		var downPaymentAmount = housePrice * (downPaymentPercent / 100);
		var loanAmount = housePrice - downPaymentAmount;

		var monthlyPayment = calculateMortgage(loanAmount, interestRate, loanTerm);
		var totalInterest = calculateTotalInterest(monthlyPayment, loanTerm, loanAmount);
		var totalCost = loanAmount + totalInterest;

		// Aktualizuj wyświetlane wyniki
		var resultElement = document.querySelector('.h2result');
		if (resultElement) {
			resultElement.innerHTML = 'Miesięczna rata: &nbsp; ' + formatAsMoney(monthlyPayment) +
				'<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult(\'...\', 0, \'...\', \'...\', \'...\');">';
		}

		return {
			monthlyPayment: monthlyPayment,
			totalInterest: totalInterest,
			totalCost: totalCost,
			loanAmount: loanAmount
		};

	} catch (error) {
		console.error('Błąd w obliczeniach:', error);
		return null;
	}
}

// Funkcja do inicjalizacji kalkulatora
function initializeCalculator() {
	// Dodaj event listenery do pól formularza
	var inputs = ['chouseprice', 'cdownpayment', 'cloanterm', 'cinterestrate'];
	inputs.forEach(function(inputId) {
		var element = document.getElementById(inputId);
		if (element) {
			element.addEventListener('input', function() {
				setTimeout(updateMortgageResults, 100);
			});
			element.addEventListener('change', function() {
				updateMortgageResults();
			});
			// Dodaj walidację
			element.addEventListener('blur', function() {
				iptfieldCheck(element, 'r', 'pn');
			});
		}
	});

	// Inicjalne obliczenie
	setTimeout(updateMortgageResults, 500);
}

// Event listeners dla automatycznych obliczeń
if (typeof document !== 'undefined') {
	document.addEventListener('DOMContentLoaded', function() {
		initializeCalculator();
	});
}

// Funkcja testowa do sprawdzenia wszystkich obliczeń
function runCalculatorTests() {
	console.log("=== TEST FUNKCJI KALKULATORA ===");

	// Test 1: Podstawowe funkcje
	console.log("1. Test podstawowych funkcji:");
	console.log("isNumber('123'):", isNumber('123'));
	console.log("isNumber('abc'):", isNumber('abc'));
	console.log("formatAsMoney(1234.56):", formatAsMoney(1234.56));

	// Test 2: Kalkulator kredytu
	console.log("\n2. Test kalkulatora kredytu:");
	var payment = calculateMortgage(400000, 7.25, 25);
	var interest = calculateTotalInterest(payment, 25, 400000);
	console.log("Kredyt 400,000 zł, 7.25%, 25 lat:");
	console.log("Miesięczna rata:", payment.toFixed(2), "zł");
	console.log("Całkowite odsetki:", interest.toFixed(2), "zł");

	// Test 3: Zdolność kredytowa
	console.log("\n3. Test zdolności kredytowej:");
	var capacity = calculateCreditCapacity(8000, 0, 3000, 2, 25, 7.25);
	console.log("Dochody 8000 zł, wydatki 3000 zł:");
	console.log("Maksymalny kredyt:", capacity.maxLoanAmount, "zł");
	console.log("DTI:", capacity.dtiRatio.toFixed(2), "%");

	// Test 4: Nadpłaty
	console.log("\n4. Test nadpłat:");
	var prepayment = calculatePrepaymentEffect(350000, 2500, 7.25, 20, 500, 'monthly');
	console.log("Nadpłata 500 zł miesięcznie:");
	console.log("Oszczędności:", prepayment.savings.toFixed(2), "zł");
	console.log("Skrócenie:", Math.floor(prepayment.timeSavedMonths/12), "lat");

	console.log("\n✅ Wszystkie testy zakończone!");
	return true;
}
