<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Test Fixed Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .crighthalf { float: right; width: 45%; }
        .clefthalf { float: left; width: 45%; }
        .h2result { color: #333; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; }
        td { padding: 5px; border: 1px solid #ddd; }
        input, select { padding: 5px; margin: 2px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result-section { background: #f9f9f9; padding: 15px; margin: 10px 0; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Test Fixed Calculator</h1>
    
    <div class="clefthalf">
        <h2>Input Values</h2>
        <table>
            <tr>
                <td><PERSON>na nieruchomości:</td>
                <td><input type="text" id="chouseprice" value="500000"> zł</td>
            </tr>
            <tr>
                <td>Wkład własny:</td>
                <td>
                    <input type="text" id="cdownpayment" value="20">
                    <select id="cdownpaymentunit">
                        <option value="p" selected>%</option>
                        <option value="d">zł</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>Okres kredytowania:</td>
                <td><input type="text" id="cloanterm" value="25"> lat</td>
            </tr>
            <tr>
                <td>Oprocentowanie:</td>
                <td><input type="text" id="cinterestrate" value="7.25"> %</td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center; padding: 15px;">
                    <button onclick="calculateMortgage()">Oblicz kredyt</button>
                    <button onclick="clearForm()">Wyczyść</button>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="crighthalf">
        <h2>Results</h2>
        <table align="center">
            <tbody>
                <tr>
                    <td>
                        <h2 class="h2result">Miesięczna rata: &nbsp; 0 zł</h2>
                        <table cellpadding="3" width="100%">
                            <tbody>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td align="right"><b>Miesięcznie</b></td>
                                    <td align="right"><b>Łącznie</b></td>
                                </tr>
                                <tr bgcolor="#dddddd">
                                    <td><b>Rata kredytu</b></td>
                                    <td align="right"><b>0 zł</b></td>
                                    <td align="right"><b>0 zł</b></td>
                                </tr>
                                <tr>
                                    <td>Podatek od nieruchomości</td>
                                    <td align="right">0 zł</td>
                                    <td align="right">0 zł</td>
                                </tr>
                                <tr>
                                    <td>Ubezpieczenie domu</td>
                                    <td align="right">0 zł</td>
                                    <td align="right">0 zł</td>
                                </tr>
                                <tr>
                                    <td>Ubezpieczenie kredytu</td>
                                    <td align="right">0 zł</td>
                                    <td align="right">0 zł</td>
                                </tr>
                                <tr>
                                    <td>Opłaty administracyjne</td>
                                    <td align="right">0 zł</td>
                                    <td align="right">0 zł</td>
                                </tr>
                                <tr bgcolor="#dddddd">
                                    <td><b>Łącznie</b></td>
                                    <td align="right"><b>0 zł</b></td>
                                    <td align="right"><b>0 zł</b></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div style="clear: both;"></div>
    
    <div class="result-section">
        <h3>Debug Information</h3>
        <div id="debugInfo">Click "Oblicz kredyt" to see debug information</div>
    </div>

    <script>
        function calculateMortgage() {
            try {
                console.log("Starting calculateMortgage function...");
                
                // 获取输入值
                var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
                var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
                var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
                var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
                var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
                
                console.log("Input values:", {housePrice, downPayment, downPaymentUnit, loanTerm, interestRate});
                
                // 计算首付和贷款金额
                var downPaymentAmount;
                if (downPaymentUnit === 'p') {
                    downPaymentAmount = housePrice * (downPayment / 100);
                } else if (downPaymentUnit === 'd') {
                    downPaymentAmount = downPayment;
                } else {
                    downPaymentAmount = downPayment;
                }
                var loanAmount = housePrice - downPaymentAmount;
                
                console.log("Calculated amounts:", {downPaymentAmount, loanAmount});
                
                // 计算月供
                var monthlyRate = interestRate / 100 / 12;
                var numPayments = loanTerm * 12;
                var monthlyPayment = 0;
                
                if (monthlyRate > 0) {
                    monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                    (Math.pow(1 + monthlyRate, numPayments) - 1);
                } else {
                    monthlyPayment = loanAmount / numPayments;
                }
                
                var totalPayment = monthlyPayment * numPayments;
                var totalInterest = totalPayment - loanAmount;
                
                console.log("Final calculations:", {monthlyPayment, totalPayment, totalInterest});
                
                // 简化费用计算
                var costs = {
                    propertyTax: housePrice * 0.005, // 0.5% rocznie
                    homeInsurance: 1200, // 1200 zł rocznie
                    lifeInsurance: loanAmount * 0.003, // 0.3% rocznie
                    accountFee: 20 * 12, // 20 zł miesięcznie
                    otherCosts: 5000 // inne koszty
                };
                
                var totalCost = loanAmount + totalInterest + costs.propertyTax * loanTerm + 
                               costs.homeInsurance * loanTerm + costs.lifeInsurance * loanTerm + 
                               costs.accountFee * loanTerm + costs.otherCosts;
                
                var monthlyTotal = monthlyPayment + (costs.propertyTax/12) + (costs.homeInsurance/12) + 
                                  (costs.lifeInsurance/12) + (costs.accountFee/12);
                
                console.log("Costs and totals:", {costs, totalCost, monthlyTotal});
                
                // 更新显示
                updateResults(loanAmount, monthlyPayment, totalPayment, totalInterest, costs,
                             totalCost, monthlyTotal, loanTerm, housePrice, downPaymentAmount);
                             
                // 更新调试信息
                document.getElementById('debugInfo').innerHTML = 
                    '<h4>✅ Calculation Successful!</h4>' +
                    '<p><strong>Loan Amount:</strong> ' + formatNumber(loanAmount) + ' zł</p>' +
                    '<p><strong>Monthly Payment:</strong> ' + formatNumber(monthlyPayment) + ' zł</p>' +
                    '<p><strong>Total Payment:</strong> ' + formatNumber(totalPayment) + ' zł</p>' +
                    '<p><strong>Total Interest:</strong> ' + formatNumber(totalInterest) + ' zł</p>' +
                    '<p><strong>Monthly Total (with costs):</strong> ' + formatNumber(monthlyTotal) + ' zł</p>';
                    
            } catch (error) {
                console.error("Error in calculateMortgage:", error);
                document.getElementById('debugInfo').innerHTML = 
                    '<h4>❌ Calculation Error!</h4>' +
                    '<p><strong>Error:</strong> ' + error.message + '</p>' +
                    '<p><strong>Stack:</strong> ' + error.stack + '</p>';
            }
        }
        
        function updateResults(loanAmount, monthlyPayment, totalPayment, totalInterest, costs,
                              totalCost, monthlyTotal, loanTerm, housePrice, downPaymentAmount) {
            function formatNumber(num) {
                return Math.round(num).toLocaleString('pl-PL');
            }
            
            console.log("Starting updateResults...");
            
            // 更新主要结果
            var resultHeader = document.querySelector('.h2result');
            if (resultHeader) {
                resultHeader.innerHTML = 'Miesięczna rata: &nbsp; ' + formatNumber(monthlyPayment) + ' zł';
                console.log("Updated h2result element");
            } else {
                console.error("h2result element not found");
            }
            
            // 更新详细结果表格
            var resultTable = document.querySelector('.crighthalf table table tbody');
            if (resultTable) {
                var rows = resultTable.querySelectorAll('tr');
                console.log("Found", rows.length, "rows in result table");
                
                if (rows.length > 1) {
                    // 更新第二行（主要月供行，索引1）
                    var mainRow = rows[1];
                    if (mainRow && mainRow.cells.length >= 3) {
                        mainRow.cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        mainRow.cells[2].innerHTML = '<b>' + formatNumber(totalPayment) + ' zł</b>';
                        console.log("Updated main row cells");
                    }
                    
                    // 更新其他行
                    if (rows[2] && rows[2].cells.length >= 3) {
                        rows[2].cells[1].innerHTML = formatNumber(costs.propertyTax/12) + ' zł';
                        rows[2].cells[2].innerHTML = formatNumber(costs.propertyTax * loanTerm) + ' zł';
                    }
                    
                    if (rows[3] && rows[3].cells.length >= 3) {
                        rows[3].cells[1].innerHTML = formatNumber(costs.homeInsurance/12) + ' zł';
                        rows[3].cells[2].innerHTML = formatNumber(costs.homeInsurance * loanTerm) + ' zł';
                    }
                    
                    if (rows[4] && rows[4].cells.length >= 3) {
                        rows[4].cells[1].innerHTML = formatNumber(costs.lifeInsurance/12) + ' zł';
                        rows[4].cells[2].innerHTML = formatNumber(costs.lifeInsurance * loanTerm) + ' zł';
                    }
                    
                    if (rows[5] && rows[5].cells.length >= 3) {
                        rows[5].cells[1].innerHTML = formatNumber(costs.accountFee/12) + ' zł';
                        rows[5].cells[2].innerHTML = formatNumber(costs.accountFee * loanTerm) + ' zł';
                    }
                    
                    // 更新总计行
                    if (rows[6] && rows[6].cells.length >= 3) {
                        rows[6].cells[1].innerHTML = '<b>' + formatNumber(monthlyTotal) + ' zł</b>';
                        rows[6].cells[2].innerHTML = '<b>' + formatNumber(totalCost) + ' zł</b>';
                    }
                }
            } else {
                console.error("Result table not found");
            }
            
            console.log("updateResults completed");
        }
        
        function formatNumber(num) {
            return Math.round(num).toLocaleString('pl-PL');
        }
        
        function clearForm() {
            document.getElementById('chouseprice').value = '500000';
            document.getElementById('cdownpayment').value = '20';
            document.getElementById('cdownpaymentunit').value = 'p';
            document.getElementById('cloanterm').value = '25';
            document.getElementById('cinterestrate').value = '7.25';
            
            // 清除结果
            var resultHeader = document.querySelector('.h2result');
            if (resultHeader) {
                resultHeader.innerHTML = 'Miesięczna rata: &nbsp; 0 zł';
            }
            
            document.getElementById('debugInfo').innerHTML = 'Form cleared. Click "Oblicz kredyt" to calculate.';
        }
        
        // 页面加载时的测试
        window.onload = function() {
            console.log("Test page loaded");
            console.log("Available elements:");
            ['chouseprice', 'cdownpayment', 'cdownpaymentunit', 'cloanterm', 'cinterestrate'].forEach(function(id) {
                var element = document.getElementById(id);
                console.log(id + ":", element ? "Found" : "NOT FOUND");
            });
            
            var h2result = document.querySelector('.h2result');
            console.log("h2result element:", h2result ? "Found" : "NOT FOUND");
            
            var resultTable = document.querySelector('.crighthalf table table tbody');
            console.log("Result table:", resultTable ? "Found" : "NOT FOUND");
        };
    </script>
</body>
</html>
