<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego PKO</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego PKO Bank Polski - oblicz ratę kredytu mieszkaniowego w PKO BP, sprawdź warunki i promocje PKO na kredyty hipoteczne 2025.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego pko, pko bank polski kredyt hipoteczny, rata kredytu pko, warunki kredytu pko bp">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-pko.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego pko</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego PKO</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w PKO Bank Polski"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-pko.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości, którą chcesz kupić. Na podstawie tej kwoty PKO obliczy maksymalną kwotę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpropertyvalue" id="cpropertyvalue" value="650000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny w PKO Bank Polski. Minimum 10% wartości nieruchomości. Wyższy wkład = lepsza marża.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w PKO Bank Polski. Maksymalnie 35 lat. Dłuższy okres = niższa rata.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w PKO BP. WIBOR 3M + marża PKO.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.45" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Profil klienta PKO</td>
                                <td align="left" colspan="2">
                                    <select name="cclientprofile" id="cclientprofile" onchange="updatePKORate();">
                                        <option value="new" selected="">Nowy klient PKO</option>
                                        <option value="existing">Obecny klient PKO</option>
                                        <option value="premium">PKO Premium</option>
                                        <option value="private">PKO Private Banking</option>
                                        <option value="young">Program Młodzi PKO</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania PKO</td>
                                <td align="left" colspan="2">
                                    <select name="cinteresttype" id="cinteresttype" onchange="updatePKOInterestType();">
                                        <option value="variable" selected="">Zmienne (WIBOR + marża PKO)</option>
                                        <option value="fixed_5">Stałe na 5 lat PKO</option>
                                        <option value="fixed_10">Stałe na 10 lat PKO</option>
                                        <option value="mixed">Mieszane PKO</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpkofees" class="cbcontainer">
                                        <input type="checkbox" name="caddpkofees" id="caddpkofees" value="1" checked="" onclick="cshpkofees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpkofeesdesc">Opłaty PKO Bank Polski</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpkofees" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty i koszty PKO</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przygotowawcza PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja PKO za przygotowanie kredytu. 0-2% kwoty kredytu w zależności od profilu klienta.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkoprovision" id="cpkoprovision" value="1.5" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rachunek kredytowy PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w PKO. 0-25 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkoaccount" id="cpkoaccount" value="15" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w PKO. 0.15-0.40% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkoinsurance" id="cpkoinsurance" value="0.25" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę PKO. 800-1500 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkovaluation" id="cpkovaluation" value="1200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty PKO <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty PKO: notariusz, wpis hipoteki, ubezpieczenie nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkoother" id="cpkoother" value="3500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpkopromo" class="cbcontainer">
                                        <input type="checkbox" name="caddpkopromo" id="caddpkopromo" value="1" onclick="cshpkopromo();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpkopromodesc">Promocje PKO 2025</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpkopromo" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Cashback PKO</td>
                                                    <td align="right"><input type="text" name="cpkocashback" id="cpkocashback" value="3000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zwolnienie z prowizji PKO</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cpkoprovisionwaiver" id="cpkoprovisionwaiver">
                                                            <option value="0" selected="">Brak zwolnienia</option>
                                                            <option value="50">50% zwolnienia</option>
                                                            <option value="100">100% zwolnienia</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz kredyt PKO" onclick="calculatePKOMortgage();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata PKO: &nbsp; 3 898 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBQS08=', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBQS08=', 'UmF0YSBQS08=', 'MyA4OTggekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>PKO Bank Polski</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata PKO</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>3 898 zł</b></td>
                                        <td align="right"><b>1 169 400 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu PKO</td>
                                        <td align="right">520 000 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">520 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał + odsetki PKO</td>
                                        <td align="right">-</td>
                                        <td align="right">3 773 zł</td>
                                        <td align="right">1 131 900 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie PKO</td>
                                        <td align="right">-</td>
                                        <td align="right">108 zł</td>
                                        <td align="right">32 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Rachunek kredytowy PKO</td>
                                        <td align="right">-</td>
                                        <td align="right">15 zł</td>
                                        <td align="right">4 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty jednorazowe PKO</td>
                                        <td align="right">12 500 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">12 500 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Szczegóły kredytu PKO:</h3>
                                            <p><strong>RRSO PKO:</strong> 8.45% (z wszystkimi opłatami PKO)</p>
                                            <p><strong>Status klienta:</strong> Nowy klient PKO (marża standardowa)</p>
                                            <p><strong>Typ oprocentowania:</strong> Zmienne WIBOR + marża PKO</p>
                                            <p><strong>Całkowity koszt PKO:</strong> 1 181 900 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 661 900 zł (127% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie profili klientów PKO</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Profil PKO</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3500 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">3750 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4000 zł</text>
                    
                    <!-- PKO Private Banking -->
                    <rect x="50" y="95" width="35" height="50" fill="#8b0000" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">Private</text>
                    <text x="67" y="90" class="mcllabelx" style="fill:#000;">3685 zł</text>
                    
                    <!-- PKO Premium -->
                    <rect x="95" y="88" width="35" height="57" fill="#dc143c" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">Premium</text>
                    <text x="112" y="83" class="mcllabelx" style="fill:#000;">3742 zł</text>
                    
                    <!-- Młodzi PKO -->
                    <rect x="140" y="82" width="35" height="63" fill="#ff6347" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">Młodzi</text>
                    <text x="157" y="77" class="mcllabelx" style="fill:#000;">3798 zł</text>
                    
                    <!-- Obecny klient -->
                    <rect x="185" y="78" width="35" height="67" fill="#cd5c5c" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">Obecny</text>
                    <text x="202" y="73" class="mcllabelx" style="fill:#000;">3825 zł</text>
                    
                    <!-- Nowy klient -->
                    <rect x="230" y="72" width="35" height="73" fill="#b22222" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">Nowy</text>
                    <text x="247" y="67" class="mcllabelx" style="fill:#000;">3898 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Profile klientów PKO &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania PKO</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Typ oprocentowania PKO</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Różnica</th>
                        </tr>
                        <tr>
                            <td>Zmienne WIBOR + marża</td>
                            <td>7.45%</td>
                            <td>3 898 zł</td>
                            <td>1 169 400 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Stałe na 5 lat PKO</td>
                            <td>7.95%</td>
                            <td>4 085 zł</td>
                            <td>1 225 500 zł</td>
                            <td>+56 100 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 10 lat PKO</td>
                            <td>8.25%</td>
                            <td>4 198 zł</td>
                            <td>1 259 400 zł</td>
                            <td>+90 000 zł</td>
                        </tr>
                        <tr>
                            <td>Mieszane PKO</td>
                            <td>7.70%</td>
                            <td>3 992 zł</td>
                            <td>1 197 600 zł</td>
                            <td>+28 200 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego PKO Bank Polski - kompletny przewodnik 2025</h2>
            <p>PKO Bank Polski to największy bank w Polsce z bogatą ofertą kredytów hipotecznych. Nasz kalkulator kredytu hipotecznego PKO uwzględnia wszystkie specyficzne warunki, opłaty i promocje PKO BP, pomagając Ci precyzyjnie obliczyć koszt kredytu mieszkaniowego w tym banku.</p>

            <h3>Aktualna oferta kredytów hipotecznych PKO 2025:</h3>
            <div style="margin: 15px 0;">
                <h4>Podstawowe parametry kredytu PKO:</h4>
                <ul>
                    <li><strong>Kwota kredytu PKO:</strong> od 50 000 zł do 2 000 000 zł</li>
                    <li><strong>Okres kredytowania PKO:</strong> od 5 do 35 lat</li>
                    <li><strong>Wkład własny PKO:</strong> minimum 10% wartości nieruchomości</li>
                    <li><strong>Oprocentowanie PKO:</strong> WIBOR 3M + marża od 2.19% do 3.89%</li>
                    <li><strong>Waluta kredytu PKO:</strong> złoty polski (PLN)</li>
                    <li><strong>Maksymalny wiek PKO:</strong> 75 lat na koniec spłaty</li>
                </ul>

                <h4>Profile klientów PKO i marże:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Profil klienta PKO</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Marża PKO</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Miesięczna rata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oszczędność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>PKO Private Banking</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.19%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.05%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 685 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">213 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">PKO Premium</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.34%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.20%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 742 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">156 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Program Młodzi PKO</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.44%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.30%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 798 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">100 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Obecny klient PKO</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.54%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.35%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 825 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">73 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Nowy klient PKO</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.59%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 898 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                    </tr>
                </table>
            </div>

            <h3>Typy oprocentowania w PKO Bank Polski:</h3>
            <div style="margin: 15px 0;">
                <h4>Oprocentowanie zmienne PKO:</h4>
                <ul>
                    <li><strong>Bazowe:</strong> WIBOR 3M + marża PKO</li>
                    <li><strong>Aktualne WIBOR 3M:</strong> około 4.86% (styczeń 2025)</li>
                    <li><strong>Całkowite oprocentowanie:</strong> 7.05% - 7.45%</li>
                    <li><strong>Zmiany oprocentowania:</strong> co 3 miesiące</li>
                    <li><strong>Przewaga:</strong> możliwość spadku przy obniżce stóp NBP</li>
                </ul>

                <h4>Oprocentowanie stałe PKO:</h4>
                <ul>
                    <li><strong>Stałe na 5 lat:</strong> 7.95% (+ 0.50% do zmiennego)</li>
                    <li><strong>Stałe na 10 lat:</strong> 8.25% (+ 0.80% do zmiennego)</li>
                    <li><strong>Przewaga:</strong> stabilność i przewidywalność rat</li>
                    <li><strong>Wada:</strong> wyższe oprocentowanie niż zmienne</li>
                    <li><strong>Po okresie stałym:</strong> automatyczne przejście na zmienne</li>
                </ul>

                <h4>Oprocentowanie mieszane PKO:</h4>
                <ul>
                    <li><strong>Kombinacja:</strong> część stała + część zmienna</li>
                    <li><strong>Oprocentowanie:</strong> 7.70% (+ 0.25% do zmiennego)</li>
                    <li><strong>Elastyczność:</strong> kompromis między stabilnością a kosztem</li>
                    <li><strong>Dostępność:</strong> dla wybranych klientów PKO</li>
                </ul>
            </div>

            <h3>Opłaty i koszty kredytu hipotecznego PKO:</h3>
            <div style="margin: 15px 0;">
                <h4>Opłaty jednorazowe PKO:</h4>
                <ul>
                    <li><strong>Prowizja przygotowawcza PKO:</strong> 0% - 2% kwoty kredytu</li>
                    <li><strong>Wycena nieruchomości PKO:</strong> 800 - 1 500 zł</li>
                    <li><strong>Opłata notarialna:</strong> 2 000 - 4 000 zł</li>
                    <li><strong>Wpis hipoteki:</strong> 200 - 500 zł</li>
                    <li><strong>Ubezpieczenie nieruchomości:</strong> 500 - 1 500 zł rocznie</li>
                </ul>

                <h4>Opłaty miesięczne PKO:</h4>
                <ul>
                    <li><strong>Rachunek kredytowy PKO:</strong> 0 - 25 zł miesięcznie</li>
                    <li><strong>Ubezpieczenie spłaty PKO:</strong> 0.15% - 0.40% kwoty kredytu rocznie</li>
                    <li><strong>Obsługa kredytu PKO:</strong> 0 zł (w ramach pakietu)</li>
                </ul>

                <h4>RRSO PKO (Rzeczywista Roczna Stopa Oprocentowania):</h4>
                <p>RRSO w PKO Bank Polski wynosi <strong>8.45%</strong> i obejmuje:</p>
                <ul>
                    <li>Oprocentowanie kredytu PKO</li>
                    <li>Prowizję przygotowawczą PKO</li>
                    <li>Opłatę za rachunek kredytowy PKO</li>
                    <li>Obowiązkowe ubezpieczenia PKO</li>
                    <li>Koszty wyceny nieruchomości PKO</li>
                </ul>
            </div>

            <h3>Promocje PKO Bank Polski 2025:</h3>
            <ul>
                <li><strong>Cashback PKO:</strong> do 5 000 zł zwrotu przy kredycie powyżej 300 000 zł</li>
                <li><strong>Zwolnienie z prowizji PKO:</strong> 50% lub 100% dla wybranych klientów</li>
                <li><strong>Darmowy rachunek PKO:</strong> brak opłat za rachunek kredytowy przez 12 miesięcy</li>
                <li><strong>Preferencyjne ubezpieczenie PKO:</strong> zniżka 20% na ubezpieczenie spłaty</li>
                <li><strong>Program Młodzi PKO:</strong> obniżona marża dla klientów do 35 lat</li>
                <li><strong>PKO Premium:</strong> dodatkowe korzyści dla klientów premium</li>
            </ul>

            <h3>Wymagania kredytowe PKO Bank Polski:</h3>
            <div style="margin: 15px 0;">
                <h4>Wymagania dochodowe PKO:</h4>
                <ul>
                    <li><strong>Minimalny dochód PKO:</strong> 3 000 zł netto miesięcznie</li>
                    <li><strong>Maksymalny DTI PKO:</strong> 40% dochodów netto</li>
                    <li><strong>Stażpracy PKO:</strong> minimum 3 miesiące na obecnym stanowisku</li>
                    <li><strong>Wiek kredytobiorcy PKO:</strong> 18-75 lat</li>
                </ul>

                <h4>Wymagania dotyczące nieruchomości PKO:</h4>
                <ul>
                    <li><strong>Rodzaj nieruchomości PKO:</strong> mieszkania, domy, działki budowlane</li>
                    <li><strong>Lokalizacja PKO:</strong> cała Polska</li>
                    <li><strong>Wycena PKO:</strong> przez rzeczoznawcę z listy PKO</li>
                    <li><strong>Ubezpieczenie PKO:</strong> obowiązkowe od ognia i innych zdarzeń losowych</li>
                </ul>
            </div>

            <h3>Proces uzyskania kredytu hipotecznego PKO:</h3>
            <ol>
                <li><strong>Wstępna kalkulacja PKO</strong> - użyj naszego kalkulatora</li>
                <li><strong>Złożenie wniosku PKO</strong> - online lub w oddziale</li>
                <li><strong>Analiza zdolności PKO</strong> - weryfikacja dochodów i zobowiązań</li>
                <li><strong>Wycena nieruchomości PKO</strong> - przez rzeczoznawcę PKO</li>
                <li><strong>Decyzja kredytowa PKO</strong> - pozytywna lub negatywna</li>
                <li><strong>Podpisanie umowy PKO</strong> - u notariusza</li>
                <li><strong>Uruchomienie kredytu PKO</strong> - przelew środków</li>
                <li><strong>Wpis hipoteki PKO</strong> - zabezpieczenie kredytu</li>
            </ol>

            <h3>Zalety kredytu hipotecznego PKO:</h3>
            <ul>
                <li><strong>Największy bank w Polsce</strong> - stabilność i bezpieczeństwo</li>
                <li><strong>Szeroka sieć oddziałów PKO</strong> - obsługa w całej Polsce</li>
                <li><strong>Doświadczenie PKO</strong> - lider rynku kredytów hipotecznych</li>
                <li><strong>Elastyczne warunki PKO</strong> - różne profile klientów</li>
                <li><strong>Promocje PKO</strong> - atrakcyjne oferty specjalne</li>
                <li><strong>Bankowość internetowa PKO</strong> - wygodna obsługa online</li>
                <li><strong>Kompleksowa obsługa PKO</strong> - od wniosku do spłaty</li>
            </ul>

            <h3>Porady ekspertów dotyczące kredytu PKO:</h3>
            <ul>
                <li>Sprawdź, czy kwalifikujesz się do programu Młodzi PKO lub PKO Premium</li>
                <li>Rozważ oprocentowanie stałe PKO w okresie wysokich stóp procentowych</li>
                <li>Skorzystaj z promocji PKO - cashback może znacznie obniżyć koszty</li>
                <li>Porównaj warunki PKO z innymi bankami przed podjęciem decyzji</li>
                <li>Negocjuj marżę PKO - szczególnie jako klient premium</li>
                <li>Rozważ ubezpieczenie spłaty PKO w pierwszych latach kredytu</li>
                <li>Monitoruj zmiany WIBOR i rozważ refinansowanie PKO</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator kredytu</a>
                <a href="/kalkulator-kredytu-hipotecznego-ing.html">Kalkulator kredytu ING</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Profile klientów PKO</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz profil PKO:</strong><br>
                        <a href="#" onclick="return setPKOProfile('private');">Private Banking</a><br>
                        <a href="#" onclick="return setPKOProfile('premium');">PKO Premium</a><br>
                        <a href="#" onclick="return setPKOProfile('young');">Młodzi PKO</a><br>
                        <a href="#" onclick="return setPKOProfile('existing');">Obecny klient</a><br>
                        <a href="#" onclick="return setPKOProfile('new');">Nowy klient</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego PKO Bank Polski - oblicz ratę kredytu mieszkaniowego w PKO BP, sprawdź warunki i promocje.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePKORate() {
    var profile = document.getElementById('cclientprofile').value;
    var rateField = document.getElementById('cinterestrate');
    var baseRate = 7.45;
    
    switch(profile) {
        case 'private':
            rateField.value = (baseRate - 0.40).toFixed(2);
            break;
        case 'premium':
            rateField.value = (baseRate - 0.25).toFixed(2);
            break;
        case 'young':
            rateField.value = (baseRate - 0.15).toFixed(2);
            break;
        case 'existing':
            rateField.value = (baseRate - 0.10).toFixed(2);
            break;
        case 'new':
            rateField.value = baseRate.toFixed(2);
            break;
    }
}

function updatePKOInterestType() {
    var type = document.getElementById('cinteresttype').value;
    var rateField = document.getElementById('cinterestrate');
    var baseRate = 7.45;
    
    switch(type) {
        case 'variable':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'fixed_5':
            rateField.value = (baseRate + 0.50).toFixed(2);
            break;
        case 'fixed_10':
            rateField.value = (baseRate + 0.80).toFixed(2);
            break;
        case 'mixed':
            rateField.value = (baseRate + 0.25).toFixed(2);
            break;
    }
}

function cshpkofees() {
    var checkbox = document.getElementById('caddpkofees');
    var feesDiv = document.getElementById('cpkofees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshpkopromo() {
    var checkbox = document.getElementById('caddpkopromo');
    var promoDiv = document.getElementById('cpkopromo');
    if (checkbox.checked) {
        promoDiv.style.display = 'block';
    } else {
        promoDiv.style.display = 'none';
    }
}

function setPKOProfile(profile) {
    document.getElementById('cclientprofile').value = profile;
    updatePKORate();
    return false;
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania PKO &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Profile klientów PKO</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Profile klientów PKO &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania PKO</a>';
    }
    return false;
}
</script>

<script>
function calculatePKOMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 获取PKO特殊费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // 更新显示
    updatePKOResults(loanAmount, monthlyPayment, totalInterest, costs,
                    totalCost, monthlyTotal, rrso, loanTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updatePKOResults(loanAmount, monthlyPayment, totalInterest, costs,
                         totalCost, monthlyTotal, rrso, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Kredyt PKO: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły kredytu PKO
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegóły kredytu PKO:</h3>' +
            '<p><strong>RRSO PKO:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi opłatami PKO)</p>' +
            '<p><strong>Status klienta:</strong> ' + getPKOClientStatus() + '</p>' +
            '<p><strong>Program PKO:</strong> ' + getPKOProgram() + '</p>' +
            '<p><strong>Całkowity koszt PKO:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Przepłacone odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + Math.round((totalInterest/loanAmount)*100) + '% kwoty kredytu)</p>';
    }
}

function getPKOClientStatus() {
    var profile = document.getElementById('cpkoprofile');
    if (!profile) return 'Standardowy klient PKO';

    switch(profile.value) {
        case 'private': return 'PKO Private Banking (marża preferencyjna)';
        case 'premium': return 'PKO Premium (marża preferencyjna)';
        case 'young': return 'PKO Młodzi (marża promocyjna)';
        case 'existing': return 'Obecny klient PKO (marża preferencyjna)';
        default: return 'Nowy klient PKO (marża standardowa)';
    }
}

function getPKOProgram() {
    var program = document.getElementById('cpkoprogram');
    if (!program) return 'PKO Kredyt hipoteczny standardowy';

    switch(program.value) {
        case 'standard': return 'PKO Kredyt hipoteczny standardowy';
        case 'first': return 'PKO Pierwszy kredyt (dla młodych)';
        case 'family': return 'PKO Kredyt rodzinny (z dopłatami)';
        case 'green': return 'PKO Kredyt zielony (energooszczędny)';
        default: return 'PKO Kredyt hipoteczny standardowy';
    }
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '500000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.05';
    calculatePKOMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora PKO zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePKOMortgage();
};
</script>

</body>
</html>
