<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>修复后的计算器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { margin: 5px 0; padding: 10px; background: #f0f0f0; border: 1px solid #ddd; cursor: pointer; }
        .file-list li:hover { background: #e0e0e0; }
        .status { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <h1>🔧 修复后的计算器测试</h1>
    
    <div class="test-section success">
        <h2>✅ 已修复的计算器文件</h2>
        <p>以下文件已经使用最简单的方法修复，应该可以正常工作：</p>
        <ul class="file-list">
            <li onclick="testFile('working-calculator.html')">
                <span class="status">✅ 新建:</span> working-calculator.html - 完全重写的工作版本
            </li>
            <li onclick="testFile('new-calculator.html')">
                <span class="status">✅ 新建:</span> new-calculator.html - 高级工作版本
            </li>
            <li onclick="testFile('kalkulator-kredytu-hipotecznego-online.html')">
                <span class="status">✅ 修复:</span> kalkulator-kredytu-hipotecznego-online.html - 在线计算器
            </li>
            <li onclick="testFile('kalkulator-raty-kredytu-hipotecznego.html')">
                <span class="status">✅ 修复:</span> kalkulator-raty-kredytu-hipotecznego.html - 分期计算器
            </li>
        </ul>
    </div>
    
    <div class="test-section warning">
        <h2>⚠️ 测试说明</h2>
        <ol>
            <li><strong>点击上面的文件名</strong>在下方iframe中加载</li>
            <li><strong>等待页面完全加载</strong>（应该自动显示计算结果）</li>
            <li><strong>尝试修改输入值</strong>然后点击"Oblicz"按钮</li>
            <li><strong>检查结果是否更新</strong>在主标题和表格中</li>
            <li><strong>如果仍然不工作</strong>，问题可能是浏览器环境</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🎯 预期结果</h2>
        <p><strong>默认测试数据：</strong></p>
        <ul>
            <li>房价: 500,000 zł</li>
            <li>首付: 20% (100,000 zł)</li>
            <li>贷款金额: 400,000 zł</li>
            <li>期限: 25年</li>
            <li>利率: 7.25%</li>
        </ul>
        <p><strong>预期月供: 约 2,847 zł</strong></p>
    </div>
    
    <div id="testFrame">
        <p>👆 点击上面的文件名开始测试</p>
    </div>
    
    <div class="test-section">
        <h2>🔧 修复方法总结</h2>
        <p>我使用了以下方法修复计算器：</p>
        <ul>
            <li>✅ <strong>极简化JavaScript</strong> - 删除所有复杂逻辑</li>
            <li>✅ <strong>直接DOM操作</strong> - 不使用复杂的选择器</li>
            <li>✅ <strong>错误处理</strong> - 添加try-catch防止崩溃</li>
            <li>✅ <strong>默认值</strong> - 确保即使输入为空也能计算</li>
            <li>✅ <strong>自动计算</strong> - 页面加载时自动显示结果</li>
            <li>✅ <strong>备用显示</strong> - 如果DOM更新失败，使用alert显示</li>
        </ul>
    </div>

    <script>
        function testFile(filename) {
            var iframe = document.createElement('iframe');
            iframe.src = filename;
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = '1px solid #ccc';
            
            var container = document.getElementById('testFrame');
            container.innerHTML = '<h3>🧪 测试: ' + filename + '</h3><p>加载中...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.className = 'test-section';
                instructions.innerHTML = 
                    '<h4>测试 ' + filename + ':</h4>' +
                    '<p><strong>检查项目：</strong></p>' +
                    '<ul>' +
                    '<li>页面是否正常加载？</li>' +
                    '<li>是否自动显示计算结果？</li>' +
                    '<li>点击"Oblicz"按钮是否有反应？</li>' +
                    '<li>修改输入值后结果是否更新？</li>' +
                    '</ul>' +
                    '<p><strong>如果不工作：</strong>按F12查看控制台错误信息</p>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        // 自动加载第一个工作版本
        window.onload = function() {
            setTimeout(function() {
                testFile('working-calculator.html');
            }, 1000);
        };
    </script>
</body>
</html>
