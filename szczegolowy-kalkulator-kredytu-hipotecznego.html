<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Szczegółowy kalkulator kredytu hipotecznego</title>
    <meta name="description" content="Szczegółowy kalkulator kredytu hipotecznego z pełną analizą kosztów, harmonogramem spłat, symulacją nadpłat i porównaniem różnych wariantów kredytu mieszkaniowego.">
    <meta name="keywords" content="szczegółowy kalkulator kredytu hipotecznego, analiza kredytu, harmonogram spłat, symulacja kredytu, porównanie kredytów">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/szczegolowy-kalkulator-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">szczegółowy kalkulator kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Szczegółowy kalkulator kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Wprowadź szczegółowe parametry kredytu dla pełnej analizy"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/szczegolowy-kalkulator-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Cena nieruchomości</td>
                                <td align="right"><input type="text" name="chouseprice" id="chouseprice" value="550000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Płatność z góry przy zakupie, zwykle procent całkowitej ceny. Wyższy wkład własny oznacza lepsze warunki kredytu i niższe oprocentowanie.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="25" class="inhalf inpct"></td>
                                <td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czas spłaty kredytu w latach. Dłuższy okres oznacza niższą ratę, ale wyższy całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="30" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu. Może być stała lub zmienna w zależności od typu kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.8" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania</td>
                                <td align="left" colspan="2">
                                    <select name="cinteresttype" id="cinteresttype">
                                        <option value="fixed">Stałe</option>
                                        <option value="variable" selected="">Zmienne</option>
                                        <option value="mixed">Mieszane (5 lat stałe)</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ raty</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype">
                                        <option value="annuity" selected="">Annuitetowa (stała)</option>
                                        <option value="decreasing">Malejąca</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Data rozpoczęcia</td>
                                <td align="left" colspan="2">
                                    <select name="cstartmonth" id="cstartmonth">
                                        <option value="1">Styczeń</option>
                                        <option value="2">Luty</option>
                                        <option value="3">Marzec</option>
                                        <option value="4">Kwiecień</option>
                                        <option value="5">Maj</option>
                                        <option value="6">Czerwiec</option>
                                        <option value="7">Lipiec</option>
                                        <option value="8" selected="">Sierpień</option>
                                        <option value="9">Wrzesień</option>
                                        <option value="10">Październik</option>
                                        <option value="11">Listopad</option>
                                        <option value="12">Grudzień</option>
                                    </select> 
                                    <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddoptional" class="cbcontainer">
                                        <input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();">
                                        <span class="cbmark"></span>
                                        <b><span id="ctaxcostdesc">Uwzględnij szczegółowe koszty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ctaxcost" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Miesięczne koszty</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Podatek od nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczny podatek od nieruchomości płacony gminie. Wysokość zależy od lokalizacji i typu nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpropertytaxes" id="cpropertytaxes" value="0.9" class="innormal inpct"></td>
                                                    <td><select name="cpropertytaxesunit" onchange="cunitchange('cpropertytaxes', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obowiązkowe ubezpieczenie nieruchomości od ognia i innych zdarzeń losowych wymagane przez bank.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="chomeins" id="chomeins" value="2500" class="innormal indollar"></td>
                                                    <td><select name="chomeinsunit" onchange="cunitchange('chomeins', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu na wypadek utraty zdolności do pracy lub śmierci kredytobiorcy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpmi" id="cpmi" value="180" class="innormal indollar"></td>
                                                    <td><select name="cpmiunit" onchange="cunitchange('cpmi', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Opłaty administracyjne <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczne opłaty za administrację budynku, wspólnotę mieszkaniową, zarząd nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="choa" id="choa" value="350" class="innormal indollar"></td>
                                                    <td><select name="choaunit" onchange="cunitchange('choa', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja bankowa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego i obsługę kredytu przez bank.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankfee" id="cbankfee" value="25" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne koszty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe miesięczne koszty: media, utrzymanie, remonty, rezerwy na naprawy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cothercost" id="cothercost" value="400" class="innormal indollar"></td>
                                                    <td><select name="cothercostunit" onchange="cunitchange('cothercost', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddprepayment" class="cbcontainer">
                                        <input type="checkbox" name="caddprepayment" id="caddprepayment" value="1" onclick="cshprepayment();">
                                        <span class="cbmark"></span>
                                        <b><span id="cprepaymentdesc">Symuluj nadpłaty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cprepayment" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Miesięczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota płacona miesięcznie ponad standardową ratę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmonthlyprepayment" id="cmonthlyprepayment" value="500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Roczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota płacona raz w roku, np. z premii lub zwrotu podatku.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cyearlyprepayment" id="cyearlyprepayment" value="10000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz szczegółowo" onclick="calculateDetailedMortgage();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 3 128 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('U3pjemVnw7PFgm93eSBrYWxrdWxhdG9yIGtyZWR5dHUgaGlwb3RlY3puZWdv', 0, 'U3pjemVnw7PFgm93eSBrYWxrdWxhdG9yIGtyZWR5dHUgaGlwb3RlY3puZWdv', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MyAxMjggekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Rocznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata kredytu</b></td>
                                        <td align="right"><b>3 128 zł</b></td>
                                        <td align="right"><b>37 536 zł</b></td>
                                        <td align="right"><b>1 126 080 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Podatek od nieruchomości</td>
                                        <td align="right">413 zł</td>
                                        <td align="right">4 950 zł</td>
                                        <td align="right">148 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie nieruchomości</td>
                                        <td align="right">208 zł</td>
                                        <td align="right">2 500 zł</td>
                                        <td align="right">75 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie kredytu</td>
                                        <td align="right">180 zł</td>
                                        <td align="right">2 160 zł</td>
                                        <td align="right">64 800 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty administracyjne</td>
                                        <td align="right">350 zł</td>
                                        <td align="right">4 200 zł</td>
                                        <td align="right">126 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Prowizja bankowa</td>
                                        <td align="right">25 zł</td>
                                        <td align="right">300 zł</td>
                                        <td align="right">9 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Inne koszty</td>
                                        <td align="right">400 zł</td>
                                        <td align="right">4 800 zł</td>
                                        <td align="right">144 000 zł</td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowity koszt miesięczny</b></td>
                                        <td align="right">4 704 zł</td>
                                        <td align="right">56 446 zł</td>
                                        <td align="right">1 693 380 zł</td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" style="padding-top: 15px;">
                                            <table cellpadding="3" width="100%">
                                                <tr bgcolor="#f0f0f0">
                                                    <td><b>Szczegółowa analiza kredytu:</b></td>
                                                    <td align="right">&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td>Kwota kredytu</td>
                                                    <td align="right">412 500 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Wkład własny</td>
                                                    <td align="right">137 500 zł (25%)</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowita kwota do spłaty</td>
                                                    <td align="right">1 126 080 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowite odsetki</td>
                                                    <td align="right">713 580 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Stosunek odsetek do kapitału</td>
                                                    <td align="right">173%</td>
                                                </tr>
                                                <tr>
                                                    <td>RRSO (rzeczywista roczna stopa oprocentowania)</td>
                                                    <td align="right">8.2%</td>
                                                </tr>
                                                <tr>
                                                    <td>Data zakończenia spłaty</td>
                                                    <td align="right">Sierpień 2055</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Szczegółowy harmonogram spłat</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Rok</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">0 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">200K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">400K zł</text>
                    <line x1="45" y1="37.542" x2="292" y2="37.542" class="mclgrid"></line>
                    <text x="43" y="37.542" class="mcllabely">600K zł</text>
                    
                    <path d="M 48 87.689 L 292 145" fill="none" stroke="#2b7ddb" stroke-width="3"></path>
                    <path d="M 48 145 L 292 71.835" fill="none" stroke="#8bbc21" stroke-width="3"></path>
                    <path d="M 48 145 L 292 14.524" fill="none" stroke="#910000" stroke-width="3"></path>
                    <path d="M 48 145 L 292 25.124" fill="none" stroke="#ffa726" stroke-width="2" stroke-dasharray="3,3"></path>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                    <rect x="53" y="17" width="20" height="6" style="fill:#2b7ddb;"></rect>
                    <text x="78" y="24" class="mcllegend">Saldo</text>
                    <rect x="53" y="35" width="20" height="6" style="fill:#8bbc21;"></rect>
                    <text x="78" y="42" class="mcllegend">Odsetki</text>
                    <rect x="53" y="53" width="20" height="6" style="fill:#910000;"></rect>
                    <text x="78" y="60" class="mcllegend">Płatność</text>
                    <rect x="53" y="71" width="20" height="6" style="fill:#ffa726;"></rect>
                    <text x="78" y="78" class="mcllegend">Koszty dodatkowe</text>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram miesięczny</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Miesiąc</th>
                            <th>Rata</th>
                            <th>Kapitał</th>
                            <th>Odsetki</th>
                            <th>Saldo</th>
                            <th>Koszty dodatkowe</th>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>3 128 zł</td>
                            <td>1 447 zł</td>
                            <td>2 681 zł</td>
                            <td>411 053 zł</td>
                            <td>1 576 zł</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>3 128 zł</td>
                            <td>1 456 zł</td>
                            <td>2 672 zł</td>
                            <td>409 597 zł</td>
                            <td>1 576 zł</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>3 128 zł</td>
                            <td>1 465 zł</td>
                            <td>2 663 zł</td>
                            <td>408 132 zł</td>
                            <td>1 576 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Szczegółowy kalkulator kredytu hipotecznego - pełna analiza</h2>
            <p>Nasz szczegółowy kalkulator kredytu hipotecznego oferuje kompleksową analizę wszystkich aspektów kredytu mieszkaniowego. W przeciwieństwie do podstawowych kalkulatorów, uwzględnia wszystkie koszty związane z kredytem i pozwala na szczegółowe porównanie różnych wariantów.</p>

            <h3>Zaawansowane funkcje kalkulatora:</h3>
            <div style="margin: 15px 0;">
                <h4>Szczegółowa analiza kosztów:</h4>
                <ul>
                    <li>Podatek od nieruchomości - uwzględnienie lokalnych stawek</li>
                    <li>Ubezpieczenie nieruchomości - obowiązkowe i dodatkowe</li>
                    <li>Ubezpieczenie kredytu - ochrona spłaty</li>
                    <li>Opłaty administracyjne - wspólnota, zarząd</li>
                    <li>Prowizje bankowe - miesięczne opłaty za obsługę</li>
                    <li>Inne koszty - media, utrzymanie, rezerwy</li>
                </ul>

                <h4>Różne typy oprocentowania:</h4>
                <ul>
                    <li><strong>Stałe oprocentowanie</strong> - niezmienne przez cały okres</li>
                    <li><strong>Zmienne oprocentowanie</strong> - oparte na WIBOR + marża</li>
                    <li><strong>Mieszane oprocentowanie</strong> - początkowo stałe, potem zmienne</li>
                </ul>

                <h4>Typy rat kredytowych:</h4>
                <ul>
                    <li><strong>Rata annuitetowa</strong> - stała wysokość przez cały okres</li>
                    <li><strong>Rata malejąca</strong> - zmniejszająca się w czasie</li>
                </ul>

                <h4>Symulacja nadpłat:</h4>
                <ul>
                    <li>Miesięczne nadpłaty - regularne dodatkowe wpłaty</li>
                    <li>Roczne nadpłaty - wykorzystanie premii i zwrotów</li>
                    <li>Analiza oszczędności z nadpłat</li>
                    <li>Porównanie scenariuszy z nadpłatami i bez</li>
                </ul>
            </div>

            <h3>RRSO - Rzeczywista Roczna Stopa Oprocentowania:</h3>
            <p>Kalkulator automatycznie oblicza RRSO, które uwzględnia:</p>
            <ul>
                <li>Oprocentowanie nominalne kredytu</li>
                <li>Wszystkie opłaty i prowizje bankowe</li>
                <li>Koszty ubezpieczeń wymaganych przez bank</li>
                <li>Opłaty za wycenę nieruchomości</li>
                <li>Koszty prowadzenia rachunku</li>
            </ul>

            <h3>Analiza różnych scenariuszy:</h3>
            <div style="margin: 15px 0;">
                <h4>Porównanie okresów kredytowania:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Okres</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Miesięczna rata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Całkowite odsetki</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Całkowity koszt</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">15 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 825 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">275 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">688 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 045 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">501 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">914 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">30 lat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 128 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">713 580 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 126 080 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Czynniki wpływające na wysokość raty:</h3>
            <ul>
                <li><strong>Kwota kredytu</strong> - im wyższa, tym wyższa rata</li>
                <li><strong>Okres kredytowania</strong> - dłuższy okres = niższa rata</li>
                <li><strong>Oprocentowanie</strong> - wyższe oprocentowanie = wyższa rata</li>
                <li><strong>Wkład własny</strong> - wyższy wkład = niższa kwota kredytu</li>
                <li><strong>Typ raty</strong> - annuitetowa vs malejąca</li>
                <li><strong>Dodatkowe koszty</strong> - ubezpieczenia, opłaty, podatki</li>
            </ul>

            <h3>Optymalizacja kosztów kredytu:</h3>
            <ul>
                <li>Zwiększ wkład własny do minimum 20% wartości nieruchomości</li>
                <li>Porównaj oferty różnych banków</li>
                <li>Negocjuj marżę bankową</li>
                <li>Rozważ krótszy okres kredytowania</li>
                <li>Sprawdź możliwość wcześniejszej spłaty bez prowizji</li>
                <li>Uwzględnij wszystkie koszty w budżecie</li>
                <li>Rozważ ubezpieczenia grupowe</li>
            </ul>

            <h3>Harmonogram spłat - szczegółowa analiza:</h3>
            <p>Szczegółowy harmonogram pokazuje:</p>
            <ul>
                <li><strong>Podział każdej raty</strong> - kapitał vs odsetki</li>
                <li><strong>Pozostałe saldo</strong> - po każdej racie</li>
                <li><strong>Koszty dodatkowe</strong> - miesięczne opłaty</li>
                <li><strong>Skumulowane koszty</strong> - łączne wydatki</li>
                <li><strong>Procent spłaty</strong> - postęp w spłacie kredytu</li>
            </ul>

            <h3>Kiedy wybrać ratę malejącą?</h3>
            <ul>
                <li>Gdy masz wysoką zdolność kredytową</li>
                <li>Gdy spodziewasz się wzrostu dochodów</li>
                <li>Gdy chcesz minimalizować całkowity koszt kredytu</li>
                <li>Gdy planujesz wcześniejszą spłatę</li>
                <li>Gdy masz stabilną sytuację finansową</li>
            </ul>

            <h3>Kiedy wybrać ratę annuitetową?</h3>
            <ul>
                <li>Gdy preferujesz stałe miesięczne obciążenie</li>
                <li>Gdy masz ograniczoną zdolność kredytową</li>
                <li>Gdy planujesz długoterminową spłatę</li>
                <li>Gdy chcesz łatwiej planować budżet domowy</li>
                <li>Gdy nie planujesz nadpłat</li>
            </ul>

            <h3>Porady ekspertów:</h3>
            <ul>
                <li>Zawsze sprawdź RRSO, nie tylko oprocentowanie nominalne</li>
                <li>Uwzględnij wszystkie koszty w analizie opłacalności</li>
                <li>Zachowaj rezerwę finansową na nieprzewidziane wydatki</li>
                <li>Rozważ ubezpieczenie spłaty kredytu</li>
                <li>Monitoruj zmiany stóp procentowych</li>
                <li>Planuj nadpłaty przy wysokim oprocentowaniu</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-zdolnosci-kredytowej.html">Kalkulator zdolności kredytowej</a>
                <a href="/kalkulator-raty-kredytu-hipotecznego.html">Kalkulator raty kredytu</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-kredytu.html">Kalkulator kredytu</a>
                <a href="/kalkulator-leasingu.html">Kalkulator leasingu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Porównanie wariantów</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Różne okresy:</strong><br>
                        15 lat: <a href="#" onclick="return setTermRate('15', '7.5');">rata 3 825 zł</a><br>
                        25 lat: <a href="#" onclick="return setTermRate('25', '7.8');">rata 3 045 zł</a><br>
                        30 lat: <a href="#" onclick="return setTermRate('30', '7.8');">rata 3 128 zł</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Szczegółowy kalkulator kredytu hipotecznego z pełną analizą kosztów i harmonogramem spłat. Porównaj różne warianty kredytu mieszkaniowego.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateDetailedMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // 更新显示
    updateDetailedResults(loanAmount, monthlyPayment, totalInterest, costs,
                         totalCost, monthlyTotal, rrso, loanTerm, housePrice, downPaymentAmount);
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateDetailedResults(loanAmount, monthlyPayment, totalInterest, costs,
                              totalCost, monthlyTotal, rrso, loanTerm, housePrice, downPaymentAmount) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Szczegółowa analiza: &nbsp; ' + formatNumber(monthlyPayment) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 3) {
                switch(i) {
                    case 2: // 主要月供行
                        cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment * loanTerm * 12) + ' zł</b>';
                        break;
                    case 3: // 贷款金额
                        cells[2].innerHTML = formatNumber(loanAmount) + ' zł';
                        break;
                    case 4: // 利息
                        cells[2].innerHTML = formatNumber(totalInterest) + ' zł';
                        break;
                    case 5: // 手续费
                        cells[2].innerHTML = formatNumber(costs.provision) + ' zł';
                        break;
                }
            }
        }
    }

    // 更新szczegóły kredytu
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegółowa analiza kredytu:</h3>' +
            '<p><strong>RRSO:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi kosztami)</p>' +
            '<p><strong>Całkowity koszt kredytu:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Przepłacone odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + Math.round((totalInterest/loanAmount)*100) + '% kwoty kredytu)</p>' +
            '<p><strong>Miesięczny koszt całkowity:</strong> ' + formatNumber(monthlyTotal) + ' zł (z ubezpieczeniami)</p>' +
            '<p><strong>Stosunek kredytu do wartości:</strong> ' + Math.round((loanAmount/housePrice)*100) + '% (LTV)</p>';
    }
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '500000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    calculateDetailedMortgage();
}

function saveCalResult() {
    alert('Szczegółowe wyniki kalkulatora zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateDetailedMortgage();
};

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxCostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxCostDiv.style.display = 'block';
    } else {
        taxCostDiv.style.display = 'none';
    }
}

function cshprepayment() {
    var checkbox = document.getElementById('caddprepayment');
    var prepaymentDiv = document.getElementById('cprepayment');
    if (checkbox.checked) {
        prepaymentDiv.style.display = 'block';
    } else {
        prepaymentDiv.style.display = 'none';
    }
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function setTermRate(term, rate) {
    document.getElementById('cloanterm').value = term;
    document.getElementById('cinterestrate').value = rate;
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram miesięczny &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Harmonogram roczny</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram miesięczny</a>';
    }
    return false;
}
</script>

</body>
</html>
