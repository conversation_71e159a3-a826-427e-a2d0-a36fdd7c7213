<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Pekao SA kalkulator kredytu hipotecznego</title>
	<meta name="description" content="Pekao SA kalkulator kredytu hipotecznego - oblicz ratę kredytu mieszkaniowego w Banku Pekao SA, sprawdź warunki i korzyści dla klientów Pekao SA 2025.">
	<meta name="keywords" content="pekao sa kalkulator kredytu hipotecznego, bank pekao sa kredyt hipoteczny, kalkulator pekao sa, kredyt mieszkaniowy pekao sa">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/pekao-sa-kalkulator-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">pekao sa kalkulator kredytu hipotecznego</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Pekao SA kalkulator kredytu hipotecznego</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w Banku Pekao SA"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/pekao-sa-kalkulator-kredytu-hipotecznego.html">
<table align="center">
<tbody><tr><td align="right">Cena nieruchomości</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota własnych środków przeznaczonych na zakup nieruchomości.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu hipotecznego w latach.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Nominalne oprocentowanie kredytu hipotecznego w skali roku.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.15" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Profil klienta Pekao SA</td><td align="left" colspan="2"><select name="cpekosaprofile" id="cpekosaprofile"><option value="new" selected="">Nowy klient</option><option value="existing">Obecny klient Pekao SA</option><option value="premium">Pekao Premium</option><option value="private">Pekao Private Banking</option><option value="corporate">Klient korporacyjny</option><option value="young">Pekao dla młodych</option></select></td></tr>
<tr><td align="right">Program kredytowy</td><td align="left" colspan="2"><select name="cpekosaprogram" id="cpekosaprogram"><option value="standard" selected="">Standardowy</option><option value="first">Pierwszy kredyt</option><option value="family">Kredyt rodzinny</option><option value="green">Kredyt ekologiczny</option><option value="investment">Kredyt inwestycyjny</option><option value="loyalty">Program lojalnościowy</option></select></td></tr>
<tr><td align="right">Pakiet usług</td><td align="left" colspan="2"><select name="cpekosapackage" id="cpekosapackage"><option value="standard" selected="">Standardowy</option><option value="premium">Premium</option><option value="private">Private Banking</option><option value="corporate">Korporacyjny</option><option value="digital">Digital</option></select></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Koszty dodatkowe Pekao SA</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Opłaty Banku Pekao SA</td></tr>
	<tr><td align="right">Prowizja za udzielenie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja Banku Pekao SA za udzielenie kredytu hipotecznego.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" id="cprovisionunit"><option value="p" selected="">%</option><option value="z">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczny koszt ubezpieczenia nieruchomości w Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1250" class="innormal indollar"></td><td><select name="chomeinsunit" id="chomeinsunit"><option value="z" selected="">zł/rok</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w przypadku utraty zdolności do pracy.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.4" class="innormal inpct"></td><td><select name="cpmiunit" id="cpmiunit"><option value="p" selected="">% rocznie</option><option value="z">zł/rok</option></select></td></tr>
	<tr><td align="right">Prowadzenie rachunku <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="25" class="innormal indollar"></td><td><select name="choaunit" id="choaunit"><option value="z" selected="">zł/miesiąc</option><option value="p">%</option></select></td></tr>
	<tr><td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę Pekao SA.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cvaluation" id="cvaluation" value="2600" class="innormal indollar"></td><td>zł</td></tr>
	<tr><td align="right">Inne koszty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: notariusz, wpis do księgi wieczystej, itp.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="4200" class="innormal indollar"></td><td>zł</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Pekao SA</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Specjalne opcje Banku Pekao SA</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Pekao Premium</td><td><a href="#" onclick="setPekaoSAProfile('premium');return false;">Klient Premium</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Private Banking</td><td><a href="#" onclick="setPekaoSAProfile('private');return false;">Private Banking</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Klient korporacyjny</td><td><a href="#" onclick="setPekaoSAProfile('corporate');return false;">Corporate Banking</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Dla młodych</td><td><a href="#" onclick="setPekaoSAProfile('young');return false;">Pekao Young</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Program lojalnościowy</td><td><a href="#" onclick="setPekaoSAProfile('loyalty');return false;">Loyalty Program</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz kredyt Pekao SA" onclick="calculatePekaoSAMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Kredyt Pekao SA: &nbsp; 3 800 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('UGVrYW8gU0Ega2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 0, 'UGVrYW8gU0Ega2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 'S3JlZHl0IFBla2FvIFNB', 'MyA4MDAgenw=');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Kwota</b></td><td align="right"><b>Oprocentowanie</b></td><td align="right"><b>Korzyści</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Kwota kredytu</b></td><td align="right"><b>400 000 zł</b></td><td align="right"><b>7.15%</b></td><td align="right"><b>Standardowe</b></td></tr>
<tr><td>Miesięczna rata</td><td align="right">2 920 zł</td><td align="right">Efektywne: 7.15%</td><td align="right">Brak zniżek</td></tr>
<tr><td>Ubezpieczenia</td><td align="right">240 zł/miesiąc</td><td align="right">0.4% rocznie</td><td align="right">Standardowe</td></tr>
<tr><td>Prowadzenie rachunku</td><td align="right">25 zł/miesiąc</td><td align="right">Opłata miesięczna</td><td align="right">Standardowe</td></tr>
<tr><td>Prowizja</td><td align="right">8 000 zł</td><td align="right">2.0%</td><td align="right">Standardowa</td></tr>
<tr><td>Całkowity koszt</td><td align="right">1 175 000 zł</td><td align="right">RRSO: 8.75%</td><td align="right">Pełny koszt</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Szczegóły kredytu w Banku Pekao SA:</h3>
<p><strong>RRSO Pekao SA:</strong> 8.75% (z wszystkimi opłatami Banku Pekao SA)</p>
<p><strong>Efektywne oprocentowanie:</strong> 7.15% (po zniżkach Pekao SA)</p>
<p><strong>Status klienta:</strong> Nowy klient Banku Pekao SA</p>
<p><strong>Program kredytowy:</strong> Kredyt hipoteczny standardowy Pekao SA</p>
<p><strong>Korzyści Pekao SA:</strong><br>Standardowe warunki</p>
<p><strong>Całkowity koszt Pekao SA:</strong> 1 175 000 zł (kredyt + wszystkie opłaty)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Korzyści dla klientów Banku Pekao SA</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Korzyści klientów Pekao SA</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">Nowy</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">Premium</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">Private</text>

<!-- Nowy klient -->
<rect x="50" y="135" width="35" height="10" fill="#95a5a6" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Nowy</text>
<text x="67" y="130" class="mcllabelx" style="fill:#000;">0%</text>

<!-- Obecny klient -->
<rect x="95" y="125" width="35" height="20" fill="#3498db" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Obecny</text>
<text x="112" y="120" class="mcllabelx" style="fill:#000;">-0.1%</text>

<!-- Premium -->
<rect x="140" y="105" width="35" height="40" fill="#f39c12" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Premium</text>
<text x="157" y="100" class="mcllabelx" style="fill:#000;">-0.3%</text>

<!-- Private -->
<rect x="185" y="85" width="35" height="60" fill="#e74c3c" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Private</text>
<text x="202" y="80" class="mcllabelx" style="fill:#000;">-0.5%</text>

<!-- Corporate -->
<rect x="230" y="75" width="35" height="70" fill="#9b59b6" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Corporate</text>
<text x="247" y="70" class="mcllabelx" style="fill:#000;">-0.4%</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Korzyści Pekao SA &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie profilów</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Profil klienta</th>
<th>Zniżka marży</th>
<th>Korzyści</th>
<th>Opłaty</th>
<th>Obsługa</th>
</tr>
<tr>
<td>Nowy klient</td>
<td>0%</td>
<td>Standardowe</td>
<td>Pełne</td>
<td>Standardowa</td>
</tr>
<tr>
<td>Obecny klient</td>
<td>-0.1%</td>
<td>Preferencyjne</td>
<td>Standardowe</td>
<td>Preferencyjna</td>
</tr>
<tr>
<td>Pekao Premium</td>
<td>-0.3%</td>
<td>Rozszerzone</td>
<td>Zniżki</td>
<td>Premium</td>
</tr>
<tr>
<td>Private Banking</td>
<td>-0.5%</td>
<td>Ekskluzywne</td>
<td>Minimalne</td>
<td>Dedykowana</td>
</tr>
<tr>
<td>Klient korporacyjny</td>
<td>-0.4%</td>
<td>Biznesowe</td>
<td>Preferencyjne</td>
<td>Korporacyjna</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Pekao SA kalkulator kredytu hipotecznego - oblicz kredyt mieszkaniowy w Banku Pekao SA 2025</h2>
            <p>Pekao SA kalkulator kredytu hipotecznego to specjalistyczne narzędzie do obliczania kredytu mieszkaniowego w Banku Pekao SA. Nasz kalkulator uwzględnia wszystkie korzyści dla klientów Pekao SA, specjalne programy kredytowe oraz pakiety usług dostępne w jednym z największych banków w Polsce.</p>

            <h3>Profile klientów Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Nowy klient Banku Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> 0% (standardowe oprocentowanie)</li>
                    <li><strong>Prowizja:</strong> 2.0% kwoty kredytu</li>
                    <li><strong>Prowadzenie rachunku:</strong> 25 zł/miesiąc</li>
                    <li><strong>Korzyści:</strong> Standardowe warunki</li>
                    <li><strong>Obsługa:</strong> Standardowa w oddziałach</li>
                    <li><strong>Dla kogo:</strong> Osoby rozpoczynające współpracę z Pekao SA</li>
                </ul>

                <h4>Obecny klient Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.1% (preferencyjne oprocentowanie)</li>
                    <li><strong>Prowizja:</strong> 1.8% kwoty kredytu</li>
                    <li><strong>Prowadzenie rachunku:</strong> 20 zł/miesiąc</li>
                    <li><strong>Korzyści:</strong> Preferencyjne warunki</li>
                    <li><strong>Obsługa:</strong> Preferencyjna</li>
                    <li><strong>Dla kogo:</strong> Klienci z historią w Pekao SA</li>
                </ul>

                <h4>Pekao Premium:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.3% (znacząca zniżka)</li>
                    <li><strong>Prowizja:</strong> 1.5% kwoty kredytu (25% zniżka)</li>
                    <li><strong>Prowadzenie rachunku:</strong> Bezpłatne</li>
                    <li><strong>Korzyści:</strong> Rozszerzone korzyści</li>
                    <li><strong>Obsługa:</strong> Premium w dedykowanych strefach</li>
                    <li><strong>Dla kogo:</strong> Klienci o wysokich dochodach</li>
                </ul>

                <h4>Pekao Private Banking:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.5% (najlepsza marża)</li>
                    <li><strong>Prowizja:</strong> 1.0% kwoty kredytu (50% zniżka)</li>
                    <li><strong>Prowadzenie rachunku:</strong> Bezpłatne</li>
                    <li><strong>Wycena nieruchomości:</strong> Bezpłatna</li>
                    <li><strong>Korzyści:</strong> Ekskluzywne korzyści</li>
                    <li><strong>Obsługa:</strong> Dedykowany bankier</li>
                    <li><strong>Dla kogo:</strong> Klienci zamożni (aktywa 1 000 000+ zł)</li>
                </ul>

                <h4>Klient korporacyjny Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.4% (korporacyjna marża)</li>
                    <li><strong>Prowizja:</strong> 1.2% kwoty kredytu (40% zniżka)</li>
                    <li><strong>Prowadzenie rachunku:</strong> 15 zł/miesiąc</li>
                    <li><strong>Korzyści:</strong> Biznesowe</li>
                    <li><strong>Obsługa:</strong> Korporacyjna</li>
                    <li><strong>Dla kogo:</strong> Firmy i przedsiębiorcy</li>
                </ul>

                <h4>Pekao dla młodych:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.2% (promocyjna marża)</li>
                    <li><strong>Prowizja:</strong> 1.5% kwoty kredytu</li>
                    <li><strong>Prowadzenie rachunku:</strong> 15 zł/miesiąc</li>
                    <li><strong>Korzyści:</strong> Specjalne dla młodych</li>
                    <li><strong>Obsługa:</strong> Młodzieżowa</li>
                    <li><strong>Dla kogo:</strong> Osoby do 35 roku życia</li>
                </ul>
            </div>

            <h3>Programy kredytowe Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Pierwszy kredyt Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.3% (promocyjna)</li>
                    <li><strong>Dla kogo:</strong> Osoby kupujące pierwszą nieruchomość</li>
                    <li><strong>Wiek:</strong> Do 35 lat</li>
                    <li><strong>LTV:</strong> Do 90%</li>
                    <li><strong>Korzyści:</strong> Zniżka na ubezpieczenie</li>
                    <li><strong>Okres promocji:</strong> Pierwsze 3 lata</li>
                </ul>

                <h4>Kredyt rodzinny Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.2% (rodzinna)</li>
                    <li><strong>Dla kogo:</strong> Rodziny z dziećmi</li>
                    <li><strong>Dopłaty:</strong> Możliwość wykorzystania dopłat rządowych</li>
                    <li><strong>LTV:</strong> Do 95% z dopłatami</li>
                    <li><strong>Korzyści:</strong> Elastyczne warunki spłaty</li>
                    <li><strong>Dodatkowe:</strong> Wsparcie w procedurach</li>
                </ul>

                <h4>Kredyt ekologiczny Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.4% (ekologiczna)</li>
                    <li><strong>Dla kogo:</strong> Nieruchomości energooszczędne</li>
                    <li><strong>Wymagania:</strong> Certyfikat energetyczny A lub B</li>
                    <li><strong>LTV:</strong> Do 90%</li>
                    <li><strong>Korzyści:</strong> Najniższa marża</li>
                    <li><strong>Dodatkowe:</strong> Dofinansowanie do modernizacji</li>
                </ul>

                <h4>Kredyt inwestycyjny Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> +0.2% (wyższa marża)</li>
                    <li><strong>Dla kogo:</strong> Inwestorzy kupujący na wynajem</li>
                    <li><strong>LTV:</strong> Do 70%</li>
                    <li><strong>Wymagania:</strong> Wyższe dochody</li>
                    <li><strong>Korzyści:</strong> Elastyczne warunki</li>
                    <li><strong>Dodatkowe:</strong> Możliwość refinansowania</li>
                </ul>

                <h4>Program lojalnościowy Pekao SA:</h4>
                <ul>
                    <li><strong>Zniżka marży:</strong> -0.35% (lojalnościowa)</li>
                    <li><strong>Dla kogo:</strong> Długoletni klienci Pekao SA</li>
                    <li><strong>Wymagania:</strong> Min. 5 lat współpracy</li>
                    <li><strong>LTV:</strong> Do 85%</li>
                    <li><strong>Korzyści:</strong> Bonus za lojalność</li>
                    <li><strong>Dodatkowe:</strong> Zniżka na ubezpieczenia</li>
                </ul>
            </div>

            <h3>Pakiety usług Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Pakiet Standardowy:</h4>
                <ul>
                    <li><strong>Opłata miesięczna:</strong> 25 zł</li>
                    <li><strong>Zawiera:</strong> Podstawowe usługi bankowe</li>
                    <li><strong>Przelewy:</strong> 10 bezpłatnych miesięcznie</li>
                    <li><strong>Karty:</strong> Karta debetowa</li>
                    <li><strong>Bankowość:</strong> Internetowa i mobilna</li>
                </ul>

                <h4>Pakiet Premium:</h4>
                <ul>
                    <li><strong>Opłata miesięczna:</strong> Bezpłatny (przy kredycie)</li>
                    <li><strong>Zawiera:</strong> Rozszerzone usługi</li>
                    <li><strong>Przelewy:</strong> Bez limitu</li>
                    <li><strong>Karty:</strong> Karta kredytowa premium</li>
                    <li><strong>Bankowość:</strong> Pełny dostęp + doradca</li>
                    <li><strong>Zniżka marży:</strong> -0.1%</li>
                </ul>

                <h4>Pakiet Private Banking:</h4>
                <ul>
                    <li><strong>Opłata miesięczna:</strong> Bezpłatny</li>
                    <li><strong>Zawiera:</strong> Ekskluzywne usługi</li>
                    <li><strong>Przelewy:</strong> Bez limitu + ekspresowe</li>
                    <li><strong>Karty:</strong> Karty premium + concierge</li>
                    <li><strong>Bankowość:</strong> Dedykowany bankier</li>
                    <li><strong>Zniżka marży:</strong> -0.2%</li>
                </ul>

                <h4>Pakiet Korporacyjny:</h4>
                <ul>
                    <li><strong>Opłata miesięczna:</strong> 15 zł</li>
                    <li><strong>Zawiera:</strong> Usługi dla firm</li>
                    <li><strong>Przelewy:</strong> Biznesowe bez limitu</li>
                    <li><strong>Karty:</strong> Firmowe karty</li>
                    <li><strong>Bankowość:</strong> Biznesowa + faktoring</li>
                    <li><strong>Zniżka marży:</strong> -0.15%</li>
                </ul>

                <h4>Pakiet Digital:</h4>
                <ul>
                    <li><strong>Opłata miesięczna:</strong> 10 zł</li>
                    <li><strong>Zawiera:</strong> Usługi cyfrowe</li>
                    <li><strong>Przelewy:</strong> Tylko elektroniczne</li>
                    <li><strong>Karty:</strong> Wirtualne karty</li>
                    <li><strong>Bankowość:</strong> Tylko online/mobile</li>
                    <li><strong>Zniżka marży:</strong> -0.05%</li>
                </ul>
            </div>

            <h3>Koszty kredytu w Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Prowizja za udzielenie kredytu:</h4>
                <ul>
                    <li><strong>Nowy klient:</strong> 2.0% kwoty kredytu</li>
                    <li><strong>Obecny klient:</strong> 1.8% kwoty kredytu</li>
                    <li><strong>Premium:</strong> 1.5% kwoty kredytu</li>
                    <li><strong>Private Banking:</strong> 1.0% kwoty kredytu</li>
                    <li><strong>Korporacyjny:</strong> 1.2% kwoty kredytu</li>
                    <li><strong>Młodzi:</strong> 1.5% kwoty kredytu</li>
                </ul>

                <h4>Ubezpieczenia:</h4>
                <ul>
                    <li><strong>Ubezpieczenie nieruchomości:</strong> 1 250 zł/rok</li>
                    <li><strong>Ubezpieczenie spłaty kredytu:</strong> 0.4% kwoty kredytu rocznie</li>
                    <li><strong>Zniżki Premium:</strong> 25% zniżka na ubezpieczenia</li>
                    <li><strong>Zniżki Private:</strong> 35% zniżka na ubezpieczenia</li>
                </ul>

                <h4>Opłaty miesięczne:</h4>
                <ul>
                    <li><strong>Prowadzenie rachunku standardowego:</strong> 25 zł/miesiąc</li>
                    <li><strong>Prowadzenie rachunku Premium:</strong> Bezpłatne</li>
                    <li><strong>Prowadzenie rachunku Private:</strong> Bezpłatne</li>
                    <li><strong>Prowadzenie rachunku korporacyjnego:</strong> 15 zł/miesiąc</li>
                    <li><strong>Prowadzenie rachunku młodzieżowego:</strong> 15 zł/miesiąc</li>
                </ul>

                <h4>Koszty jednorazowe:</h4>
                <ul>
                    <li><strong>Wycena nieruchomości:</strong> 2 600 zł (bezpłatna dla Private)</li>
                    <li><strong>Notariusz:</strong> 3 500-5 500 zł</li>
                    <li><strong>Wpis do księgi wieczystej:</strong> 200 zł</li>
                    <li><strong>Inne koszty:</strong> 1 500 zł</li>
                </ul>
            </div>

            <h3>Korzyści dla klientów Banku Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Pekao Premium:</h4>
                <ul>
                    <li><strong>Zniżka na prowizję:</strong> 25%</li>
                    <li><strong>Bezpłatne prowadzenie rachunku</strong></li>
                    <li><strong>Zniżka na ubezpieczenia:</strong> 25%</li>
                    <li><strong>Obsługa priorytetowa</strong></li>
                    <li><strong>Dedykowane strefy obsługi</strong></li>
                </ul>

                <h4>Private Banking:</h4>
                <ul>
                    <li><strong>Najniższa marża:</strong> -0.5%</li>
                    <li><strong>Zniżka na prowizję:</strong> 50%</li>
                    <li><strong>Bezpłatna wycena nieruchomości</strong></li>
                    <li><strong>Dedykowany bankier</strong></li>
                    <li><strong>Ekskluzywne produkty inwestycyjne</strong></li>
                </ul>

                <h4>Klient korporacyjny:</h4>
                <ul>
                    <li><strong>Korporacyjna marża:</strong> -0.4%</li>
                    <li><strong>Zniżka na prowizję:</strong> 40%</li>
                    <li><strong>Obsługa korporacyjna</strong></li>
                    <li><strong>Powiązanie z kredytami biznesowymi</strong></li>
                    <li><strong>Dedykowany doradca korporacyjny</strong></li>
                </ul>

                <h4>Dla młodych:</h4>
                <ul>
                    <li><strong>Promocyjna marża:</strong> -0.2%</li>
                    <li><strong>Niższe opłaty miesięczne</strong></li>
                    <li><strong>Elastyczne warunki spłaty</strong></li>
                    <li><strong>Wsparcie w pierwszym kredycie</strong></li>
                </ul>

                <h4>Program lojalnościowy:</h4>
                <ul>
                    <li><strong>Bonus za lojalność:</strong> -0.35% marży</li>
                    <li><strong>Zniżka na ubezpieczenia</strong></li>
                    <li><strong>Preferencyjne warunki</strong></li>
                    <li><strong>Dodatkowe korzyści za długoletnie klientów</strong></li>
                </ul>
            </div>

            <h3>Wskazówki dla klientów Pekao SA:</h3>
            <ul>
                <li><strong>Sprawdź profil klienta:</strong> Wybierz odpowiedni profil dla najlepszych warunków</li>
                <li><strong>Rozważ pakiet Premium:</strong> Często opłacalny przy kredycie hipotecznym</li>
                <li><strong>Skorzystaj z programów specjalnych:</strong> Pierwszy kredyt, rodzinny, ekologiczny</li>
                <li><strong>Negocjuj warunki:</strong> Szczególnie prowizję i oprocentowanie</li>
                <li><strong>Porównaj z innymi bankami:</strong> Pekao SA często ma konkurencyjne warunki</li>
                <li><strong>Wykorzystaj program lojalnościowy:</strong> Dla długoletnich klientów</li>
                <li><strong>Rozważ Private Banking:</strong> Przy wyższych kwotach kredytu</li>
                <li><strong>Sprawdź korzyści korporacyjne:</strong> Dla firm i przedsiębiorców</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Profile klientów Pekao SA</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Sprawdź korzyści:</strong><br>
<a href="#" onclick="return setPekaoSAProfile('premium');">Pekao Premium</a><br>
<a href="#" onclick="return setPekaoSAProfile('private');">Private Banking</a><br>
<a href="#" onclick="return setPekaoSAProfile('corporate');">Klient korporacyjny</a><br>
<a href="#" onclick="return setPekaoSAProfile('young');">Pekao dla młodych</a><br>
<a href="#" onclick="return setPekaoSAProfile('loyalty');">Program lojalnościowy</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Pekao SA kalkulator kredytu hipotecznego - oblicz ratę kredytu mieszkaniowego w Banku Pekao SA.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculatePekaoSAMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 获取Pekao SA特殊信息
    var pekaoSAProfile = document.getElementById('cpekosaprofile').value;
    var pekaoSAProgram = document.getElementById('cpekosaprogram').value;
    var pekaoSAPackage = document.getElementById('cpekosapackage').value;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else if (downPaymentUnit === 'z') {
        downPaymentAmount = downPayment;
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 调整利率基于Pekao SA程序
    var adjustedRate = adjustPekaoSARate(interestRate, pekaoSAProfile, pekaoSAProgram, pekaoSAPackage);
    
    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取Pekao SA费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }
    
    // Pekao SA特殊korzyści
    var pekaoSABenefits = getPekaoSABenefits(pekaoSAProfile, pekaoSAProgram, pekaoSAPackage);
    
    // 应用Pekao SA korzyści
    if (pekaoSABenefits.reducedProvision) costs.provision *= 0.6;
    if (pekaoSABenefits.freeAccount) costs.accountFee = 0;
    if (pekaoSABenefits.discountInsurance) costs.lifeInsurance *= 0.75;
    if (pekaoSABenefits.freeValuation) costs.valuation = 0;
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + 
                   costs.valuation + costs.otherCosts;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updatePekaoSAResults(loanAmount, monthlyPayment, totalInterest, costs, 
                        totalCost, monthlyTotal, rrso, loanTerm, pekaoSAProfile, 
                        pekaoSAProgram, pekaoSABenefits, adjustedRate);
}

function adjustPekaoSARate(baseRate, profile, program, package) {
    var adjustment = 0;
    
    // 基于客户档案调整
    switch(profile) {
        case 'premium': adjustment -= 0.3; break;
        case 'private': adjustment -= 0.5; break;
        case 'corporate': adjustment -= 0.4; break;
        case 'young': adjustment -= 0.2; break;
        case 'existing': adjustment -= 0.1; break;
    }
    
    // 基于程序调整
    switch(program) {
        case 'first': adjustment -= 0.3; break;
        case 'family': adjustment -= 0.2; break;
        case 'green': adjustment -= 0.4; break;
        case 'investment': adjustment += 0.2; break;
        case 'loyalty': adjustment -= 0.35; break;
    }
    
    // 基于pakiet调整
    switch(package) {
        case 'premium': adjustment -= 0.1; break;
        case 'private': adjustment -= 0.2; break;
        case 'corporate': adjustment -= 0.15; break;
        case 'digital': adjustment -= 0.05; break;
    }
    
    return Math.max(baseRate + adjustment, 2.0); // Pekao SA最低2.0%
}

function getPekaoSABenefits(profile, program, package) {
    var benefits = {
        reducedProvision: false,
        freeAccount: false,
        discountInsurance: false,
        freeValuation: false,
        corporateService: false,
        loyaltyBonus: false,
        priorityService: false
    };
    
    // Premium和wyższe korzyści
    if (profile === 'premium' || profile === 'private') {
        benefits.freeAccount = true;
        benefits.discountInsurance = true;
        benefits.priorityService = true;
    }
    
    // Private Banking korzyści
    if (profile === 'private') {
        benefits.reducedProvision = true;
        benefits.freeValuation = true;
    }
    
    // Corporate korzyści
    if (profile === 'corporate') {
        benefits.reducedProvision = true;
        benefits.corporateService = true;
        benefits.priorityService = true;
    }
    
    // Program korzyści
    if (program === 'loyalty') {
        benefits.loyaltyBonus = true;
        benefits.discountInsurance = true;
    }
    
    return benefits;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;
    
    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updatePekaoSAResults(loanAmount, monthlyPayment, totalInterest, costs, 
                             totalCost, monthlyTotal, rrso, loanTerm, pekaoSAProfile, 
                             pekaoSAProgram, pekaoSABenefits, adjustedRate) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Kredyt Pekao SA: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新szczegóły kredytu Pekao SA
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var benefitsText = '';
        if (pekaoSABenefits.reducedProvision) benefitsText += '• 40% zniżka na prowizję<br>';
        if (pekaoSABenefits.freeAccount) benefitsText += '• Bezpłatne prowadzenie rachunku<br>';
        if (pekaoSABenefits.discountInsurance) benefitsText += '• 25% zniżka na ubezpieczenie<br>';
        if (pekaoSABenefits.freeValuation) benefitsText += '• Bezpłatna wycena nieruchomości<br>';
        if (pekaoSABenefits.corporateService) benefitsText += '• Obsługa korporacyjna<br>';
        if (pekaoSABenefits.loyaltyBonus) benefitsText += '• Bonus za lojalność<br>';
        if (pekaoSABenefits.priorityService) benefitsText += '• Obsługa priorytetowa<br>';
        
        detailsSection.innerHTML = 
            '<h3>Szczegóły kredytu w Banku Pekao SA:</h3>' +
            '<p><strong>RRSO Pekao SA:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi opłatami Banku Pekao SA)</p>' +
            '<p><strong>Efektywne oprocentowanie:</strong> ' + adjustedRate.toFixed(2) + '% (po zniżkach Pekao SA)</p>' +
            '<p><strong>Status klienta:</strong> ' + getPekaoSAClientStatus(pekaoSAProfile) + '</p>' +
            '<p><strong>Program kredytowy:</strong> ' + getPekaoSAProgramName(pekaoSAProgram) + '</p>' +
            '<p><strong>Korzyści Pekao SA:</strong><br>' + (benefitsText || 'Standardowe warunki') + '</p>' +
            '<p><strong>Całkowity koszt Pekao SA:</strong> ' + formatNumber(totalCost) + ' zł (kredyt + wszystkie opłaty)</p>';
    }
}

function getPekaoSAClientStatus(profile) {
    switch(profile) {
        case 'premium': return 'Klient Pekao Premium (marża preferencyjna -0.3%)';
        case 'private': return 'Pekao Private Banking (najlepsza marża -0.5%)';
        case 'corporate': return 'Klient korporacyjny Pekao SA (marża preferencyjna -0.4%)';
        case 'young': return 'Pekao dla młodych (marża promocyjna -0.2%)';
        case 'existing': return 'Obecny klient Pekao SA (marża preferencyjna -0.1%)';
        default: return 'Nowy klient Banku Pekao SA';
    }
}

function getPekaoSAProgramName(program) {
    switch(program) {
        case 'first': return 'Pierwszy kredyt Pekao SA (dla młodych)';
        case 'family': return 'Kredyt rodzinny Pekao SA (z dopłatami)';
        case 'green': return 'Kredyt ekologiczny Pekao SA (energooszczędny)';
        case 'investment': return 'Kredyt inwestycyjny Pekao SA';
        case 'loyalty': return 'Program lojalnościowy Pekao SA (dla stałych klientów)';
        default: return 'Kredyt hipoteczny standardowy Pekao SA';
    }
}

function setPekaoSAProfile(profile) {
    switch(profile) {
        case 'premium':
            document.getElementById('cpekosaprofile').value = 'premium';
            document.getElementById('cpekosapackage').value = 'premium';
            document.getElementById('cprovision').value = '1.5';
            document.getElementById('choa').value = '0';
            break;
        case 'private':
            document.getElementById('cpekosaprofile').value = 'private';
            document.getElementById('cpekosapackage').value = 'private';
            document.getElementById('cprovision').value = '1.0';
            document.getElementById('choa').value = '0';
            document.getElementById('cvaluation').value = '0';
            break;
        case 'corporate':
            document.getElementById('cpekosaprofile').value = 'corporate';
            document.getElementById('cpekosapackage').value = 'corporate';
            document.getElementById('cprovision').value = '1.2';
            document.getElementById('choa').value = '15';
            break;
        case 'young':
            document.getElementById('cpekosaprofile').value = 'young';
            document.getElementById('cpekosaprogram').value = 'first';
            document.getElementById('cprovision').value = '1.5';
            document.getElementById('choa').value = '15';
            break;
        case 'loyalty':
            document.getElementById('cpekosaprogram').value = 'loyalty';
            document.getElementById('cprovision').value = '1.3';
            break;
    }
    calculatePekaoSAMortgage();
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Koszty dodatkowe Pekao SA';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Koszty dodatkowe Pekao SA';
    }
    calculatePekaoSAMortgage();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Opcje Pekao SA</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Pekao SA</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.15';
    document.getElementById('cpekosaprofile').value = 'new';
    document.getElementById('cpekosaprogram').value = 'standard';
    document.getElementById('cpekosapackage').value = 'standard';
    
    document.getElementById('cprovision').value = '2.0';
    document.getElementById('chomeins').value = '1250';
    document.getElementById('cpmi').value = '0.4';
    document.getElementById('choa').value = '25';
    document.getElementById('cvaluation').value = '2600';
    document.getElementById('cothercost').value = '4200';
    
    calculatePekaoSAMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora Pekao SA zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie profilów &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Korzyści Pekao SA</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Korzyści Pekao SA &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie profilów</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePekaoSAMortgage();
};
</script>

</body></html>
