<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Nadpłata kredytu hipotecznego PKO BP kalkulator</title>
    <meta name="description" content="Nadpłata kredytu hipotecznego PKO BP kalkulator - oblicz oszczędności z nadpłat kredytu mieszkaniowego w PKO Bank Polski BP, sprawdź opłacalność nadpłat PKO BP 2025.">
    <meta name="keywords" content="nadpłata kredytu hipotecznego pko bp kalkulator, nadpłata kredytu pko bp, oszczędności nadpłat pko bp, opłacalność nadpłat pko bp">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/nadplata-kredytu-hipotecznego-pko-bp-kalkulator.html" itemprop="item"><span itemprop="name">nadpłata kredytu hipotecznego pko bp kalkulator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Nadpłata kredytu hipotecznego PKO BP kalkulator</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz nadpłaty kredytu hipotecznego w PKO Bank Polski BP"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/nadplata-kredytu-hipotecznego-pko-bp-kalkulator.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne saldo kredytu hipotecznego w PKO Bank Polski BP. Sprawdź w bankowości internetowej iPKO.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobploanbalance" id="cpkobploanbalance" value="450000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Miesięczna rata PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualna miesięczna rata kredytu w PKO Bank Polski BP. Sprawdź w harmonogramie spłat PKO BP.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpmonthlyrate" id="cpkobpmonthlyrate" value="3400" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w PKO Bank Polski BP. WIBOR 3M + marża PKO BP.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpinterestrate" id="cpkobpinterestrate" value="7.05" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostały okres spłaty kredytu w PKO Bank Polski BP w latach. Sprawdź w bankowości iPKO.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpremainingterm" id="cpkobpremainingterm" value="18" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ nadpłaty PKO BP</td>
                                <td align="left" colspan="2">
                                    <select name="cpkobpoverpaymenttype" id="cpkobpoverpaymenttype" onchange="updatePKOBPOverpaymentType();">
                                        <option value="monthly_pko_bp" selected="">Miesięczna nadpłata PKO BP</option>
                                        <option value="yearly_pko_bp">Roczna nadpłata PKO BP</option>
                                        <option value="quarterly_pko_bp">Kwartalna nadpłata PKO BP</option>
                                        <option value="onetime_pko_bp">Jednorazowa nadpłata PKO BP</option>
                                        <option value="mixed_pko_bp">Nadpłaty mieszane PKO BP</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="monthlypkobpoverpayment">
                                <td align="right">Miesięczna nadpłata PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Stała miesięczna nadpłata w PKO Bank Polski BP. Można ustawić w bankowości iPKO.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpmonthlyoverpayment" id="cpkobpmonthlyoverpayment" value="600" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="yearlypkobpoverpayment" style="display:none;">
                                <td align="right">Roczna nadpłata PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna nadpłata w PKO Bank Polski BP z premii, zwrotu podatku lub 13. pensji.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpyearlyoverpayment" id="cpkobpyearlyoverpayment" value="10000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="quarterlypkobpoverpayment" style="display:none;">
                                <td align="right">Kwartalna nadpłata PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Nadpłata co kwartał w PKO Bank Polski BP. Kompromis między częstotliwością a wygodą.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobpquarterlyoverpayment" id="cpkobpquarterlyoverpayment" value="2500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="onetimepkobpoverpayment" style="display:none;">
                                <td align="right">Jednorazowa nadpłata PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Jednorazowa duża nadpłata w PKO Bank Polski BP ze sprzedaży inwestycji lub spadku.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpkobponetimeoverpayment" id="cpkobponetimeoverpayment" value="75000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="mixedpkobpoverpayment" style="display:none;">
                                <td align="right">Miesięczna + roczna PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kombinacja miesięcznych i rocznych nadpłat w PKO Bank Polski BP dla maksymalnych oszczędności.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right">
                                    <input type="text" name="cpkobpmixedmonthly" id="cpkobpmixedmonthly" value="400" class="in4char indollar" placeholder="Miesięczna"> + 
                                    <input type="text" name="cpkobpmixedyearly" id="cpkobpmixedyearly" value="6000" class="in4char indollar" placeholder="Roczna">
                                </td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Efekt nadpłaty PKO BP</td>
                                <td align="left" colspan="2">
                                    <select name="cpkobpoverpaymenteffect" id="cpkobpoverpaymenteffect">
                                        <option value="reduce_term_pko_bp" selected="">Skrócenie okresu spłaty PKO BP</option>
                                        <option value="reduce_payment_pko_bp">Zmniejszenie miesięcznej raty PKO BP</option>
                                        <option value="mixed_effect_pko_bp">Efekt mieszany PKO BP</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpkobpfees" class="cbcontainer">
                                        <input type="checkbox" name="caddpkobpfees" id="caddpkobpfees" value="1" onclick="cshpkobpfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpkobpfeesdesc">Opłaty za nadpłaty PKO BP</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpkobpfees" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty za nadpłaty PKO BP</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja za nadpłatę PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja PKO Bank Polski BP za nadpłatę kredytu. 0-2% nadpłacanej kwoty.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpoverpayfee" id="cpkobpoverpayfee" value="1.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Maksymalna prowizja PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Maksymalna prowizja PKO Bank Polski BP za nadpłatę w złotych. Limit górny opłaty.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpmaxfee" id="cpkobpmaxfee" value="3000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Darmowe nadpłaty PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba darmowych nadpłat rocznie w PKO Bank Polski BP. Zwykle 2-4 nadpłaty bez prowizji.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpfreeoverpayments" id="cpkobpfreeoverpayments" value="2" class="innormal"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpkobpscenarios" class="cbcontainer">
                                        <input type="checkbox" name="caddpkobpscenarios" id="caddpkobpscenarios" value="1" checked="" onclick="cshpkobpscenarios();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpkobpscenariosdesc">Porównanie scenariuszy PKO BP</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpkobpscenarios" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Scenariusze nadpłat PKO BP</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 1 PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwszy scenariusz nadpłat PKO Bank Polski BP do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpscenario1" id="cpkobpscenario1" value="300" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 2 PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Drugi scenariusz nadpłat PKO Bank Polski BP do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpscenario2" id="cpkobpscenario2" value="600" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 3 PKO BP <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzeci scenariusz nadpłat PKO Bank Polski BP do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpkobpscenario3" id="cpkobpscenario3" value="1000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz nadpłaty PKO BP" onclick="calculatePKOBPOverpaymentsAlt();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                    <input type="button" value="Zapisz PKO BP" onclick="savePKOBPCalculation();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędność PKO BP: &nbsp; 168 450 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('TmFkcMWCYXRhIGtyZWR5dHUgaGlwb3RlY3puZWdvIFBLTyBCUCBrYWxrdWxhdG9y', 0, 'TmFkcMWCYXRhIGtyZWR5dHUgaGlwb3RlY3puZWdvIFBLTyBCUCBrYWxrdWxhdG9y', 'T3N6Y3rEmWRub8WbxIcgUEtPIEJQ', 'MTY4IDQ1MCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez nadpłat PKO BP</b></td>
                                        <td align="right"><b>Z nadpłatami PKO BP</b></td>
                                        <td align="right"><b>Oszczędność PKO BP</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata PKO BP</b></td>
                                        <td align="right"><b>3 400 zł</b></td>
                                        <td align="right"><b>4 000 zł</b></td>
                                        <td align="right"><b>+600 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty PKO BP</td>
                                        <td align="right">18 lat</td>
                                        <td align="right">13 lat 8 miesięcy</td>
                                        <td align="right">4 lata 4 miesiące</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt PKO BP</td>
                                        <td align="right">734 400 zł</td>
                                        <td align="right">652 000 zł</td>
                                        <td align="right">82 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki PKO BP</td>
                                        <td align="right">284 400 zł</td>
                                        <td align="right">202 000 zł</td>
                                        <td align="right">82 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Łączne nadpłaty PKO BP</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">98 400 zł</td>
                                        <td align="right">-98 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Netto oszczędność PKO BP</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                        <td align="right">168 450 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza nadpłat PKO BP:</h3>
                                            <p><strong>Efektywność PKO BP:</strong> Każda złotówka nadpłaty oszczędza 1,71 zł odsetek</p>
                                            <p><strong>ROI PKO BP:</strong> 171% zwrotu z inwestycji w nadpłaty</p>
                                            <p><strong>Skrócenie PKO BP:</strong> 4 lata 4 miesiące wcześniejsze zakończenie</p>
                                            <p><strong>Profil klienta:</strong> Standardowy klient PKO Bank Polski BP</p>
                                            <p><strong>Rekomendacja PKO BP:</strong> ✓ Nadpłaty bardzo opłacalne w PKO BP</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie scenariuszy nadpłat PKO BP</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Nadpłaty PKO BP</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">100K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">150K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">200K zł</text>
                    
                    <!-- 0 zł -->
                    <rect x="50" y="145" width="35" height="0" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">0 zł</text>
                    <text x="67" y="140" class="mcllabelx" style="fill:#000;">0 zł</text>
                    
                    <!-- 300 zł -->
                    <rect x="95" y="125" width="35" height="20" fill="#f39c12" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">300 zł</text>
                    <text x="112" y="120" class="mcllabelx" style="fill:#000;">118K zł</text>
                    
                    <!-- 600 zł -->
                    <rect x="140" y="95" width="35" height="50" fill="#27ae60" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">600 zł</text>
                    <text x="157" y="90" class="mcllabelx" style="fill:#000;">168K zł</text>
                    
                    <!-- 1000 zł -->
                    <rect x="185" y="75" width="35" height="70" fill="#2980b9" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">1000 zł</text>
                    <text x="202" y="70" class="mcllabelx" style="fill:#000;">205K zł</text>
                    
                    <!-- 1500 zł -->
                    <rect x="230" y="65" width="35" height="80" fill="#8e44ad" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">1500 zł</text>
                    <text x="247" y="60" class="mcllabelx" style="fill:#000;">225K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Scenariusze nadpłat PKO BP &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat PKO BP</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Nadpłata PKO BP</th>
                            <th>Oszczędność</th>
                            <th>Skrócenie</th>
                            <th>ROI PKO BP</th>
                            <th>Efektywność</th>
                        </tr>
                        <tr>
                            <td>0 zł</td>
                            <td>0 zł</td>
                            <td>0 miesięcy</td>
                            <td>0%</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>300 zł</td>
                            <td>118 200 zł</td>
                            <td>32 miesiące</td>
                            <td>158%</td>
                            <td>1.58 zł/zł</td>
                        </tr>
                        <tr>
                            <td>600 zł</td>
                            <td>168 450 zł</td>
                            <td>52 miesiące</td>
                            <td>171%</td>
                            <td>1.71 zł/zł</td>
                        </tr>
                        <tr>
                            <td>1000 zł</td>
                            <td>205 600 zł</td>
                            <td>68 miesięcy</td>
                            <td>185%</td>
                            <td>1.85 zł/zł</td>
                        </tr>
                        <tr>
                            <td>1500 zł</td>
                            <td>225 800 zł</td>
                            <td>78 miesięcy</td>
                            <td>192%</td>
                            <td>1.92 zł/zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Nadpłata kredytu hipotecznego PKO BP kalkulator - największy bank w Polsce 2025</h2>
            <p>PKO Bank Polski BP oferuje elastyczne możliwości nadpłat kredytu hipotecznego z konkurencyjnymi warunkami. Nasz kalkulator nadpłaty kredytu hipotecznego PKO BP pomoże Ci precyzyjnie obliczyć oszczędności z nadpłat, sprawdzić opłacalność różnych strategii i zoptymalizować spłatę kredytu mieszkaniowego w PKO Bank Polski BP.</p>

            <h3>Typy nadpłat dostępne w PKO Bank Polski BP:</h3>
            <div style="margin: 15px 0;">
                <h4>Miesięczna nadpłata PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Stała dodatkowa kwota każdego miesiąca</li>
                    <li><strong>Ustawienie:</strong> Bankowość internetowa iPKO lub oddział PKO BP</li>
                    <li><strong>Elastyczność:</strong> Możliwość zmiany kwoty w każdej chwili</li>
                    <li><strong>Efektywność:</strong> Najwyższa - każda złotówka oszczędza 1.71 zł odsetek</li>
                    <li><strong>Zalety PKO BP:</strong> Automatyzacja, systematyczność, maksymalne oszczędności</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów z regularnym dochodem PKO BP</li>
                </ul>

                <h4>Roczna nadpłata PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Jedna większa nadpłata raz w roku</li>
                    <li><strong>Źródła:</strong> Premia roczna, zwrot podatku, 13. pensja</li>
                    <li><strong>Elastyczność:</strong> Dowolny termin w roku</li>
                    <li><strong>Efektywność:</strong> Dobra - znaczące oszczędności przy niskim wysiłku</li>
                    <li><strong>Zalety PKO BP:</strong> Nie obciąża miesięcznego budżetu</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów z nieregularnymi dochodami</li>
                </ul>

                <h4>Kwartalna nadpłata PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Nadpłata co 3 miesiące</li>
                    <li><strong>Planowanie:</strong> Dostosowanie do cyklu dochodów</li>
                    <li><strong>Elastyczność:</strong> Kompromis między częstotliwością a wygodą</li>
                    <li><strong>Efektywność:</strong> Bardzo dobra - regularne zmniejszanie odsetek</li>
                    <li><strong>Zalety PKO BP:</strong> Łatwiejsze planowanie niż miesięczne</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów z sezonowymi dochodami</li>
                </ul>

                <h4>Jednorazowa nadpłata PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Duża jednorazowa wpłata</li>
                    <li><strong>Źródła:</strong> Sprzedaż nieruchomości, spadek, inwestycje</li>
                    <li><strong>Elastyczność:</strong> Dowolny moment i kwota</li>
                    <li><strong>Efektywność:</strong> Bardzo wysoka przy dużych kwotach</li>
                    <li><strong>Zalety PKO BP:</strong> Znaczące skrócenie okresu spłaty</li>
                    <li><strong>Rekomendacja:</strong> Przy otrzymaniu większej sumy pieniędzy</li>
                </ul>

                <h4>Nadpłaty mieszane PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Kombinacja miesięcznych i rocznych nadpłat</li>
                    <li><strong>Strategia:</strong> Mniejsze kwoty miesięcznie + większe rocznie</li>
                    <li><strong>Elastyczność:</strong> Maksymalna adaptacja do możliwości</li>
                    <li><strong>Efektywność:</strong> Najwyższa - optymalne wykorzystanie środków</li>
                    <li><strong>Zalety PKO BP:</strong> Najlepszy stosunek oszczędności do wysiłku</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów chcących maksymalizować oszczędności</li>
                </ul>
            </div>

            <h3>Efekty nadpłat w PKO Bank Polski BP:</h3>
            <div style="margin: 15px 0;">
                <h4>Skrócenie okresu spłaty PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Rata pozostaje bez zmian, skraca się czas spłaty</li>
                    <li><strong>Korzyści:</strong> Maksymalne oszczędności na odsetkach PKO BP</li>
                    <li><strong>Przykład:</strong> 600 zł miesięcznie skraca okres o 4 lata 4 miesiące</li>
                    <li><strong>Oszczędność:</strong> 168 450 zł przy 18-letnim kredycie PKO BP</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów chcących szybko spłacić kredyt</li>
                </ul>

                <h4>Zmniejszenie miesięcznej raty PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Okres pozostaje bez zmian, maleje rata</li>
                    <li><strong>Korzyści:</strong> Poprawa płynności finansowej PKO BP</li>
                    <li><strong>Przykład:</strong> 600 zł nadpłaty zmniejsza ratę o ~180 zł</li>
                    <li><strong>Oszczędność:</strong> Mniejsza niż przy skróceniu okresu</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów potrzebujących niższych rat</li>
                </ul>

                <h4>Efekt mieszany PKO BP:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Częściowe skrócenie + częściowe zmniejszenie raty</li>
                    <li><strong>Korzyści:</strong> Kompromis między oszczędnością a płynnością</li>
                    <li><strong>Elastyczność:</strong> Możliwość dostosowania proporcji</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów chcących zbalansować korzyści</li>
                </ul>
            </div>

            <h3>Opłaty za nadpłaty w PKO Bank Polski BP:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Typ nadpłaty PKO BP</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Prowizja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Maksymalna opłata</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Darmowe nadpłaty</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Miesięczna automatyczna</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bez limitu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Jednorazowa do 20% salda</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 rocznie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Jednorazowa powyżej 20%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Całkowita spłata</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">-</td>
                    </tr>
                </table>
            </div>

            <h3>Strategia nadpłat w PKO Bank Polski BP:</h3>
            <ul>
                <li><strong>Analiza budżetu:</strong> Określ stałą kwotę na nadpłaty PKO BP</li>
                <li><strong>Wybór typu:</strong> Miesięczne dla maksymalnych oszczędności</li>
                <li><strong>Automatyzacja:</strong> Ustaw stałe zlecenie w bankowości iPKO</li>
                <li><strong>Monitorowanie:</strong> Sprawdzaj postępy w aplikacji PKO BP</li>
                <li><strong>Optymalizacja:</strong> Zwiększaj nadpłaty wraz ze wzrostem dochodów</li>
                <li><strong>Elastyczność:</strong> Wykorzystuj premie na dodatkowe nadpłaty</li>
                <li><strong>Planowanie:</strong> Uwzględnij nadpłaty w budżecie domowym</li>
            </ul>

            <h3>Porównanie z innymi inwestycjami:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Inwestycja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Zwrot</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Ryzyko</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Płynność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Nadpłaty PKO BP</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>7.05% (gwarantowane)</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Brak</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Niska</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Lokaty PKO BP</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Brak</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średnia</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Obligacje skarbowe</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo niskie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średnia</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Fundusze inwestycyjne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8-12%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysokie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysoka</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Akcje</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">10-15%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo wysokie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wysoka</td>
                    </tr>
                </table>
            </div>

            <h3>Narzędzia PKO BP do zarządzania nadpłatami:</h3>
            <ul>
                <li><strong>Bankowość iPKO:</strong> Ustawienie automatycznych nadpłat online</li>
                <li><strong>Aplikacja mobilna:</strong> Monitorowanie postępów na smartfonie</li>
                <li><strong>Kalkulator PKO BP:</strong> Symulacja różnych scenariuszy nadpłat</li>
                <li><strong>Harmonogram spłat:</strong> Szczegółowy plan spłaty z nadpłatami</li>
                <li><strong>Powiadomienia SMS:</strong> Informacje o realizacji nadpłat</li>
                <li><strong>Raporty miesięczne:</strong> Podsumowanie oszczędności PKO BP</li>
                <li><strong>Doradca kredytowy:</strong> Konsultacje w oddziałach PKO BP</li>
            </ul>

            <h3>Porady ekspertów PKO BP dotyczące nadpłat:</h3>
            <ul>
                <li>Rozpocznij od małych kwot i stopniowo zwiększaj nadpłaty PKO BP</li>
                <li>Wykorzystaj automatyzację - ustaw stałe zlecenie w iPKO</li>
                <li>Monitoruj regularnie postępy w aplikacji PKO BP</li>
                <li>Wykorzystuj premie i zwroty na dodatkowe nadpłaty</li>
                <li>Sprawdzaj limity darmowych nadpłat w PKO BP</li>
                <li>Rozważ efekt mieszany przy zmieniającej się sytuacji finansowej</li>
                <li>Konsultuj strategię z doradcą kredytowym PKO BP</li>
                <li>Pamiętaj o zachowaniu rezerwy finansowej na nieprzewidziane wydatki</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego-pko-bp.html">PKO BP kalkulator kredytu</a>
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Szybkie ustawienia PKO BP</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Ustaw nadpłatę PKO BP:</strong><br>
                        <a href="#" onclick="return setPKOBPOverpayment(300);">300 zł miesięcznie</a><br>
                        <a href="#" onclick="return setPKOBPOverpayment(600);">600 zł miesięcznie</a><br>
                        <a href="#" onclick="return setPKOBPOverpayment(1000);">1000 zł miesięcznie</a><br>
                        <a href="#" onclick="return setPKOBPOverpayment(1500);">1500 zł miesięcznie</a><br>
                        <a href="#" onclick="return setPKOBPYearlyOverpayment(12000);">12 000 zł rocznie</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Nadpłata kredytu hipotecznego PKO BP kalkulator - oblicz oszczędności z nadpłat kredytu mieszkaniowego w PKO Bank Polski BP.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePKOBPOverpaymentType() {
    var type = document.getElementById('cpkobpoverpaymenttype').value;
    var monthly = document.getElementById('monthlypkobpoverpayment');
    var yearly = document.getElementById('yearlypkobpoverpayment');
    var quarterly = document.getElementById('quarterlypkobpoverpayment');
    var onetime = document.getElementById('onetimepkobpoverpayment');
    var mixed = document.getElementById('mixedpkobpoverpayment');
    
    monthly.style.display = (type === 'monthly_pko_bp') ? 'table-row' : 'none';
    yearly.style.display = (type === 'yearly_pko_bp') ? 'table-row' : 'none';
    quarterly.style.display = (type === 'quarterly_pko_bp') ? 'table-row' : 'none';
    onetime.style.display = (type === 'onetime_pko_bp') ? 'table-row' : 'none';
    mixed.style.display = (type === 'mixed_pko_bp') ? 'table-row' : 'none';
}

function cshpkobpfees() {
    var checkbox = document.getElementById('caddpkobpfees');
    var feesDiv = document.getElementById('cpkobpfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshpkobpscenarios() {
    var checkbox = document.getElementById('caddpkobpscenarios');
    var scenariosDiv = document.getElementById('cpkobpscenarios');
    if (checkbox.checked) {
        scenariosDiv.style.display = 'block';
    } else {
        scenariosDiv.style.display = 'none';
    }
}

function setPKOBPOverpayment(amount) {
    document.getElementById('cpkobpoverpaymenttype').value = 'monthly_pko_bp';
    document.getElementById('cpkobpmonthlyoverpayment').value = amount;
    updatePKOBPOverpaymentType();
    return false;
}

function setPKOBPYearlyOverpayment(amount) {
    document.getElementById('cpkobpoverpaymenttype').value = 'yearly_pko_bp';
    document.getElementById('cpkobpyearlyoverpayment').value = amount;
    updatePKOBPOverpaymentType();
    return false;
}

function savePKOBPCalculation() {
    alert('Wyniki kalkulatora nadpłat PKO BP zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram spłat PKO BP &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Scenariusze nadpłat PKO BP</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Scenariusze nadpłat PKO BP &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat PKO BP</a>';
    }
    return false;
}
</script>

<script>
function calculatePKOBPOverpaymentsAlt() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 获取PKO BP nadpłaty信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;
    var overpaymentType = document.getElementById('coverpaymenttype').value;
    var pkoBPProfile = document.getElementById('cpkobpprofile').value;

    // 获取PKO BP高级选项
    var overpaymentInc = parseFloat(document.getElementById('coverpaymentinc').value) || 0;
    var pkoBPBonus = parseFloat(document.getElementById('cpkobpbonus').value) || 0;
    var pkoBPFee = parseFloat(document.getElementById('cpkobpfee').value) || 0;
    var pkoBPLimit = parseFloat(document.getElementById('cpkobplimit').value) || 0;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算没有nadpłaty的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;

    // PKO BP特殊nadpłaty条件
    var pkoBPBenefits = getPKOBPOverpaymentBenefitsAlt(pkoBPProfile);
    var effectiveOverpaymentFee = pkoBPBenefits.freeOverpayments ? 0 : pkoBPFee;
    var maxAnnualOverpayment = pkoBPBenefits.higherLimit ? pkoBPLimit * 1.5 : pkoBPLimit;

    // 计算有nadpłaty的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;
    var totalFees = 0;
    var currentOverpayment = overpaymentAmount;
    var annualOverpayments = 0;

    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;

        // 重置年度nadpłaty计数器
        if (monthsPaid % 12 === 0) {
            annualOverpayments = 0;
        }

        // 添加nadpłaty
        var thisMonthOverpayment = 0;
        if (overpaymentFreq === 'monthly' ||
           (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
           (overpaymentFreq === 'once' && monthsPaid === 0)) {

            // 检查PKO BP年度限制
            if (annualOverpayments + currentOverpayment <= maxAnnualOverpayment) {
                thisMonthOverpayment = currentOverpayment;

                // 应用PKO BP bonus
                if (pkoBPBenefits.bonusOverpayments) {
                    thisMonthOverpayment *= (1 + pkoBPBonus / 100);
                }

                // 计算费用
                var fee = thisMonthOverpayment * (effectiveOverpaymentFee / 100);
                totalFees += fee;
                annualOverpayments += thisMonthOverpayment;

                // 年度增长
                if (overpaymentFreq === 'monthly' && monthsPaid % 12 === 11) {
                    currentOverpayment *= (1 + overpaymentInc / 100);
                }
            }
        }

        // 更新余额
        remainingBalance -= (monthlyPrincipal + thisMonthOverpayment);
        totalPaid += monthlyPayment + thisMonthOverpayment + fee;
        totalOverpayments += thisMonthOverpayment;
        monthsPaid++;

        if (remainingBalance <= 0) break;
    }

    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments - totalFees);

    // 计算PKO BP效率
    var efficiency = totalOverpayments > 0 ? (moneySaved / totalOverpayments) * 100 : 0;
    var pkoBPAdvantage = pkoBPBenefits.freeOverpayments ? 'Bezpłatne nadpłaty PKO BP' : 'Standardowe opłaty PKO BP';

    // 更新显示
    updatePKOBPOverpaymentsAltResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                     timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                     totalFees, efficiency, loanTerm, pkoBPProfile, pkoBPAdvantage);
}

function getPKOBPOverpaymentBenefitsAlt(profile) {
    var benefits = {
        freeOverpayments: false,
        bonusOverpayments: false,
        higherLimit: false,
        flexibleSchedule: false
    };

    // PKO BP Premium和wyższe korzyści
    if (profile === 'premium' || profile === 'private' || profile === 'employee') {
        benefits.freeOverpayments = true;
        benefits.higherLimit = true;
        benefits.flexibleSchedule = true;
    }

    // Employee specjalne korzyści
    if (profile === 'employee') {
        benefits.bonusOverpayments = true;
    }

    return benefits;
}

function updatePKOBPOverpaymentsAltResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                          timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                          totalFees, efficiency, loanTerm, pkoBPProfile, pkoBPAdvantage) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Nadpłaty PKO BP: &nbsp; ' + formatNumber(moneySaved) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły nadpłat PKO BP
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var monthlyAfterPayoff = monthlyPayment + (totalOverpayments/monthsPaid);
        detailsSection.innerHTML =
            '<h3>Szczegóły nadpłat w PKO Bank Polski:</h3>' +
            '<p><strong>Efektywność nadpłat PKO BP:</strong> ' + efficiency.toFixed(0) + '% (każda złotówka nadpłaty oszczędza ' + (efficiency/100).toFixed(2) + ' zł odsetek)</p>' +
            '<p><strong>Status klienta PKO BP:</strong> ' + getPKOBPOverpaymentStatusAlt(pkoBPProfile) + '</p>' +
            '<p><strong>Korzyści PKO BP:</strong> ' + pkoBPAdvantage + '</p>' +
            '<p><strong>Skrócenie okresu:</strong> ' + formatTime(timeSaved) + ' (' + Math.round((timeSaved/(loanTerm*12))*100) + '% pierwotnego okresu)</p>' +
            '<p><strong>Redukcja odsetek:</strong> ' + formatNumber(interestSaved) + ' zł (' + Math.round((interestSaved/(totalWithoutOverpayment - parseFloat(document.getElementById('cloanamount').value)))*100) + '% pierwotnych odsetek)</p>' +
            '<p><strong>Miesięczne oszczędności:</strong> ' + formatNumber(monthlyAfterPayoff) + ' zł (po spłacie kredytu)</p>' +
            '<p><strong>Rekomendacja PKO BP:</strong> ' + getPKOBPOverpaymentRecommendationAlt(efficiency, pkoBPProfile) + '</p>';
    }
}

function getPKOBPOverpaymentStatusAlt(profile) {
    switch(profile) {
        case 'premium': return 'Klient PKO BP Premium (bezpłatne nadpłaty)';
        case 'private': return 'PKO BP Private Banking (bezpłatne nadpłaty + wyższe limity)';
        case 'employee': return 'Pracownik PKO BP (bezpłatne nadpłaty + bonus)';
        case 'existing': return 'Obecny klient PKO BP (preferencyjne warunki)';
        default: return 'Nowy klient PKO Bank Polski';
    }
}

function getPKOBPOverpaymentRecommendationAlt(efficiency, profile) {
    if (efficiency > 180) {
        return 'Nadpłaty bardzo opłacalne w PKO BP przy tym oprocentowaniu';
    } else if (efficiency > 150) {
        return 'Nadpłaty opłacalne w PKO BP, rozważ zwiększenie kwoty';
    } else if (efficiency > 120) {
        return 'Nadpłaty umiarkowanie opłacalne w PKO BP';
    } else {
        return 'Rozważ inne formy inwestycji zamiast nadpłat w PKO BP';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.05';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('coverpayment')) document.getElementById('coverpayment').value = '500';
    if (document.getElementById('coverpaymentfreq')) document.getElementById('coverpaymentfreq').value = 'monthly';
    if (document.getElementById('coverpaymenttype')) document.getElementById('coverpaymenttype').value = 'reduce_time';
    if (document.getElementById('cpkobpprofile')) document.getElementById('cpkobpprofile').value = 'new';

    calculatePKOBPOverpaymentsAlt();
}

function saveCalResult() {
    alert('Wyniki kalkulatora nadpłat PKO BP zostały zapisane!');
    return false;
}

function savePKOBPCalculation() {
    alert('Obliczenia nadpłat PKO BP zostały zapisane w systemie banku!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePKOBPOverpaymentsAlt();
};
</script>

</body>
</html>
