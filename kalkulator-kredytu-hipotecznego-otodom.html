<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Kalkulator kredytu hipotecznego Otodom</title>
	<meta name="description" content="Kalkulator kredytu hipotecznego Otodom - oblicz ratę kredytu mieszkaniowego dla nieruchomości z Otodom, sprawdź warunki kredytowe i zdolność kredytową 2025.">
	<meta name="keywords" content="kalkulator kredytu hipotecznego otodom, otodom kredyt hipoteczny, rata kredytu otodom, nieruchomości otodom kredyt">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulator-kredytu-hipotecznego-otodom.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego otodom</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Kalkulator kredytu hipotecznego Otodom</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt dla nieruchomości Otodom"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/kalkulator-kredytu-hipotecznego-otodom.html">
<table align="center">
<tbody><tr><td align="right">Cena nieruchomości Otodom</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="650000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny dla nieruchomości z Otodom. Minimum 10% wartości nieruchomości. Wyższy wkład własny = lepsza marża kredytowa.', '');" onmouseout="tooltip.hide();"></td><td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu dla nieruchomości Otodom. Zazwyczaj 15-30 lat. Dłuższy okres = niższa rata miesięczna.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego. WIBOR 3M + marża banku dla nieruchomości Otodom.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.35" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Data rozpoczęcia</td><td align="left" colspan="2"><select name="cstartmonth" id="cstartmonth"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char"></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Uwzględnij koszty Otodom poniżej</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Koszty nieruchomości Otodom</td></tr>
	<tr><td align="right">Prowizja przygotowawcza <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja banku za przygotowanie kredytu na nieruchomość Otodom. Zazwyczaj 1-3% kwoty kredytu.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" onchange="cunitchange('cprovision', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obowiązkowe ubezpieczenie nieruchomości z Otodom od ognia i innych zdarzeń losowych.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1400" class="innormal  indollar"></td><td><select name="chomeinsunit" onchange="cunitchange('chomeins', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu na nieruchomość Otodom. 0.25-0.45% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.35" class="innormal inpct"></td><td><select name="cpmiunit" onchange="cunitchange('cpmi', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Rachunek kredytowy <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego. 0-25 zł miesięcznie.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="22" class="innormal  indollar"></td><td><select name="choaunit" onchange="cunitchange('choa', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Koszty transakcji Otodom <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: wycena nieruchomości Otodom (1200-2000 zł), koszty notarialne, wpis hipoteki, prowizja agenta.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="6500" class="innormal  indollar"></td><td><select name="cothercostunit" onchange="cunitchange('cothercost', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Otodom</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Typy nieruchomości Otodom</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Typ nieruchomości</td><td><select name="cpropertytype" id="cpropertytype" onchange="updateOtodomPropertyType();"><option value="apartment" selected="">Mieszkanie Otodom</option><option value="house">Dom Otodom</option><option value="plot">Działka Otodom</option><option value="commercial">Komercyjna Otodom</option><option value="investment">Inwestycyjna Otodom</option></select></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Lokalizacja Otodom</td><td><select name="clocation" id="clocation" onchange="updateOtodomLocation();"><option value="warsaw" selected="">Warszawa</option><option value="krakow">Kraków</option><option value="gdansk">Gdańsk</option><option value="wroclaw">Wrocław</option><option value="poznan">Poznań</option><option value="other">Inne miasto</option></select></td></tr>
	<tr><td colspan="3" style="padding:10px 0px 3px 0px;font-weight:bold;text-align:left;">Usługi Otodom</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Prowizja agenta Otodom</td><td><input type="text" name="cagentfee" id="cagentfee" value="3" class="innormal inpct"> %</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wycena nieruchomości</td><td><input type="text" name="cvaluation" id="cvaluation" value="1500" class="innormal indollar"> zł</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Ubezpieczenie tytułu</td><td><input type="text" name="ctitleins" id="ctitleins" value="800" class="innormal indollar"> zł</td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz kredyt Otodom" onclick="calculateOtodomMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Rata Otodom: &nbsp; 4 285 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBPdG9kb20=', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBPdG9kb20=', 'UmF0YSBPdG9kb20=', 'NCAyODUgekw=');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Rocznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu Otodom</b></td><td align="right"><b>4 285 zł</b></td><td align="right"><b>51 420 zł</b></td><td align="right"><b>1 285 500 zł</b></td></tr>
<tr><td>Kwota kredytu Otodom</td><td align="right">-</td><td align="right">-</td><td align="right">520 000 zł</td></tr>
<tr><td>Odsetki kredytu</td><td align="right">-</td><td align="right">-</td><td align="right">765 500 zł</td></tr>
<tr><td>Prowizja przygotowawcza</td><td align="right">-</td><td align="right">-</td><td align="right">10 400 zł</td></tr>
<tr><td>Ubezpieczenie nieruchomości</td><td align="right">117 zł</td><td align="right">1 400 zł</td><td align="right">35 000 zł</td></tr>
<tr><td>Ubezpieczenie spłaty kredytu</td><td align="right">152 zł</td><td align="right">1 820 zł</td><td align="right">45 500 zł</td></tr>
<tr><td>Rachunek kredytowy</td><td align="right">22 zł</td><td align="right">264 zł</td><td align="right">6 600 zł</td></tr>
<tr><td>Koszty transakcji Otodom</td><td align="right">-</td><td align="right">-</td><td align="right">6 500 zł</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Szczegóły kredytu Otodom:</h3>
<p><strong>RRSO Otodom:</strong> 8.65% (z wszystkimi kosztami Otodom)</p>
<p><strong>Typ nieruchomości:</strong> Mieszkanie Otodom (standardowe warunki)</p>
<p><strong>Lokalizacja:</strong> Warszawa (najwyższe ceny na rynku)</p>
<p><strong>Całkowity koszt Otodom:</strong> 1 389 500 zł</p>
<p><strong>Przepłacone odsetki:</strong> 869 500 zł (167% kwoty kredytu)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Analiza nieruchomości Otodom</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Typy nieruchomości Otodom</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">3500 zł</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">4000 zł</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">4500 zł</text>

<!-- Mieszkanie -->
<rect x="50" y="95" width="35" height="50" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Mieszkanie</text>
<text x="67" y="90" class="mcllabelx" style="fill:#000;">4285 zł</text>

<!-- Dom -->
<rect x="95" y="85" width="35" height="60" fill="#2ecc71" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Dom</text>
<text x="112" y="80" class="mcllabelx" style="fill:#000;">4485 zł</text>

<!-- Działka -->
<rect x="140" y="125" width="35" height="20" fill="#f39c12" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Działka</text>
<text x="157" y="120" class="mcllabelx" style="fill:#000;">3685 zł</text>

<!-- Komercyjna -->
<rect x="185" y="75" width="35" height="70" fill="#9b59b6" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Komercyjna</text>
<text x="202" y="70" class="mcllabelx" style="fill:#000;">4685 zł</text>

<!-- Inwestycyjna -->
<rect x="230" y="105" width="35" height="40" fill="#e74c3c" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Inwestycyjna</text>
<text x="247" y="100" class="mcllabelx" style="fill:#000;">4385 zł</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Typy nieruchomości Otodom &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Lokalizacje Otodom</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Miasto Otodom</th>
<th>Średnia cena m²</th>
<th>Rata kredytu</th>
<th>Wkład własny</th>
<th>Różnica</th>
</tr>
<tr>
<td>Warszawa</td>
<td>12 500 zł/m²</td>
<td>4 285 zł</td>
<td>130 000 zł</td>
<td>-</td>
</tr>
<tr>
<td>Kraków</td>
<td>10 800 zł/m²</td>
<td>3 685 zł</td>
<td>112 000 zł</td>
<td>-600 zł</td>
</tr>
<tr>
<td>Gdańsk</td>
<td>9 200 zł/m²</td>
<td>3 185 zł</td>
<td>96 000 zł</td>
<td>-1 100 zł</td>
</tr>
<tr>
<td>Wrocław</td>
<td>8 900 zł/m²</td>
<td>3 085 zł</td>
<td>92 000 zł</td>
<td>-1 200 zł</td>
</tr>
<tr>
<td>Poznań</td>
<td>8 600 zł/m²</td>
<td>2 985 zł</td>
<td>89 000 zł</td>
<td>-1 300 zł</td>
</tr>
<tr>
<td>Inne miasto</td>
<td>6 500 zł/m²</td>
<td>2 285 zł</td>
<td>68 000 zł</td>
<td>-2 000 zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego Otodom - nieruchomości z największego portalu 2025</h2>
            <p>Kalkulator kredytu hipotecznego Otodom to specjalistyczne narzędzie do obliczania rat kredytu mieszkaniowego dla nieruchomości z największego polskiego portalu nieruchomości. Nasz kalkulator uwzględnia specyfikę transakcji Otodom, prowizje agentów, koszty wyceny i wszystkie dodatkowe opłaty związane z zakupem nieruchomości przez portal Otodom.</p>

            <h3>Specyfika kredytów dla nieruchomości Otodom:</h3>
            <div style="margin: 15px 0;">
                <h4>Typy nieruchomości Otodom:</h4>
                <ul>
                    <li><strong>Mieszkania Otodom:</strong> 7.35% oprocentowanie (standardowe warunki)</li>
                    <li><strong>Domy Otodom:</strong> 7.50% oprocentowanie (wyższe koszty wyceny)</li>
                    <li><strong>Działki Otodom:</strong> 7.10% oprocentowanie (specjalne warunki)</li>
                    <li><strong>Nieruchomości komercyjne:</strong> 7.70% oprocentowanie (biznesowe warunki)</li>
                    <li><strong>Nieruchomości inwestycyjne:</strong> 7.45% oprocentowanie (preferencyjne stawki)</li>
                </ul>

                <h4>Lokalizacje Otodom i ich wpływ na kredyt:</h4>
                <ul>
                    <li><strong>Warszawa:</strong> 650 000 zł średnia cena (12 500 zł/m²)</li>
                    <li><strong>Kraków:</strong> 560 000 zł średnia cena (10 800 zł/m²)</li>
                    <li><strong>Gdańsk:</strong> 480 000 zł średnia cena (9 200 zł/m²)</li>
                    <li><strong>Wrocław:</strong> 460 000 zł średnia cena (8 900 zł/m²)</li>
                    <li><strong>Poznań:</strong> 445 000 zł średnia cena (8 600 zł/m²)</li>
                    <li><strong>Inne miasta:</strong> 350 000 zł średnia cena (6 500 zł/m²)</li>
                </ul>

                <h4>Dodatkowe koszty Otodom:</h4>
                <ul>
                    <li><strong>Prowizja agenta Otodom:</strong> 2-4% ceny nieruchomości</li>
                    <li><strong>Wycena nieruchomości:</strong> 1 200 - 2 000 zł (wyższa dla domów)</li>
                    <li><strong>Ubezpieczenie tytułu:</strong> 600 - 1 200 zł (opcjonalne)</li>
                    <li><strong>Koszty notarialne:</strong> 2 500 - 5 000 zł (zależne od wartości)</li>
                    <li><strong>Wpis hipoteki:</strong> 200 - 500 zł (opłata sądowa)</li>
                    <li><strong>Opłaty administracyjne:</strong> 300 - 800 zł</li>
                </ul>
            </div>

            <h3>Zalety korzystania z Otodom przy kredycie:</h3>
            <ul>
                <li><strong>Największy wybór:</strong> Ponad 500 000 ofert nieruchomości</li>
                <li><strong>Zweryfikowane oferty:</strong> Sprawdzone przez zespół Otodom</li>
                <li><strong>Profesjonalni agenci:</strong> Licencjonowani pośrednicy nieruchomości</li>
                <li><strong>Kompleksowa obsługa:</strong> Od wyszukania do finalizacji transakcji</li>
                <li><strong>Narzędzia analityczne:</strong> Raporty rynkowe i analizy cen</li>
                <li><strong>Wsparcie prawne:</strong> Pomoc w przygotowaniu dokumentów</li>
                <li><strong>Partnerstwa bankowe:</strong> Współpraca z największymi bankami</li>
                <li><strong>Ubezpieczenia:</strong> Kompleksowe pakiety ubezpieczeniowe</li>
            </ul>

            <h3>Proces kredytowy dla nieruchomości Otodom:</h3>
            <div style="margin: 15px 0;">
                <h4>Etap 1: Wyszukiwanie nieruchomości</h4>
                <ul>
                    <li>Przeglądanie ofert na portalu Otodom</li>
                    <li>Kontakt z agentem nieruchomości</li>
                    <li>Oględziny nieruchomości</li>
                    <li>Wstępna negocjacja ceny</li>
                </ul>

                <h4>Etap 2: Przygotowanie kredytu</h4>
                <ul>
                    <li>Obliczenie zdolności kredytowej</li>
                    <li>Wybór banku i produktu kredytowego</li>
                    <li>Przygotowanie dokumentów</li>
                    <li>Złożenie wniosku kredytowego</li>
                </ul>

                <h4>Etap 3: Wycena i weryfikacja</h4>
                <ul>
                    <li>Zlecenie wyceny nieruchomości</li>
                    <li>Sprawdzenie stanu prawnego</li>
                    <li>Weryfikacja dokumentów przez bank</li>
                    <li>Decyzja kredytowa banku</li>
                </ul>

                <h4>Etap 4: Finalizacja transakcji</h4>
                <ul>
                    <li>Podpisanie umowy kredytowej</li>
                    <li>Akt notarialny kupna-sprzedaży</li>
                    <li>Wpis hipoteki w księdze wieczystej</li>
                    <li>Uruchomienie środków kredytowych</li>
                </ul>
            </div>

            <h3>Porównanie kosztów według lokalizacji:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Miasto</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Średnia cena</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wkład własny 20%</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Rata kredytu</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Koszty dodatkowe</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Warszawa</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">650 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">130 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">4 285 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">26 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Kraków</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">560 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">112 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 685 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">22 400 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Gdańsk</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">480 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">96 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 185 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">19 200 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Wrocław</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">460 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">92 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 085 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">18 400 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Poznań</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">445 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">89 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 985 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">17 800 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Inne miasta</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">350 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">70 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2 285 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">14 000 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Wskazówki dla kupujących przez Otodom:</h3>
            <ul>
                <li><strong>Sprawdź historię cen:</strong> Używaj narzędzi analitycznych Otodom</li>
                <li><strong>Negocjuj prowizję:</strong> Prowizja agenta jest negocjowalna</li>
                <li><strong>Porównaj oferty:</strong> Sprawdzaj podobne nieruchomości w okolicy</li>
                <li><strong>Weryfikuj dokumenty:</strong> Sprawdzaj stan prawny nieruchomości</li>
                <li><strong>Planuj koszty dodatkowe:</strong> Uwzględniaj wszystkie opłaty</li>
                <li><strong>Korzystaj z ekspertów:</strong> Konsultuj się z doradcami Otodom</li>
                <li><strong>Sprawdzaj otoczenie:</strong> Analizuj infrastrukturę i komunikację</li>
                <li><strong>Planuj przyszłość:</strong> Rozważaj potencjał inwestycyjny</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Nieruchomości Otodom</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Wybierz typ Otodom:</strong><br>
<a href="#" onclick="return setOtodomProperty('apartment');">Mieszkanie Otodom</a><br>
<a href="#" onclick="return setOtodomProperty('house');">Dom Otodom</a><br>
<a href="#" onclick="return setOtodomProperty('plot');">Działka Otodom</a><br>
<a href="#" onclick="return setOtodomProperty('commercial');">Komercyjna Otodom</a><br>
<a href="#" onclick="return setOtodomProperty('investment');">Inwestycyjna Otodom</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Kalkulator kredytu hipotecznego Otodom - oblicz ratę kredytu mieszkaniowego dla nieruchomości z Otodom.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateOtodomMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
        
        // Otodom特殊费用
        costs.agentFee = calculateCost('cagentfee', 'p', housePrice);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.titleInsurance = parseFloat(document.getElementById('ctitleins').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0, agentFee: 0, valuation: 0, titleInsurance: 0};
    }
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts +
                   costs.agentFee + costs.valuation + costs.titleInsurance;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updateOtodomResults(loanAmount, monthlyPayment, totalInterest, costs, 
                       totalCost, monthlyTotal, rrso, loanTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    if (!valueElement) return 0;
    
    var value = parseFloat(valueElement.value) || 0;
    var unit = unitId;
    
    if (typeof unitId !== 'string') {
        var unitElement = document.getElementById(unitId);
        if (!unitElement) return 0;
        unit = unitElement.value;
    }
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateOtodomResults(loanAmount, monthlyPayment, totalInterest, costs, 
                            totalCost, monthlyTotal, rrso, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    document.querySelector('.h2result').innerHTML = 
        'Rata Otodom: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
        '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    
    // 更新szczegóły kredytu
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var additionalCosts = totalCost - loanAmount - totalInterest;
        detailsSection.innerHTML = 
            '<h3>Szczegóły kredytu Otodom:</h3>' +
            '<p><strong>RRSO Otodom:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi kosztami Otodom)</p>' +
            '<p><strong>Typ nieruchomości:</strong> ' + getOtodomPropertyType() + '</p>' +
            '<p><strong>Lokalizacja:</strong> ' + getOtodomLocation() + '</p>' +
            '<p><strong>Całkowity koszt Otodom:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Przepłacone odsetki:</strong> ' + formatNumber(totalInterest + additionalCosts) + ' zł (' + Math.round(((totalInterest + additionalCosts)/loanAmount)*100) + '% kwoty kredytu)</p>';
    }
}

function getOtodomPropertyType() {
    var type = document.getElementById('cpropertytype').value;
    switch(type) {
        case 'apartment': return 'Mieszkanie Otodom (standardowe warunki)';
        case 'house': return 'Dom Otodom (wyższe koszty)';
        case 'plot': return 'Działka Otodom (specjalne warunki)';
        case 'commercial': return 'Komercyjna Otodom (biznesowe warunki)';
        case 'investment': return 'Inwestycyjna Otodom (preferencyjne stawki)';
        default: return 'Mieszkanie Otodom (standardowe warunki)';
    }
}

function getOtodomLocation() {
    var location = document.getElementById('clocation').value;
    switch(location) {
        case 'warsaw': return 'Warszawa (najwyższe ceny na rynku)';
        case 'krakow': return 'Kraków (wysokie ceny)';
        case 'gdansk': return 'Gdańsk (średnie ceny)';
        case 'wroclaw': return 'Wrocław (średnie ceny)';
        case 'poznan': return 'Poznań (średnie ceny)';
        case 'other': return 'Inne miasto (niższe ceny)';
        default: return 'Warszawa (najwyższe ceny na rynku)';
    }
}

function updateOtodomPropertyType() {
    var type = document.getElementById('cpropertytype').value;
    var rateField = document.getElementById('cinterestrate');
    var baseRate = 7.35;
    
    switch(type) {
        case 'apartment':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'house':
            rateField.value = (baseRate + 0.15).toFixed(2);
            break;
        case 'plot':
            rateField.value = (baseRate - 0.25).toFixed(2);
            break;
        case 'commercial':
            rateField.value = (baseRate + 0.35).toFixed(2);
            break;
        case 'investment':
            rateField.value = (baseRate + 0.10).toFixed(2);
            break;
    }
    calculateOtodomMortgage();
}

function updateOtodomLocation() {
    var location = document.getElementById('clocation').value;
    var priceField = document.getElementById('chouseprice');
    
    switch(location) {
        case 'warsaw':
            priceField.value = '650000';
            break;
        case 'krakow':
            priceField.value = '560000';
            break;
        case 'gdansk':
            priceField.value = '480000';
            break;
        case 'wroclaw':
            priceField.value = '460000';
            break;
        case 'poznan':
            priceField.value = '445000';
            break;
        case 'other':
            priceField.value = '350000';
            break;
    }
    calculateOtodomMortgage();
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij koszty Otodom poniżej';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij koszty Otodom poniżej';
    }
    calculateOtodomMortgage();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Opcje Otodom</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Otodom</a>';
        hiddenField.value = '0';
    }
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
    calculateOtodomMortgage();
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '650000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.35';
    document.getElementById('cstartmonth').value = '8';
    document.getElementById('cstartyear').value = '2025';
    
    document.getElementById('cprovision').value = '2.0';
    document.getElementById('chomeins').value = '1400';
    document.getElementById('cpmi').value = '0.35';
    document.getElementById('choa').value = '22';
    document.getElementById('cothercost').value = '6500';
    
    document.getElementById('cpropertytype').value = 'apartment';
    document.getElementById('clocation').value = 'warsaw';
    
    document.getElementById('caddoptional').checked = true;
    cshtaxcost();
    calculateOtodomMortgage();
}

function setOtodomProperty(type) {
    document.getElementById('cpropertytype').value = type;
    updateOtodomPropertyType();
    return false;
}

function saveCalResult() {
    alert('Wyniki kalkulatora Otodom zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Lokalizacje Otodom &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Typy nieruchomości Otodom</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Typy nieruchomości Otodom &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Lokalizacje Otodom</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateOtodomMortgage();
};
</script>

</body></html>
