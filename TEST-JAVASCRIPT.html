<!DOCTYPE html>
<html>
<head>
<title>Test JavaScript</title>
<style>
body{font-family:Arial;margin:40px;text-align:center;background:#f0f0f0}
button{padding:30px 60px;font-size:24px;background:#dc3545;color:white;border:none;cursor:pointer;border-radius:10px;margin:20px}
button:hover{background:#c82333}
.info{font-size:18px;margin:20px;padding:20px;background:white;border-radius:10px;border:2px solid #dc3545}
</style>
</head>
<body>

<h1>🚨 JAVASCRIPT TEST 🚨</h1>

<div class="info">
<p><strong>点击下面的红色按钮</strong></p>
<p>如果弹出提示框 = JavaScript工作正常</p>
<p>如果没有反应 = JavaScript被禁用</p>
</div>

<button onclick="alert('✅ JavaScript工作正常！现在所有计算器都应该能用了！')">
🔴 点击测试JavaScript
</button>

<div class="info">
<h3>如果按钮没有反应，请启用JavaScript：</h3>
<p><strong>Chrome:</strong> 设置 → 隐私设置和安全性 → 网站设置 → JavaScript → 允许</p>
<p><strong>Firefox:</strong> 地址栏输入 about:config → 搜索 javascript.enabled → 设为 true</p>
<p><strong>Edge:</strong> 设置 → Cookie和网站权限 → JavaScript → 开启</p>
</div>

<div style="margin-top:40px;padding:20px;background:#fff3cd;border-radius:10px">
<h3>📋 测试步骤：</h3>
<p>1. 点击红色按钮测试JavaScript</p>
<p>2. 如果弹出提示框，打开 CALCULATOR-FIXED.html</p>
<p>3. 计算器应该自动显示结果并且按钮可以点击</p>
</div>

</body>
</html>
