<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最终解决方案</title>
</head>
<body>
    <h1>计算器问题最终解决方案</h1>
    
    <h2>步骤1: 基础测试</h2>
    <button onclick="alert('如果你看到这个提示框，JavaScript工作正常')">点击测试JavaScript</button>
    
    <h2>步骤2: 如果上面的按钮不工作</h2>
    <p><strong>问题：JavaScript被禁用</strong></p>
    <p><strong>解决方案：</strong></p>
    <ul>
        <li><strong>Chrome:</strong> 设置 → 隐私设置和安全性 → 网站设置 → JavaScript → 允许</li>
        <li><strong>Firefox:</strong> 地址栏输入 about:config → 搜索 javascript.enabled → 设为 true</li>
        <li><strong>Edge:</strong> 设置 → Cookie和网站权限 → JavaScript → 允许</li>
    </ul>
    
    <h2>步骤3: 如果JavaScript工作但计算器不工作</h2>
    <p><strong>问题：本地文件安全限制</strong></p>
    <p><strong>解决方案：</strong></p>
    <ol>
        <li><strong>使用在线版本：</strong>将文件上传到网站服务器</li>
        <li><strong>使用本地服务器：</strong>
            <ul>
                <li>Python: <code>python -m http.server 8000</code></li>
                <li>Node.js: <code>npx http-server</code></li>
                <li>PHP: <code>php -S localhost:8000</code></li>
            </ul>
        </li>
        <li><strong>Chrome特殊启动：</strong>使用参数 <code>--allow-file-access-from-files</code></li>
    </ol>
    
    <h2>步骤4: 简单计算器测试</h2>
    <p>房价: <input type="number" id="price" value="500000"></p>
    <p>首付%: <input type="number" id="down" value="20"></p>
    <p>年限: <input type="number" id="years" value="25"></p>
    <p>利率%: <input type="number" id="rate" value="7.25"></p>
    <button onclick="calc()">计算</button>
    <p id="result">结果将显示在这里</p>
    
    <h2>步骤5: 如果以上都不工作</h2>
    <p><strong>问题：浏览器环境问题</strong></p>
    <p><strong>解决方案：</strong></p>
    <ul>
        <li>尝试不同的浏览器（Chrome, Firefox, Edge）</li>
        <li>更新浏览器到最新版本</li>
        <li>临时禁用杀毒软件和浏览器扩展</li>
        <li>使用隐私模式/无痕模式</li>
    </ul>
    
    <h2>步骤6: 最终建议</h2>
    <p>如果所有方法都不工作，建议：</p>
    <ol>
        <li><strong>使用在线计算器：</strong>将HTML文件上传到免费网站托管服务</li>
        <li><strong>使用现成工具：</strong>使用银行官方计算器或其他在线工具</li>
        <li><strong>联系技术支持：</strong>可能需要专业的技术支持来解决环境问题</li>
    </ol>

    <script>
        function calc() {
            try {
                var price = parseFloat(document.getElementById('price').value) || 500000;
                var down = parseFloat(document.getElementById('down').value) || 20;
                var years = parseFloat(document.getElementById('years').value) || 25;
                var rate = parseFloat(document.getElementById('rate').value) || 7.25;
                
                var downAmount = price * (down / 100);
                var loanAmount = price - downAmount;
                var monthlyRate = rate / 100 / 12;
                var numPayments = years * 12;
                var monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1);
                
                document.getElementById('result').innerHTML = 
                    '贷款金额: ' + Math.round(loanAmount).toLocaleString() + ' zł<br>' +
                    '月供: ' + Math.round(monthlyPayment).toLocaleString() + ' zł<br>' +
                    '总支付: ' + Math.round(monthlyPayment * numPayments).toLocaleString() + ' zł';
            } catch (error) {
                document.getElementById('result').innerHTML = '计算出错: ' + error.message;
            }
        }
        
        // 页面加载时自动计算
        window.onload = function() {
            calc();
        };
    </script>
</body>
</html>
