<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator raty kredytu hipotecznego - oblicz miesięczną ratę online</title>
    <meta name="description" content="Darmowy kalkulator raty kredytu hipotecznego. Oblicz miesięczną ratę, całkowity koszt kredytu i harmonogram spłat. Porównaj różne warianty kredytu mieszkaniowego.">
    <meta name="keywords" content="kalkulator raty kredytu hipotecznego, rata kredytu, kredyt mieszkaniowy, oprocentowanie, harmonogram spłat">
    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">kalkulatory finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> /
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="kalkulator-raty-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator raty kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator raty kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Wprowadź parametry kredytu i oblicz miesięczną ratę"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-raty-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Kwota kredytu</td>
                                <td align="right"><input type="text" name="cloanamount" id="cloanamount" value="400000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czas spłaty kredytu w latach. Dłuższy okres oznacza niższą miesięczną ratę, ale wyższy całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu. Może być stała przez cały okres lub zmienna.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ raty</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype">
                                        <option value="annuity" selected="">Annuitetowa (stała)</option>
                                        <option value="decreasing">Malejąca</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Data rozpoczęcia</td>
                                <td align="left" colspan="2">
                                    <select name="cstartmonth" id="cstartmonth">
                                        <option value="1">Styczeń</option>
                                        <option value="2">Luty</option>
                                        <option value="3">Marzec</option>
                                        <option value="4">Kwiecień</option>
                                        <option value="5">Maj</option>
                                        <option value="6">Czerwiec</option>
                                        <option value="7">Lipiec</option>
                                        <option value="8" selected="">Sierpień</option>
                                        <option value="9">Wrzesień</option>
                                        <option value="10">Październik</option>
                                        <option value="11">Listopad</option>
                                        <option value="12">Grudzień</option>
                                    </select> 
                                    <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddcosts" class="cbcontainer">
                                        <input type="checkbox" name="caddcosts" id="caddcosts" value="1" checked="" onclick="cshcosts();">
                                        <span class="cbmark"></span>
                                        <b><span id="ccostsdesc">Uwzględnij dodatkowe koszty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ccosts" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Miesięczne koszty</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna składka ubezpieczenia spłaty kredytu. Chroni przed niespłaceniem kredytu w przypadku utraty zdolności do pracy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ccreditinsurance" id="ccreditinsurance" value="150" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna składka ubezpieczenia nieruchomości od ognia i innych zdarzeń losowych. Wymagane przez bank.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpropertyinsurance" id="cpropertyinsurance" value="100" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja bankowa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego i obsługę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankfee" id="cbankfee" value="20" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe miesięczne opłaty związane z kredytem (np. opłaty za przelewy, wyciągi, itp.).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cotherfees" id="cotherfees" value="30" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz ratę" onclick="
var l=parseFloat(document.getElementById('cloanamount').value)||400000;
var r=parseFloat(document.getElementById('cinterestrate').value)||7.25;
var t=parseFloat(document.getElementById('cloanterm').value)||25;
var mr=r/100/12;
var n=t*12;
var m=(mr>0)?l*(mr*Math.pow(1+mr,n))/(Math.pow(1+mr,n)-1):l/n;
alert('Rata kredytu: '+Math.round(m).toLocaleString('pl-PL')+' zł (Kwota kredytu: '+Math.round(l).toLocaleString('pl-PL')+' zł, Łącznie: '+Math.round(m*n).toLocaleString('pl-PL')+' zł)');
">
                                    <input type="button" value="Wyczyść" onclick="
document.getElementById('cloanamount').value='400000';
document.getElementById('cinterestrate').value='7.25';
document.getElementById('cloanterm').value='25';
">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 2 847 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBSYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 0, 'S2Fsa3VsYXRvciBSYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MiA4NDcgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata kredytu</b></td>
                                        <td align="right"><b>2 847 zł</b></td>
                                        <td align="right"><b>854 100 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie kredytu</td>
                                        <td align="right">150 zł</td>
                                        <td align="right">45 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie nieruchomości</td>
                                        <td align="right">100 zł</td>
                                        <td align="right">30 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Prowizja bankowa</td>
                                        <td align="right">20 zł</td>
                                        <td align="right">6 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Inne opłaty</td>
                                        <td align="right">30 zł</td>
                                        <td align="right">9 000 zł</td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowity koszt miesięczny</b></td>
                                        <td align="right">3 147 zł</td>
                                        <td align="right">944 100 zł</td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="padding-top: 15px;">
                                            <table cellpadding="3" width="100%">
                                                <tr bgcolor="#f0f0f0">
                                                    <td><b>Szczegóły kredytu:</b></td>
                                                    <td align="right">&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td>Kwota kredytu</td>
                                                    <td align="right">400 000 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowita kwota do spłaty</td>
                                                    <td align="right">854 100 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowite odsetki</td>
                                                    <td align="right">454 100 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Data zakończenia spłaty</td>
                                                    <td align="right">Sierpień 2050</td>
                                                </tr>
                                                <tr>
                                                    <td>Liczba rat</td>
                                                    <td align="right">300</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <div style="padding: 20px 0;">
            <h2>Jak obliczyć ratę kredytu hipotecznego?</h2>
            <p>Rata kredytu hipotecznego składa się z dwóch głównych elementów: kapitału (spłaty części kwoty kredytu) i odsetek. Wysokość raty zależy od kilku kluczowych czynników.</p>
            
            <h3>Czynniki wpływające na wysokość raty:</h3>
            <ul>
                <li><strong>Kwota kredytu</strong> - im wyższa kwota, tym wyższa rata</li>
                <li><strong>Okres kredytowania</strong> - dłuższy okres = niższa rata, ale wyższe całkowite koszty</li>
                <li><strong>Oprocentowanie</strong> - wyższe oprocentowanie = wyższa rata</li>
                <li><strong>Typ raty</strong> - annuitetowa (stała) vs malejąca</li>
            </ul>
            
            <h3>Rodzaje rat kredytowych:</h3>
            <div style="margin: 15px 0;">
                <h4>Rata annuitetowa (stała):</h4>
                <ul>
                    <li>Stała wysokość raty przez cały okres kredytowania</li>
                    <li>Na początku większa część to odsetki, pod koniec większa część to kapitał</li>
                    <li>Łatwiejsze planowanie budżetu domowego</li>
                    <li>Wyższy całkowity koszt kredytu</li>
                </ul>
                
                <h4>Rata malejąca:</h4>
                <ul>
                    <li>Stała część kapitałowa, malejące odsetki</li>
                    <li>Najwyższa rata na początku, stopniowo maleje</li>
                    <li>Niższy całkowity koszt kredytu</li>
                    <li>Wymaga wyższej zdolności kredytowej na początku</li>
                </ul>
            </div>
            
            <h3>Dodatkowe koszty kredytu hipotecznego:</h3>
            <ul>
                <li><strong>Ubezpieczenie kredytu</strong> - chroni przed niespłaceniem w przypadku problemów</li>
                <li><strong>Ubezpieczenie nieruchomości</strong> - wymagane przez bank</li>
                <li><strong>Prowizja bankowa</strong> - opłata za obsługę kredytu</li>
                <li><strong>Opłaty dodatkowe</strong> - przelewy, wyciągi, inne usługi bankowe</li>
            </ul>
            
            <h3>Porady dotyczące optymalizacji raty:</h3>
            <ul>
                <li>Porównaj oferty różnych banków</li>
                <li>Rozważ wpłatę wyższego wkładu własnego</li>
                <li>Sprawdź możliwość wcześniejszej spłaty bez prowizji</li>
                <li>Monitoruj zmiany stóp procentowych</li>
                <li>Rozważ refinansowanie przy spadku stóp</li>
            </ul>
        </div>
    </div>
    
    <div id="right">
        <div id="othercalc">
            <div id="octitle">Popularne Kalkulatory</div>
            <div id="occontent">
                <a href="/">Kalkulator kredytu hipotecznego</a>
                <a href="kalkulator-zdolnosci-kredytowej.html">Kalkulator zdolności kredytowej</a>
                <a href="kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłat</a>
                <a href="pko-kalkulator-kredytu-hipotecznego.html">PKO Kalkulator</a>
                <a href="ing-kalkulator-kredytu-hipotecznego.html">ING Kalkulator</a>
                <a href="mbank-kalkulator-kredytu-hipotecznego.html">mBank Kalkulator</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Porównaj stopy procentowe</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Aktualne stawki:</strong><br>
                        25 lat: <a href="#" onclick="return setRate('7.25', '25');">7.25%</a><br>
                        20 lat: <a href="#" onclick="return setRate('6.95', '20');">6.95%</a><br>
                        15 lat: <a href="#" onclick="return setRate('6.75', '15');">6.75%</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="o-nas.html">O nas</a> |
            <a href="kontakt.html">Kontakt</a> |
            <a href="polityka-prywatnosci.html">Polityka prywatności</a> |
            <a href="regulamin.html">Regulamin</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Oblicz ratę kredytu hipotecznego szybko i dokładnie. Porównaj różne warianty i wybierz najlepszą opcję finansowania nieruchomości.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function cshcosts() {
    var checkbox = document.getElementById('caddcosts');
    var costsDiv = document.getElementById('ccosts');
    if (checkbox.checked) {
        costsDiv.style.display = 'block';
    } else {
        costsDiv.style.display = 'none';
    }
}

function setRate(rate, term) {
    document.getElementById('cinterestrate').value = rate;
    document.getElementById('cloanterm').value = term;
    return false;
}
</script>

<script>
function calculateInstallmentRate() {
    var l = parseFloat(document.getElementById('cloanamount').value) || 400000;
    var r = parseFloat(document.getElementById('cinterestrate').value) || 7.25;
    var t = parseFloat(document.getElementById('cloanterm').value) || 25;

    var mr = r / 100 / 12;
    var n = t * 12;
    var m = (mr > 0) ? l * (mr * Math.pow(1 + mr, n)) / (Math.pow(1 + mr, n) - 1) : l / n;

    try {
        var resultElement = document.querySelector('.h2result');
        if (resultElement) {
            resultElement.innerHTML = 'Rata kredytu: ' + Math.round(m).toLocaleString('pl-PL') + ' zł';
        }

        var tableRow = document.querySelector('.crighthalf table table tbody tr:nth-child(2)');
        if (tableRow && tableRow.cells && tableRow.cells.length >= 3) {
            tableRow.cells[1].innerHTML = '<b>' + Math.round(m).toLocaleString('pl-PL') + ' zł</b>';
            tableRow.cells[2].innerHTML = '<b>' + Math.round(m * n).toLocaleString('pl-PL') + ' zł</b>';
        }
    } catch (e) {
        alert('Miesięczna rata: ' + Math.round(m).toLocaleString('pl-PL') + ' zł');
    }
}

function updateInstallmentRateResults(loanAmount, monthlyPayment, totalInterest, totalCost,
                                    installmentRatio, interestRatio, annualPayment,
                                    monthlyInterest, monthlyPrincipal, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Rata miesięczna: &nbsp; ' + formatNumber(monthlyPayment) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły rat
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegółowa analiza raty kredytu hipotecznego:</h3>' +
            '<p><strong>Rata miesięczna:</strong> ' + formatNumber(monthlyPayment) + ' zł (' + installmentRatio.toFixed(2) + '% kwoty kredytu)</p>' +
            '<p><strong>Rata roczna:</strong> ' + formatNumber(annualPayment) + ' zł</p>' +
            '<p><strong>Część odsetkowa raty:</strong> ' + formatNumber(monthlyInterest) + ' zł (średnio)</p>' +
            '<p><strong>Część kapitałowa raty:</strong> ' + formatNumber(monthlyPrincipal) + ' zł (średnio)</p>' +
            '<p><strong>Łączne odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + interestRatio.toFixed(0) + '% kwoty kredytu)</p>' +
            '<p><strong>Całkowity koszt kredytu:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Obciążenie budżetu:</strong> ' + getRateAnalysis(installmentRatio) + '</p>' +
            '<p><strong>Efektywność kredytu:</strong> ' + getEfficiencyAnalysis(interestRatio) + '</p>';
    }
}

function getRateAnalysis(ratio) {
    if (ratio < 2) {
        return 'Bardzo niskie obciążenie budżetu';
    } else if (ratio < 3) {
        return 'Niskie obciążenie budżetu';
    } else if (ratio < 4) {
        return 'Umiarkowane obciążenie budżetu';
    } else if (ratio < 5) {
        return 'Wysokie obciążenie budżetu';
    } else {
        return 'Bardzo wysokie obciążenie budżetu';
    }
}

function getEfficiencyAnalysis(ratio) {
    if (ratio < 80) {
        return 'Bardzo efektywny kredyt (niskie odsetki)';
    } else if (ratio < 120) {
        return 'Efektywny kredyt (umiarkowane odsetki)';
    } else if (ratio < 160) {
        return 'Średnio efektywny kredyt';
    } else if (ratio < 200) {
        return 'Mało efektywny kredyt (wysokie odsetki)';
    } else {
        return 'Nieefektywny kredyt (bardzo wysokie odsetki)';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '300000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.5';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '20';

    calculateInstallmentRate();
}

function saveCalResult() {
    alert('Analiza raty została zapisana!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateInstallmentRate();
};
</script>

</body>
</html>
