<!DOCTYPE html>
<html>
<head>
<title>Kalkulator kredytu hipotecznego - NAPRAWIONY</title>
<style>
body{font-family:Arial;margin:20px;background:#f5f5f5}
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px}
table{width:100%;border-collapse:collapse;margin:10px 0}
td{padding:10px;border:1px solid #ddd}
input{padding:8px;font-size:16px;border:1px solid #ccc;border-radius:4px}
button{padding:15px 30px;background:#28a745;color:white;border:none;cursor:pointer;border-radius:5px;font-size:18px;margin:10px}
button:hover{background:#218838}
.result{font-size:24px;font-weight:bold;color:#28a745;margin:20px 0;text-align:center;background:#f8f9fa;padding:20px;border-radius:5px}
h1{text-align:center;color:#333}
</style>
</head>
<body>
<div class="container">
<h1>🏠 KALKULATOR KREDYTU HIPOTECZNEGO 🏠</h1>

<table>
<tr><td><strong>Cena nieruchomości (zł):</strong></td><td><input type="number" id="price" value="500000"></td></tr>
<tr><td><strong>Wkład własny (%):</strong></td><td><input type="number" id="down" value="20"></td></tr>
<tr><td><strong>Okres kredytowania (lat):</strong></td><td><input type="number" id="years" value="25"></td></tr>
<tr><td><strong>Oprocentowanie (%):</strong></td><td><input type="number" id="rate" value="7.25" step="0.01"></td></tr>
<tr><td colspan="2" style="text-align:center">
<button onclick="oblicz()">🧮 OBLICZ RATĘ</button>
<button onclick="wyczysc()" style="background:#6c757d">🔄 WYCZYŚĆ</button>
</td></tr>
</table>

<div class="result" id="wynik">Miesięczna rata: 0 zł</div>

<table style="border:2px solid #28a745">
<tr style="background:#28a745;color:white"><td><strong>Szczegóły</strong></td><td><strong>Kwota</strong></td></tr>
<tr><td>💰 Kwota kredytu</td><td id="kredyt">0 zł</td></tr>
<tr><td>💵 Wkład własny</td><td id="wklad">0 zł</td></tr>
<tr><td>📅 Okres spłaty</td><td id="okres">0 miesięcy</td></tr>
<tr><td>📈 Oprocentowanie</td><td id="procent">0%</td></tr>
<tr><td>💸 Łączne odsetki</td><td id="odsetki">0 zł</td></tr>
<tr style="background:#e8f5e9"><td><strong>💵 ŁĄCZNY KOSZT</strong></td><td id="koszt"><strong>0 zł</strong></td></tr>
</table>

<div style="margin-top:20px;padding:20px;background:#e8f5e9;border-radius:10px;text-align:center">
<h3>✅ Jeśli widzisz tę stronę, HTML działa poprawnie!</h3>
<p>Kliknij "OBLICZ RATĘ" aby zobaczyć wyniki.</p>
<p><strong>Jeśli przycisk nie działa, JavaScript jest wyłączony w przeglądarce.</strong></p>
</div>

</div>

<script>
function oblicz(){
try{
var cena=parseFloat(document.getElementById('price').value)||500000;
var wklad_proc=parseFloat(document.getElementById('down').value)||20;
var lata=parseFloat(document.getElementById('years').value)||25;
var oprocentowanie=parseFloat(document.getElementById('rate').value)||7.25;

var wklad_kwota=cena*(wklad_proc/100);
var kwota_kredytu=cena-wklad_kwota;
var stopa_miesięczna=oprocentowanie/100/12;
var liczba_rat=lata*12;
var rata_miesięczna=kwota_kredytu*(stopa_miesięczna*Math.pow(1+stopa_miesięczna,liczba_rat))/(Math.pow(1+stopa_miesięczna,liczba_rat)-1);
var łączny_koszt=rata_miesięczna*liczba_rat;
var łączne_odsetki=łączny_koszt-kwota_kredytu;

function formatuj(liczba){
return Math.round(liczba).toLocaleString('pl-PL')+' zł';
}

document.getElementById('wynik').innerHTML='💰 Miesięczna rata: '+formatuj(rata_miesięczna);
document.getElementById('kredyt').innerHTML=formatuj(kwota_kredytu);
document.getElementById('wklad').innerHTML=formatuj(wklad_kwota)+' ('+wklad_proc+'%)';
document.getElementById('okres').innerHTML=liczba_rat+' miesięcy ('+lata+' lat)';
document.getElementById('procent').innerHTML=oprocentowanie+'% rocznie';
document.getElementById('odsetki').innerHTML=formatuj(łączne_odsetki);
document.getElementById('koszt').innerHTML='<strong>'+formatuj(łączny_koszt)+'</strong>';

console.log('Obliczenia zakończone pomyślnie');
}catch(e){
alert('Błąd obliczeń: '+e.message);
}
}

function wyczysc(){
document.getElementById('price').value='500000';
document.getElementById('down').value='20';
document.getElementById('years').value='25';
document.getElementById('rate').value='7.25';
oblicz();
}

window.onload=function(){
console.log('Strona załadowana');
oblicz();
};
</script>

</body>
</html>
