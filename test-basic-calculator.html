<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Test Basic Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        input, select { margin: 5px; padding: 5px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Basic Calculator Functions</h1>
    
    <div>
        <h2>Input Values:</h2>
        <label>House Price: <input type="text" id="housePrice" value="500000"> zł</label><br>
        <label>Down Payment: <input type="text" id="downPayment" value="20"></label>
        <select id="downPaymentUnit">
            <option value="p" selected>%</option>
            <option value="d">zł</option>
        </select><br>
        <label>Loan Term: <input type="text" id="loanTerm" value="25"> years</label><br>
        <label>Interest Rate: <input type="text" id="interestRate" value="7.25"> %</label><br>
        <button onclick="testCalculation()">Test Calculation</button>
    </div>
    
    <div id="testResults">
        <h2>Test Results:</h2>
        <div id="result1" class="test-result">Click Test Calculation to see results</div>
    </div>

    <script>
        function testCalculation() {
            try {
                console.log("Starting calculation test...");
                
                // Get input values
                var housePrice = parseFloat(document.getElementById('housePrice').value) || 0;
                var downPayment = parseFloat(document.getElementById('downPayment').value) || 0;
                var downPaymentUnit = document.getElementById('downPaymentUnit').value;
                var loanTerm = parseFloat(document.getElementById('loanTerm').value) || 0;
                var interestRate = parseFloat(document.getElementById('interestRate').value) || 0;
                
                console.log("Input values:", {housePrice, downPayment, downPaymentUnit, loanTerm, interestRate});
                
                // Calculate down payment amount
                var downPaymentAmount;
                if (downPaymentUnit === 'p') {
                    downPaymentAmount = housePrice * (downPayment / 100);
                } else if (downPaymentUnit === 'd') {
                    downPaymentAmount = downPayment;
                } else {
                    downPaymentAmount = downPayment;
                }
                
                var loanAmount = housePrice - downPaymentAmount;
                
                console.log("Calculated values:", {downPaymentAmount, loanAmount});
                
                // Calculate monthly payment
                var monthlyRate = interestRate / 100 / 12;
                var numPayments = loanTerm * 12;
                var monthlyPayment = 0;
                
                if (monthlyRate > 0) {
                    monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                    (Math.pow(1 + monthlyRate, numPayments) - 1);
                } else {
                    monthlyPayment = loanAmount / numPayments;
                }
                
                var totalPayment = monthlyPayment * numPayments;
                var totalInterest = totalPayment - loanAmount;
                
                console.log("Final calculations:", {monthlyPayment, totalPayment, totalInterest});
                
                // Display results
                var resultDiv = document.getElementById('result1');
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = 
                    '<h3>✅ Calculation Successful!</h3>' +
                    '<p><strong>House Price:</strong> ' + formatNumber(housePrice) + ' zł</p>' +
                    '<p><strong>Down Payment:</strong> ' + formatNumber(downPaymentAmount) + ' zł (' + downPayment + (downPaymentUnit === 'p' ? '%' : ' zł') + ')</p>' +
                    '<p><strong>Loan Amount:</strong> ' + formatNumber(loanAmount) + ' zł</p>' +
                    '<p><strong>Monthly Payment:</strong> ' + formatNumber(monthlyPayment) + ' zł</p>' +
                    '<p><strong>Total Payment:</strong> ' + formatNumber(totalPayment) + ' zł</p>' +
                    '<p><strong>Total Interest:</strong> ' + formatNumber(totalInterest) + ' zł</p>' +
                    '<p><strong>Interest Rate:</strong> ' + interestRate + '% per year</p>' +
                    '<p><strong>Loan Term:</strong> ' + loanTerm + ' years (' + numPayments + ' payments)</p>';
                    
            } catch (error) {
                console.error("Calculation error:", error);
                var resultDiv = document.getElementById('result1');
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '<h3>❌ Calculation Failed!</h3><p><strong>Error:</strong> ' + error.message + '</p>';
            }
        }
        
        function formatNumber(num) {
            return Math.round(num).toLocaleString('pl-PL');
        }
        
        // Test element access on page load
        window.onload = function() {
            console.log("Testing element access...");
            var elements = ['housePrice', 'downPayment', 'downPaymentUnit', 'loanTerm', 'interestRate'];
            var allFound = true;
            
            elements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    console.log("✅ Found element:", id, "value:", element.value);
                } else {
                    console.error("❌ Element not found:", id);
                    allFound = false;
                }
            });
            
            if (allFound) {
                console.log("✅ All elements found successfully!");
            } else {
                console.error("❌ Some elements are missing!");
            }
        };
    </script>
</body>
</html>
