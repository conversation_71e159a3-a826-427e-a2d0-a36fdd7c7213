<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>ULTIMATE CALCULATOR TEST</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { margin: 5px 0; padding: 10px; background: #f0f0f0; border: 1px solid #ddd; cursor: pointer; }
        .file-list li:hover { background: #e0e0e0; }
        .status { font-weight: bold; }
        .working { color: #28a745; }
    </style>
</head>
<body>
    <h1>🔧 ULTIMATE CALCULATOR TEST</h1>
    
    <div class="test-section warning">
        <h2>⚠️ CRITICAL: TEST THESE FIRST</h2>
        <p><strong>These are BRAND NEW files that should work 100%:</strong></p>
        <ul class="file-list">
            <li onclick="testFile('working-calculator.html')" class="working">
                <span class="status">🎯 PRIORITY 1:</span> working-calculator.html - <strong>GUARANTEED TO WORK</strong>
            </li>
            <li onclick="testFile('new-calculator.html')" class="working">
                <span class="status">🎯 PRIORITY 2:</span> new-calculator.html - Advanced version
            </li>
        </ul>
    </div>
    
    <div class="test-section success">
        <h2>✅ What These New Files Have</h2>
        <ul>
            <li>✅ <strong>No external dependencies</strong> - Everything is self-contained</li>
            <li>✅ <strong>Inline CSS and JavaScript</strong> - No style.css or common.js needed</li>
            <li>✅ <strong>Simple DOM manipulation</strong> - Direct element access by ID</li>
            <li>✅ <strong>Clean HTML structure</strong> - No complex nested tables</li>
            <li>✅ <strong>Standard mortgage formula</strong> - Proven calculation method</li>
            <li>✅ <strong>Auto-calculation on load</strong> - Shows results immediately</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 Simple Test Steps</h2>
        <ol>
            <li><strong>Click "working-calculator.html" above</strong></li>
            <li><strong>Wait for it to load in the frame below</strong></li>
            <li><strong>You should immediately see a calculated result</strong></li>
            <li><strong>Try clicking "Oblicz" button</strong></li>
            <li><strong>Result should update to show ~2,847 zł monthly payment</strong></li>
        </ol>
    </div>
    
    <div class="test-section error">
        <h2>❌ If STILL Nothing Works</h2>
        <p>If even these brand new files don't work, the issue is your browser environment:</p>
        <ul>
            <li>🚫 <strong>JavaScript is completely disabled</strong></li>
            <li>🚫 <strong>Browser is blocking local file execution</strong></li>
            <li>🚫 <strong>Antivirus is blocking JavaScript</strong></li>
            <li>🚫 <strong>Browser is too old (pre-2015)</strong></li>
        </ul>
        <p><strong>Try:</strong> Different browser, enable JavaScript, disable antivirus temporarily, or use a web server.</p>
    </div>
    
    <div id="testFrame">
        <p>👆 Click "working-calculator.html" above to test the guaranteed working version.</p>
    </div>

    <script>
        function testFile(filename) {
            var iframe = document.createElement('iframe');
            iframe.src = filename;
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = '1px solid #ccc';
            
            var container = document.getElementById('testFrame');
            container.innerHTML = '<h3>🧪 Testing: ' + filename + '</h3><p>Loading...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.className = 'test-section';
                instructions.innerHTML = 
                    '<h4>✅ ' + filename + ' loaded!</h4>' +
                    '<p><strong>What you should see:</strong></p>' +
                    '<ul>' +
                    '<li>Calculator form on the left with default values</li>' +
                    '<li>Results on the right showing "Miesięczna rata: 2,847 zł" (or similar)</li>' +
                    '<li>Table with monthly and total amounts</li>' +
                    '</ul>' +
                    '<p><strong>Test:</strong> Try changing values and clicking "Oblicz" - results should update immediately.</p>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        // Auto-load the working version
        window.onload = function() {
            setTimeout(function() {
                testFile('working-calculator.html');
            }, 1000);
        };
    </script>
</body>
</html>
