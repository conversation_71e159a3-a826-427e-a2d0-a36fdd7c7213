<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator odsetek kredytu hipotecznego</title>
    <meta name="description" content="Kalkulator odsetek kredytu hipotecznego - oblicz odsetki kredytu mieszkaniowego, sprawdź całkowity koszt odsetek i porównaj różne scenariusze kredytowe 2025.">
    <meta name="keywords" content="kalkulator odsetek kredytu hipotecznego, odsetki kredytu hipotecznego, koszt odsetek, oblicz odsetki kredytu">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-odsetek-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator odsetek kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator odsetek kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz odsetki kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-odsetek-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Kwota kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota kredytu hipotecznego do obliczenia odsetek. Wprowadź kwotę główną kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanamount" id="cloanamount" value="500000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczne oprocentowanie kredytu hipotecznego. WIBOR 3M + marża banku.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w latach. Maksymalnie 35 lat w większości banków.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Typ rat</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype" onchange="updatePaymentType();">
                                        <option value="annuity" selected="">Raty równe (annuitetowe)</option>
                                        <option value="decreasing">Raty malejące (kapitałowe)</option>
                                        <option value="interest_only">Tylko odsetki</option>
                                        <option value="balloon">Raty balonowe</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ analizy odsetek</td>
                                <td align="left" colspan="2">
                                    <select name="canalysistype" id="canalysistype" onchange="updateAnalysisType();">
                                        <option value="total" selected="">Całkowite odsetki</option>
                                        <option value="monthly">Miesięczne odsetki</option>
                                        <option value="yearly">Roczne odsetki</option>
                                        <option value="period">Odsetki w okresie</option>
                                        <option value="comparison">Porównanie scenariuszy</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="periodanalysis" style="display:none;">
                                <td align="right">Okres analizy <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres do analizy odsetek. Od roku do roku lub konkretny przedział.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right">
                                    Od roku <input type="text" name="cfromyear" id="cfromyear" value="1" class="in4char"> 
                                    do roku <input type="text" name="ctoyear" id="ctoyear" value="5" class="in4char">
                                </td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddscenarios" class="cbcontainer">
                                        <input type="checkbox" name="caddscenarios" id="caddscenarios" value="1" checked="" onclick="cshscenarios();">
                                        <span class="cbmark"></span>
                                        <b><span id="cscenariosdesc">Porównanie oprocentowania</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cscenarios" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Scenariusze oprocentowania</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 1 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwszy scenariusz oprocentowania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario1" id="cscenario1" value="6.5" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 2 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Drugi scenariusz oprocentowania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario2" id="cscenario2" value="7.25" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 3 <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzeci scenariusz oprocentowania do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario3" id="cscenario3" value="8.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddadvanced" class="cbcontainer">
                                        <input type="checkbox" name="caddadvanced" id="caddadvanced" value="1" onclick="cshadvanced();">
                                        <span class="cbmark"></span>
                                        <b><span id="cadvanceddesc">Zaawansowane opcje</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cadvanced" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Zaawansowane parametry</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Kapitalizacja odsetek <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Częstotliwość kapitalizacji odsetek. Miesięczna jest standardem w Polsce.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="left" colspan="2">
                                                        <select name="ccompounding" id="ccompounding">
                                                            <option value="monthly" selected="">Miesięczna</option>
                                                            <option value="quarterly">Kwartalna</option>
                                                            <option value="yearly">Roczna</option>
                                                            <option value="daily">Dzienna</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zmienność oprocentowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Przewidywana zmiana oprocentowania w czasie. Wpływ na całkowite odsetki.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cratechange" id="cratechange" value="0" class="innormal inpct"></td>
                                                    <td>rocznie</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Karencja <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres karencji w spłacie kapitału. Tylko odsetki przez określony czas.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cgraceperiod" id="cgraceperiod" value="0" class="innormal"></td>
                                                    <td>miesięcy</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz odsetki" onclick="calculateMortgageInterest();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                    <input type="button" value="Zapisz" onclick="saveCalculation();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Odsetki: &nbsp; 384 250 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBvZHNldGVrIGtyZWR5dHUgaGlwb3RlY3puZWdv', 0, 'S2Fsa3VsYXRvciBvZHNldGVrIGtyZWR5dHUgaGlwb3RlY3puZWdv', 'T2RzZXRraQ==', 'Mzg0IDI1MCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Kwota</b></td>
                                        <td align="right"><b>Procent</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowite odsetki</b></td>
                                        <td align="right"><b>384 250 zł</b></td>
                                        <td align="right"><b>76.9%</b></td>
                                        <td align="right"><b>1 281 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu</td>
                                        <td align="right">500 000 zł</td>
                                        <td align="right">100%</td>
                                        <td align="right">1 667 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">884 250 zł</td>
                                        <td align="right">176.9%</td>
                                        <td align="right">2 948 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Miesięczna rata</td>
                                        <td align="right">2 948 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">2 948 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Odsetki w 1. roku</td>
                                        <td align="right">35 850 zł</td>
                                        <td align="right">7.2%</td>
                                        <td align="right">2 988 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Odsetki w 5. roku</td>
                                        <td align="right">31 200 zł</td>
                                        <td align="right">6.2%</td>
                                        <td align="right">2 600 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza odsetek:</h3>
                                            <p><strong>Efektywna stopa:</strong> 7.25% (roczna stopa procentowa)</p>
                                            <p><strong>Udział odsetek:</strong> 76.9% całkowitego kosztu kredytu</p>
                                            <p><strong>Średnie odsetki:</strong> 1 281 zł miesięcznie przez 25 lat</p>
                                            <p><strong>Najwyższe odsetki:</strong> 3 021 zł w pierwszym miesiącu</p>
                                            <p><strong>Najniższe odsetki:</strong> 18 zł w ostatnim miesiącu</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie oprocentowania</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Oprocentowanie</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">300K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">400K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">500K zł</text>
                    
                    <!-- 6.5% -->
                    <rect x="50" y="125" width="35" height="20" fill="#27ae60" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">6.5%</text>
                    <text x="67" y="120" class="mcllabelx" style="fill:#000;">325K zł</text>
                    
                    <!-- 7.0% -->
                    <rect x="95" y="115" width="35" height="30" fill="#f39c12" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">7.0%</text>
                    <text x="112" y="110" class="mcllabelx" style="fill:#000;">355K zł</text>
                    
                    <!-- 7.25% -->
                    <rect x="140" y="105" width="35" height="40" fill="#e67e22" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">7.25%</text>
                    <text x="157" y="100" class="mcllabelx" style="fill:#000;">384K zł</text>
                    
                    <!-- 7.5% -->
                    <rect x="185" y="95" width="35" height="50" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">7.5%</text>
                    <text x="202" y="90" class="mcllabelx" style="fill:#000;">415K zł</text>
                    
                    <!-- 8.0% -->
                    <rect x="230" y="85" width="35" height="60" fill="#c0392b" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">8.0%</text>
                    <text x="247" y="80" class="mcllabelx" style="fill:#000;">445K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Porównanie odsetek &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram odsetek</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Oprocentowanie</th>
                            <th>Całkowite odsetki</th>
                            <th>Miesięczna rata</th>
                            <th>Różnica</th>
                            <th>Udział odsetek</th>
                        </tr>
                        <tr>
                            <td>6.5%</td>
                            <td>325 200 zł</td>
                            <td>2 751 zł</td>
                            <td>-59 050 zł</td>
                            <td>65.0%</td>
                        </tr>
                        <tr>
                            <td>7.0%</td>
                            <td>354 800 zł</td>
                            <td>2 849 zł</td>
                            <td>-29 450 zł</td>
                            <td>71.0%</td>
                        </tr>
                        <tr>
                            <td>7.25%</td>
                            <td>384 250 zł</td>
                            <td>2 948 zł</td>
                            <td>-</td>
                            <td>76.9%</td>
                        </tr>
                        <tr>
                            <td>7.5%</td>
                            <td>414 600 zł</td>
                            <td>3 049 zł</td>
                            <td>+30 350 zł</td>
                            <td>82.9%</td>
                        </tr>
                        <tr>
                            <td>8.0%</td>
                            <td>445 800 zł</td>
                            <td>3 151 zł</td>
                            <td>+61 550 zł</td>
                            <td>89.2%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator odsetek kredytu hipotecznego - precyzyjna analiza kosztów finansowych 2025</h2>
            <p>Odsetki kredytu hipotecznego stanowią znaczną część całkowitego kosztu kredytu mieszkaniowego, często przekraczając 70% kwoty głównej. Nasz kalkulator odsetek kredytu hipotecznego pomoże Ci precyzyjnie obliczyć odsetki kredytu mieszkaniowego, sprawdzić całkowity koszt odsetek i porównać różne scenariusze kredytowe, aby podjąć najlepszą decyzję finansową.</p>

            <h3>Typy rat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Raty równe (annuitetowe):</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Stała miesięczna rata przez cały okres kredytu</li>
                    <li><strong>Struktura:</strong> Na początku więcej odsetek, na końcu więcej kapitału</li>
                    <li><strong>Odsetki:</strong> Najwyższe w pierwszych latach spłaty</li>
                    <li><strong>Przewidywalność:</strong> Łatwe planowanie budżetu domowego</li>
                    <li><strong>Popularność:</strong> Najczęściej wybierane przez kredytobiorców</li>
                    <li><strong>Rekomendacja:</strong> Dla osób ceniących stabilność finansową</li>
                </ul>

                <h4>Raty malejące (kapitałowe):</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Stała część kapitałowa, malejące odsetki</li>
                    <li><strong>Struktura:</strong> Wysokie raty na początku, niskie na końcu</li>
                    <li><strong>Odsetki:</strong> Niższe całkowite odsetki niż przy ratach równych</li>
                    <li><strong>Oszczędność:</strong> Znaczące oszczędności na odsetkach</li>
                    <li><strong>Wymagania:</strong> Wyższa zdolność kredytowa na początku</li>
                    <li><strong>Rekomendacja:</strong> Dla osób o wysokich dochodach</li>
                </ul>

                <h4>Tylko odsetki:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Spłata tylko odsetek przez określony okres</li>
                    <li><strong>Struktura:</strong> Niskie raty na początku, wysoki kapitał na końcu</li>
                    <li><strong>Odsetki:</strong> Bardzo wysokie całkowite odsetki</li>
                    <li><strong>Ryzyko:</strong> Wysokie ryzyko finansowe</li>
                    <li><strong>Zastosowanie:</strong> Inwestycje w nieruchomości</li>
                    <li><strong>Rekomendacja:</strong> Tylko dla doświadczonych inwestorów</li>
                </ul>

                <h4>Raty balonowe:</h4>
                <ul>
                    <li><strong>Mechanizm:</strong> Niskie raty + duża spłata na końcu</li>
                    <li><strong>Struktura:</strong> Kombinacja odsetek i części kapitału</li>
                    <li><strong>Odsetki:</strong> Wysokie ze względu na długi okres spłaty</li>
                    <li><strong>Elastyczność:</strong> Możliwość refinansowania na końcu</li>
                    <li><strong>Ryzyko:</strong> Ryzyko braku możliwości spłaty balonu</li>
                    <li><strong>Rekomendacja:</strong> Dla specjalnych sytuacji finansowych</li>
                </ul>
            </div>

            <h3>Analiza odsetek kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Całkowite odsetki:</h4>
                <ul>
                    <li><strong>Definicja:</strong> Suma wszystkich odsetek przez cały okres kredytu</li>
                    <li><strong>Przykład:</strong> 384 250 zł przy kredycie 500 000 zł na 25 lat</li>
                    <li><strong>Udział:</strong> 76.9% całkowitego kosztu kredytu</li>
                    <li><strong>Znaczenie:</strong> Główny wskaźnik kosztowności kredytu</li>
                    <li><strong>Optymalizacja:</strong> Możliwość redukcji przez nadpłaty</li>
                </ul>

                <h4>Miesięczne odsetki:</h4>
                <ul>
                    <li><strong>Definicja:</strong> Część miesięcznej raty przeznaczona na odsetki</li>
                    <li><strong>Zmienność:</strong> Malejące w czasie przy ratach równych</li>
                    <li><strong>Początek:</strong> 3 021 zł w pierwszym miesiącu</li>
                    <li><strong>Koniec:</strong> 18 zł w ostatnim miesiącu</li>
                    <li><strong>Średnia:</strong> 1 281 zł miesięcznie przez 25 lat</li>
                </ul>

                <h4>Roczne odsetki:</h4>
                <ul>
                    <li><strong>Definicja:</strong> Suma odsetek zapłaconych w danym roku</li>
                    <li><strong>Pierwszy rok:</strong> 35 850 zł (7.2% kwoty kredytu)</li>
                    <li><strong>Piąty rok:</strong> 31 200 zł (6.2% kwoty kredytu)</li>
                    <li><strong>Tendencja:</strong> Malejące odsetki rok do roku</li>
                    <li><strong>Planowanie:</strong> Ważne dla rozliczeń podatkowych</li>
                </ul>
            </div>

            <h3>Czynniki wpływające na odsetki:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Czynnik</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wpływ na odsetki</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Przykład</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bezpośredni - każdy 1% to ~60K zł więcej</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7% vs 8% = 61K zł różnicy</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Okres kredytowania</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo wysoki - dłuższy okres = więcej odsetek</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">25 vs 30 lat = 120K zł różnicy</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Kwota kredytu</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Proporcjonalny - większa kwota = więcej odsetek</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">500K vs 400K = 77K zł różnicy</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Typ rat</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średni - malejące oszczędzają ~15%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Równe vs malejące = 58K zł różnicy</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Nadpłaty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo wysoki - każda nadpłata redukuje odsetki</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">500 zł/mies = 143K zł oszczędności</td>
                    </tr>
                </table>
            </div>

            <h3>Kapitalizacja odsetek:</h3>
            <ul>
                <li><strong>Miesięczna:</strong> Standard w Polsce - odsetki naliczane co miesiąc</li>
                <li><strong>Kwartalna:</strong> Rzadko stosowana - odsetki co 3 miesiące</li>
                <li><strong>Roczna:</strong> Bardzo rzadka - odsetki raz w roku</li>
                <li><strong>Dzienna:</strong> Najdokładniejsza - odsetki naliczane codziennie</li>
                <li><strong>Wpływ:</strong> Częstsza kapitalizacja = wyższe efektywne oprocentowanie</li>
                <li><strong>Różnica:</strong> Miesięczna vs roczna to około 0.3% różnicy w koszcie</li>
            </ul>

            <h3>Strategie minimalizacji odsetek:</h3>
            <ul>
                <li><strong>Nadpłaty miesięczne:</strong> Najefektywniejsza metoda redukcji odsetek</li>
                <li><strong>Wyższy wkład własny:</strong> Mniejsza kwota kredytu = mniejsze odsetki</li>
                <li><strong>Krótszy okres:</strong> Wyższe raty, ale znacznie mniejsze odsetki</li>
                <li><strong>Raty malejące:</strong> Oszczędność 10-15% na odsetkach</li>
                <li><strong>Refinansowanie:</strong> Zmiana na niższe oprocentowanie</li>
                <li><strong>Negocjacja marży:</strong> Rozmowy z bankiem o obniżce</li>
                <li><strong>Zmiana banku:</strong> Przeniesienie do banku z lepszą ofertą</li>
            </ul>

            <h3>Porównanie z innymi kosztami:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Koszt</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Kwota</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Procent kredytu</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Odsetki kredytu</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>384 250 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>76.9%</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja banku</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">10 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2.0%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">37 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.5%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Koszty notarialne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.6%</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wycena nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.3%</td>
                    </tr>
                </table>
            </div>

            <h3>Porady ekspertów dotyczące odsetek:</h3>
            <ul>
                <li>Zawsze porównuj RRSO, nie tylko oprocentowanie nominalne</li>
                <li>Rozważ raty malejące jeśli masz wysoką zdolność kredytową</li>
                <li>Każda nadpłata to gwarantowana oszczędność równa oprocentowaniu</li>
                <li>Monitoruj zmiany stóp procentowych i rozważ refinansowanie</li>
                <li>Negocjuj marżę banku, szczególnie jako stały klient</li>
                <li>Unikaj kredytów z tylko odsetkami bez konkretnego planu</li>
                <li>Pamiętaj o korzyściach podatkowych z odsetek kredytu</li>
                <li>Planuj nadpłaty już na etapie wyboru kredytu</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-nadplacania-kredytu-hipotecznego.html">Kalkulator nadpłacania kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Szybkie ustawienia</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Ustaw oprocentowanie:</strong><br>
                        <a href="#" onclick="return setInterestRate(6.5);">6.5% (niskie)</a><br>
                        <a href="#" onclick="return setInterestRate(7.0);">7.0% (średnie)</a><br>
                        <a href="#" onclick="return setInterestRate(7.25);">7.25% (aktualne)</a><br>
                        <a href="#" onclick="return setInterestRate(7.5);">7.5% (podwyższone)</a><br>
                        <a href="#" onclick="return setInterestRate(8.0);">8.0% (wysokie)</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator odsetek kredytu hipotecznego - oblicz odsetki kredytu mieszkaniowego, sprawdź całkowity koszt odsetek.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePaymentType() {
    var type = document.getElementById('cpaymenttype').value;
    // Można dodać logikę dla różnych typów rat
}

function updateAnalysisType() {
    var type = document.getElementById('canalysistype').value;
    var periodDiv = document.getElementById('periodanalysis');
    
    if (type === 'period') {
        periodDiv.style.display = 'table-row';
    } else {
        periodDiv.style.display = 'none';
    }
}

function cshscenarios() {
    var checkbox = document.getElementById('caddscenarios');
    var scenariosDiv = document.getElementById('cscenarios');
    if (checkbox.checked) {
        scenariosDiv.style.display = 'block';
    } else {
        scenariosDiv.style.display = 'none';
    }
}

function cshadvanced() {
    var checkbox = document.getElementById('caddadvanced');
    var advancedDiv = document.getElementById('cadvanced');
    if (checkbox.checked) {
        advancedDiv.style.display = 'block';
    } else {
        advancedDiv.style.display = 'none';
    }
}

function setInterestRate(rate) {
    document.getElementById('cinterestrate').value = rate.toFixed(2);
    return false;
}

function saveCalculation() {
    alert('Wyniki kalkulatora odsetek zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram odsetek &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Porównanie odsetek</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Porównanie odsetek &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram odsetek</a>';
    }
    return false;
}
</script>

<script>
function calculateMortgageInterest() {
    // 获取输入值
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var calculationType = document.getElementById('ccalculationtype').value;
    var interestAnalysis = document.getElementById('cinterestanalysis').value;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算总利息
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var averageMonthlyInterest = totalInterest / numPayments;

    // 计算不同类型的利息分析
    var interestBreakdown = calculateInterestBreakdown(loanAmount, interestRate, loanTerm, calculationType);
    var interestAnalysisResult = performInterestAnalysis(loanAmount, totalInterest, interestRate, interestAnalysis);

    // 计算利息比例
    var interestRatio = (totalInterest / loanAmount) * 100;
    var totalCost = loanAmount + totalInterest;

    // 更新显示
    updateInterestResults(loanAmount, monthlyPayment, totalInterest, averageMonthlyInterest,
                         interestRatio, totalCost, loanTerm, calculationType,
                         interestBreakdown, interestAnalysisResult);
}

function calculateInterestBreakdown(loanAmount, interestRate, loanTerm, calculationType) {
    var breakdown = {
        firstYear: 0,
        firstFiveYears: 0,
        firstTenYears: 0,
        remainingYears: 0,
        yearlyBreakdown: []
    };

    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    var remainingBalance = loanAmount;
    var totalInterestPaid = 0;

    // 按年计算利息
    for (var year = 1; year <= loanTerm; year++) {
        var yearlyInterest = 0;
        var yearlyPrincipal = 0;

        for (var month = 1; month <= 12; month++) {
            if ((year - 1) * 12 + month > numPayments) break;

            var monthlyInterest = remainingBalance * monthlyRate;
            var monthlyPrincipal = monthlyPayment - monthlyInterest;

            yearlyInterest += monthlyInterest;
            yearlyPrincipal += monthlyPrincipal;
            remainingBalance -= monthlyPrincipal;
            totalInterestPaid += monthlyInterest;
        }

        breakdown.yearlyBreakdown.push({
            year: year,
            interest: yearlyInterest,
            principal: yearlyPrincipal,
            remainingBalance: Math.max(remainingBalance, 0)
        });

        // 累计特定期间的利息
        if (year === 1) {
            breakdown.firstYear = yearlyInterest;
        }
        if (year <= 5) {
            breakdown.firstFiveYears += yearlyInterest;
        }
        if (year <= 10) {
            breakdown.firstTenYears += yearlyInterest;
        }
        if (year > 10) {
            breakdown.remainingYears += yearlyInterest;
        }
    }

    return breakdown;
}

function performInterestAnalysis(loanAmount, totalInterest, interestRate, analysisType) {
    var analysis = {
        interestBurden: '',
        comparison: '',
        recommendation: '',
        optimization: ''
    };

    var interestRatio = (totalInterest / loanAmount) * 100;

    // Analiza obciążenia odsetkami
    if (interestRatio < 120) {
        analysis.interestBurden = 'Niskie obciążenie odsetkami';
    } else if (interestRatio < 160) {
        analysis.interestBurden = 'Umiarkowane obciążenie odsetkami';
    } else if (interestRatio < 200) {
        analysis.interestBurden = 'Wysokie obciążenie odsetkami';
    } else {
        analysis.interestBurden = 'Bardzo wysokie obciążenie odsetkami';
    }

    // Porównanie z rynkiem
    if (interestRate < 6.0) {
        analysis.comparison = 'Oprocentowanie poniżej średniej rynkowej';
    } else if (interestRate < 8.0) {
        analysis.comparison = 'Oprocentowanie na poziomie średniej rynkowej';
    } else {
        analysis.comparison = 'Oprocentowanie powyżej średniej rynkowej';
    }

    // Rekomendacje
    switch(analysisType) {
        case 'optimization':
            analysis.recommendation = 'Rozważ nadpłaty kredytu dla zmniejszenia odsetek';
            analysis.optimization = 'Nadpłata 500 zł miesięcznie może zaoszczędzić 30-40% odsetek';
            break;
        case 'comparison':
            analysis.recommendation = 'Porównaj z ofertami innych banków';
            analysis.optimization = 'Refinansowanie może obniżyć koszty o 15-25%';
            break;
        case 'detailed':
            analysis.recommendation = 'Szczegółowa analiza pokazuje rozkład odsetek w czasie';
            analysis.optimization = 'Pierwsze lata to głównie odsetki, później kapitał';
            break;
        default:
            analysis.recommendation = 'Podstawowa analiza odsetek kredytu hipotecznego';
            analysis.optimization = 'Rozważ strategie optymalizacji kosztów';
    }

    return analysis;
}

function updateInterestResults(loanAmount, monthlyPayment, totalInterest, averageMonthlyInterest,
                              interestRatio, totalCost, loanTerm, calculationType,
                              interestBreakdown, interestAnalysisResult) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Odsetki łącznie: &nbsp; ' + formatNumber(totalInterest) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły odsetek
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegółowa analiza odsetek:</h3>' +
            '<p><strong>Łączne odsetki:</strong> ' + formatNumber(totalInterest) + ' zł (' + interestRatio.toFixed(0) + '% kwoty kredytu)</p>' +
            '<p><strong>Średnie odsetki miesięczne:</strong> ' + formatNumber(averageMonthlyInterest) + ' zł</p>' +
            '<p><strong>Odsetki w pierwszym roku:</strong> ' + formatNumber(interestBreakdown.firstYear) + ' zł</p>' +
            '<p><strong>Odsetki w pierwszych 5 latach:</strong> ' + formatNumber(interestBreakdown.firstFiveYears) + ' zł</p>' +
            '<p><strong>Odsetki w pierwszych 10 latach:</strong> ' + formatNumber(interestBreakdown.firstTenYears) + ' zł</p>' +
            '<p><strong>Obciążenie odsetkami:</strong> ' + interestAnalysisResult.interestBurden + '</p>' +
            '<p><strong>Porównanie rynkowe:</strong> ' + interestAnalysisResult.comparison + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + interestAnalysisResult.recommendation + '</p>' +
            '<p><strong>Optymalizacja:</strong> ' + interestAnalysisResult.optimization + '</p>';
    }
}

function getCalculationTypeName(type) {
    switch(type) {
        case 'basic': return 'Podstawowe obliczenie odsetek';
        case 'detailed': return 'Szczegółowy rozkład odsetek';
        case 'yearly': return 'Analiza roczna odsetek';
        case 'comparison': return 'Porównanie z rynkiem';
        default: return 'Standardowe obliczenie odsetek';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.5';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('ccalculationtype')) document.getElementById('ccalculationtype').value = 'basic';
    if (document.getElementById('cinterestanalysis')) document.getElementById('cinterestanalysis').value = 'basic';

    calculateMortgageInterest();
}

function saveCalResult() {
    alert('Analiza odsetek została zapisana!');
    return false;
}

function saveCalculation() {
    alert('Obliczenia odsetek zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateMortgageInterest();
};
</script>

</body>
</html>
