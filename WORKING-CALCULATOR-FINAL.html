<!DOCTYPE html>
<html>
<head>
<title>工作的计算器</title>
</head>
<body>

<h1>房贷计算器 - 最终工作版本</h1>

<p>房价: <input type="number" id="price" value="500000"> 元</p>
<p>首付: <input type="number" id="down" value="20"> %</p>
<p>年限: <input type="number" id="years" value="25"> 年</p>
<p>利率: <input type="number" id="rate" value="7.25"> %</p>

<button onclick="
var price = document.getElementById('price').value;
var down = document.getElementById('down').value;
var years = document.getElementById('years').value;
var rate = document.getElementById('rate').value;
var loan = price - (price * down / 100);
var monthly = loan * (rate/100/12 * Math.pow(1+rate/100/12, years*12)) / (Math.pow(1+rate/100/12, years*12) - 1);
alert('月供: ' + Math.round(monthly) + ' 元');
">计算</button>

<h2>如果上面的按钮不工作，说明JavaScript被禁用</h2>

<h3>启用JavaScript的方法：</h3>
<p><strong>Chrome浏览器：</strong></p>
<p>1. 点击右上角三个点</p>
<p>2. 选择"设置"</p>
<p>3. 点击"隐私设置和安全性"</p>
<p>4. 点击"网站设置"</p>
<p>5. 点击"JavaScript"</p>
<p>6. 选择"允许（推荐）"</p>

<p><strong>Firefox浏览器：</strong></p>
<p>1. 在地址栏输入：about:config</p>
<p>2. 点击"接受风险并继续"</p>
<p>3. 搜索：javascript.enabled</p>
<p>4. 确保值为 true</p>

<p><strong>Edge浏览器：</strong></p>
<p>1. 点击右上角三个点</p>
<p>2. 选择"设置"</p>
<p>3. 点击"Cookie和网站权限"</p>
<p>4. 点击"JavaScript"</p>
<p>5. 确保开关是"开启"状态</p>

</body>
</html>
