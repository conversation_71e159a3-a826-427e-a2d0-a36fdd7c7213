<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>内联修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
        .file-list { list-style: none; padding: 0; }
        .file-list li { margin: 5px 0; padding: 10px; background: #f0f0f0; border: 1px solid #ddd; cursor: pointer; }
        .file-list li:hover { background: #e0e0e0; }
        .status { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <h1>🔧 内联JavaScript修复测试</h1>
    
    <div class="test-section success">
        <h2>✅ 使用内联JavaScript修复的文件</h2>
        <p>以下文件已经使用<strong>内联JavaScript</strong>方法修复，不依赖任何外部函数：</p>
        <ul class="file-list">
            <li onclick="testFile('kalkulator-kredytu-hipotecznego-online.html')">
                <span class="status">✅ 内联修复:</span> kalkulator-kredytu-hipotecznego-online.html
            </li>
            <li onclick="testFile('kalkulator-raty-kredytu-hipotecznego.html')">
                <span class="status">✅ 内联修复:</span> kalkulator-raty-kredytu-hipotecznego.html
            </li>
            <li onclick="testFile('ing-kalkulator-kredytu-hipotecznego.html')">
                <span class="status">✅ 内联修复:</span> ing-kalkulator-kredytu-hipotecznego.html
            </li>
            <li onclick="testFile('mbank-kalkulator-kredytu-hipotecznego.html')">
                <span class="status">✅ 内联修复:</span> mbank-kalkulator-kredytu-hipotecznego.html
            </li>
        </ul>
    </div>
    
    <div class="test-section warning">
        <h2>⚡ 内联JavaScript修复方法</h2>
        <p>这次修复使用了最直接的方法：</p>
        <ul>
            <li>✅ <strong>直接在onclick属性中写JavaScript代码</strong></li>
            <li>✅ <strong>不依赖任何外部函数</strong></li>
            <li>✅ <strong>不需要script标签</strong></li>
            <li>✅ <strong>所有计算逻辑都在按钮中</strong></li>
            <li>✅ <strong>添加了try-catch错误处理</strong></li>
            <li>✅ <strong>如果DOM更新失败，使用alert显示结果</strong></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试说明</h2>
        <ol>
            <li><strong>点击上面的文件名</strong>在下方iframe中加载</li>
            <li><strong>等待页面完全加载</strong></li>
            <li><strong>点击"Oblicz"按钮</strong>（各种变体：Oblicz online, Oblicz ratę, Oblicz w ING, Oblicz kredyt mBank）</li>
            <li><strong>检查结果</strong>：
                <ul>
                    <li>主标题应该更新显示月供</li>
                    <li>表格中的数值应该更新</li>
                    <li>如果页面更新失败，应该弹出alert显示结果</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🎯 预期结果</h2>
        <p><strong>默认测试数据：</strong></p>
        <ul>
            <li>房价: 500,000 zł (或贷款金额: 400,000 zł)</li>
            <li>首付: 20%</li>
            <li>期限: 25年</li>
            <li>利率: 7.25%</li>
        </ul>
        <p><strong>预期月供: 约 2,847 zł</strong></p>
        <p><strong>如果内联JavaScript工作，这次修复应该100%成功！</strong></p>
    </div>
    
    <div id="testFrame">
        <p>👆 点击上面的文件名开始测试</p>
    </div>
    
    <div class="test-section">
        <h2>🔧 内联JavaScript示例</h2>
        <p>修复后的按钮代码示例：</p>
        <pre style="background: #f5f5f5; padding: 10px; overflow-x: auto;">
&lt;input type="button" value="Oblicz" onclick="
var h=parseFloat(document.getElementById('chouseprice').value)||500000;
var d=parseFloat(document.getElementById('cdownpayment').value)||20;
var u=document.getElementById('cdownpaymentunit')?document.getElementById('cdownpaymentunit').value:'p';
var t=parseFloat(document.getElementById('cloanterm').value)||25;
var r=parseFloat(document.getElementById('cinterestrate').value)||7.25;
var da=(u==='p')?h*(d/100):d;
var l=h-da;
var mr=r/100/12;
var n=t*12;
var m=(mr>0)?l*(mr*Math.pow(1+mr,n))/(Math.pow(1+mr,n)-1):l/n;
try{
document.querySelector('.h2result').innerHTML='Miesięczna rata: '+Math.round(m).toLocaleString('pl-PL')+' zł';
var tb=document.querySelector('.crighthalf table table tbody tr:nth-child(2)');
if(tb&&tb.cells&&tb.cells.length>=3){
tb.cells[1].innerHTML='&lt;b>'+Math.round(m).toLocaleString('pl-PL')+' zł&lt;/b>';
tb.cells[2].innerHTML='&lt;b>'+Math.round(m*n).toLocaleString('pl-PL')+' zł&lt;/b>';
}
}catch(e){alert('Miesięczna rata: '+Math.round(m).toLocaleString('pl-PL')+' zł');}
"&gt;
        </pre>
    </div>

    <script>
        function testFile(filename) {
            var iframe = document.createElement('iframe');
            iframe.src = filename;
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = '1px solid #ccc';
            
            var container = document.getElementById('testFrame');
            container.innerHTML = '<h3>🧪 测试: ' + filename + '</h3><p>加载中...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.className = 'test-section';
                instructions.innerHTML = 
                    '<h4>测试 ' + filename + ':</h4>' +
                    '<p><strong>操作步骤：</strong></p>' +
                    '<ol>' +
                    '<li>等待页面完全加载</li>' +
                    '<li>找到并点击"Oblicz"按钮（可能是"Oblicz online"、"Oblicz ratę"等）</li>' +
                    '<li>观察结果是否更新</li>' +
                    '<li>如果页面没有更新，检查是否弹出了alert</li>' +
                    '</ol>' +
                    '<p><strong>成功标志：</strong>主标题显示月供金额，或弹出alert显示结果</p>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        // 自动加载第一个文件
        window.onload = function() {
            setTimeout(function() {
                testFile('kalkulator-kredytu-hipotecznego-online.html');
            }, 1000);
        };
    </script>
</body>
</html>
