# PowerShell脚本验证sitemap.xml中的所有URL
Write-Host "=== 验证sitemap.xml中的URL ===" -ForegroundColor Green

# 读取sitemap.xml文件
if (-not (Test-Path "sitemap.xml")) {
    Write-Host "❌ sitemap.xml文件不存在！" -ForegroundColor Red
    exit 1
}

$sitemapContent = Get-Content "sitemap.xml" -Raw -Encoding UTF8

# 提取所有URL
$urlPattern = '<loc>https://kalkulator-kredytu-hipotecznego\.pl/([^<]+)</loc>'
$matches = [regex]::Matches($sitemapContent, $urlPattern)

$allUrls = @()
$existingFiles = @()
$missingFiles = @()

Write-Host "从sitemap.xml中找到 $($matches.Count) 个URL" -ForegroundColor Yellow

foreach ($match in $matches) {
    $fullUrl = $match.Groups[0].Value
    $filename = $match.Groups[1].Value
    
    # 处理根URL
    if ($filename -eq "") {
        $filename = "index.html"
    }
    
    $urlInfo = New-Object PSObject -Property @{
        url = $fullUrl
        file = $filename
    }
    $allUrls += $urlInfo
    
    # 检查文件是否存在
    if (Test-Path $filename) {
        $existingFiles += $filename
        Write-Host "✅ $filename - 存在" -ForegroundColor Green
    } else {
        $missingFiles += $filename
        Write-Host "❌ $filename - 缺失" -ForegroundColor Red
    }
}

# 显示总结
Write-Host ""
Write-Host "=== 验证结果 ===" -ForegroundColor Green
Write-Host "总URL数: $($allUrls.Count)" -ForegroundColor Cyan
Write-Host "存在的文件: $($existingFiles.Count)" -ForegroundColor Green
Write-Host "缺失的文件: $($missingFiles.Count)" -ForegroundColor Red

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "=== 缺失的文件列表 ===" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "- $file" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "=== 建议的修复操作 ===" -ForegroundColor Yellow
    Write-Host "1. 从sitemap.xml中删除不存在的URL" -ForegroundColor Yellow
    Write-Host "2. 或者创建缺失的文件" -ForegroundColor Yellow
    
    # 生成清理后的sitemap
    Write-Host ""
    Write-Host "=== 生成清理后的sitemap ===" -ForegroundColor Cyan
    
    $cleanSitemap = @"
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

"@
    
    foreach ($urlInfo in $allUrls) {
        if ($existingFiles -contains $urlInfo.file -or $urlInfo.file -eq "index.html") {
            $cleanSitemap += @"
    <url>
        <loc>$($urlInfo.url)</loc>
        <lastmod>2025-01-28</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>

"@
        }
    }
    
    $cleanSitemap += "</urlset>"
    
    # 保存清理后的sitemap
    $cleanSitemap | Out-File -FilePath "sitemap-clean.xml" -Encoding UTF8
    Write-Host "✅ 清理后的sitemap已保存为: sitemap-clean.xml" -ForegroundColor Green
    
} else {
    Write-Host ""
    Write-Host "🎉 所有sitemap中的URL都对应存在的文件！" -ForegroundColor Green
}

# 检查是否有重要文件没有包含在sitemap中
Write-Host ""
Write-Host "=== 检查遗漏的重要文件 ===" -ForegroundColor Cyan

$importantFiles = @(
    "index.html",
    "o-nas.html",
    "kontakt.html",
    "polityka-prywatnosci.html",
    "regulamin.html"
)

$sitemapFiles = $allUrls | ForEach-Object { $_.file }
$missingImportant = @()

foreach ($file in $importantFiles) {
    if (Test-Path $file) {
        if ($sitemapFiles -notcontains $file -and $file -ne "index.html") {
            $missingImportant += $file
            Write-Host "⚠️  重要文件未包含在sitemap中: $file" -ForegroundColor Yellow
        } else {
            Write-Host "✅ $file - 已包含在sitemap中" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ 重要文件不存在: $file" -ForegroundColor Red
    }
}

if ($missingImportant.Count -gt 0) {
    Write-Host ""
    Write-Host "建议将以下重要文件添加到sitemap中:" -ForegroundColor Yellow
    foreach ($file in $missingImportant) {
        Write-Host "- $file" -ForegroundColor Yellow
    }
}

# 生成报告
$report = @{
    timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
    total_urls = $allUrls.Count
    existing_files = $existingFiles.Count
    missing_files = $missingFiles.Count
    success_rate = if ($allUrls.Count -gt 0) { [math]::Round(($existingFiles.Count / $allUrls.Count) * 100, 2) } else { 100 }
    missing_files_list = $missingFiles
    existing_files_list = $existingFiles
    missing_important = $missingImportant
}

$report | ConvertTo-Json -Depth 2 | Out-File -FilePath "sitemap-validation-report.json" -Encoding UTF8
Write-Host ""
Write-Host "📊 验证报告已保存到: sitemap-validation-report.json" -ForegroundColor Green
