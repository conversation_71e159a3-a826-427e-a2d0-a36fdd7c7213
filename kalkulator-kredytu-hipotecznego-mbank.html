<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego mBank</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego mBank - oblicz ratę kredytu mieszkaniowego w mBank, sprawdź warunki i promocje mBank na kredyty hipoteczne 2025.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego mbank, mbank kredyt hipoteczny, rata kredytu mbank, warunki kredytu mbank">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-mbank.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego mbank</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego mBank</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w mBank"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-mbank.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości w mBank. Na podstawie tej kwoty mBank obliczy maksymalną kwotę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmpropertyvalue" id="cmpropertyvalue" value="700000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny w mBank. Minimum 10% wartości nieruchomości. Wyższy wkład = lepsza marża mBank.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cmdownpayment" id="cmdownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="cmdownpaymentunit" onchange="cunitchangembank('cmdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w mBank. Maksymalnie 35 lat. Dłuższy okres = niższa rata mBank.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmloanterm" id="cmloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w mBank. WIBOR 3M + marża mBank.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cminterestrate" id="cminterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Profil klienta mBank</td>
                                <td align="left" colspan="2">
                                    <select name="cmclientprofile" id="cmclientprofile" onchange="updateMBankRate();">
                                        <option value="new_mbank" selected="">Nowy klient mBank</option>
                                        <option value="existing_mbank">Obecny klient mBank</option>
                                        <option value="premium_mbank">mBank Premium</option>
                                        <option value="private_mbank">mBank Private</option>
                                        <option value="young_mbank">mKonto Młodzi</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania mBank</td>
                                <td align="left" colspan="2">
                                    <select name="cminteresttype" id="cminteresttype" onchange="updateMBankInterestType();">
                                        <option value="variable_mbank" selected="">Zmienne mBank (WIBOR + marża)</option>
                                        <option value="fixed_3y_mbank">Stałe na 3 lata mBank</option>
                                        <option value="fixed_5y_mbank">Stałe na 5 lat mBank</option>
                                        <option value="mixed_mbank">Mieszane mBank</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddmbankfees" class="cbcontainer">
                                        <input type="checkbox" name="caddmbankfees" id="caddmbankfees" value="1" checked="" onclick="cshmbankfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cmbankfeesdesc">Opłaty mBank</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cmbankfees" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty i koszty mBank</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przygotowawcza mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja mBank za przygotowanie kredytu. 0-2% kwoty kredytu w zależności od profilu klienta mBank.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmprovision" id="cmprovision" value="1.8" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rachunek kredytowy mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w mBank. 0-20 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmaccount" id="cmaccount" value="10" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w mBank. 0.20-0.35% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cminsurance" id="cminsurance" value="0.28" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę mBank. 1000-1800 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmvaluation" id="cmvaluation" value="1400" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty mBank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty mBank: notariusz, wpis hipoteki, ubezpieczenie nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmother" id="cmother" value="3200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddmbankpromo" class="cbcontainer">
                                        <input type="checkbox" name="caddmbankpromo" id="caddmbankpromo" value="1" onclick="cshmbankpromo();">
                                        <span class="cbmark"></span>
                                        <b><span id="cmbankpromodesc">Promocje mBank 2025</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cmbankpromo" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Cashback mBank</td>
                                                    <td align="right"><input type="text" name="cmcashback" id="cmcashback" value="4000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zwolnienie z prowizji mBank</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cmprovisionwaiver" id="cmprovisionwaiver">
                                                            <option value="0" selected="">Brak zwolnienia</option>
                                                            <option value="50">50% zwolnienia</option>
                                                            <option value="100">100% zwolnienia</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Promocyjna marża mBank</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cmpromomargin" id="cmpromomargin">
                                                            <option value="0" selected="">Standardowa marża</option>
                                                            <option value="0.25">-0.25% przez 12 miesięcy</option>
                                                            <option value="0.50">-0.50% przez 6 miesięcy</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz kredyt mBank" onclick="
var h=parseFloat(document.getElementById('chouseprice').value)||500000;
var d=parseFloat(document.getElementById('cdownpayment').value)||20;
var u=document.getElementById('cdownpaymentunit')?document.getElementById('cdownpaymentunit').value:'p';
var t=parseFloat(document.getElementById('cloanterm').value)||25;
var r=parseFloat(document.getElementById('cinterestrate').value)||7.25;
var da=(u==='p')?h*(d/100):d;
var l=h-da;
var mr=r/100/12;
var n=t*12;
var m=(mr>0)?l*(mr*Math.pow(1+mr,n))/(Math.pow(1+mr,n)-1):l/n;
alert('mBank kredyt - Miesięczna rata: '+Math.round(m).toLocaleString('pl-PL')+' zł (Kwota kredytu: '+Math.round(l).toLocaleString('pl-PL')+' zł)');
">
                                    <input type="button" value="Wyczyść" onclick="
document.getElementById('chouseprice').value='500000';
document.getElementById('cdownpayment').value='20';
document.getElementById('cloanterm').value='25';
document.getElementById('cinterestrate').value='7.25';
">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata mBank: &nbsp; 4 125 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBtQmFuaw==', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBtQmFuaw==', 'UmF0YSBtQmFuaw==', 'NCA1MjUgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>mBank</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata mBank</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>4 125 zł</b></td>
                                        <td align="right"><b>1 237 500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu mBank</td>
                                        <td align="right">560 000 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">560 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał + odsetki mBank</td>
                                        <td align="right">-</td>
                                        <td align="right">4 000 zł</td>
                                        <td align="right">1 200 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie mBank</td>
                                        <td align="right">-</td>
                                        <td align="right">130 zł</td>
                                        <td align="right">39 200 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Rachunek kredytowy mBank</td>
                                        <td align="right">-</td>
                                        <td align="right">10 zł</td>
                                        <td align="right">3 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty jednorazowe mBank</td>
                                        <td align="right">14 680 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">14 680 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Szczegóły kredytu mBank:</h3>
                                            <p><strong>RRSO mBank:</strong> 8.15% (z wszystkimi opłatami mBank)</p>
                                            <p><strong>Status klienta:</strong> Nowy klient mBank (marża standardowa)</p>
                                            <p><strong>Typ oprocentowania:</strong> Zmienne WIBOR + marża mBank</p>
                                            <p><strong>Całkowity koszt mBank:</strong> 1 256 880 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 696 880 zł (124% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie profili klientów mBank</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Profil mBank</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3800 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">4000 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4200 zł</text>
                    
                    <!-- mBank Private -->
                    <rect x="50" y="95" width="35" height="50" fill="#00a651" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">Private</text>
                    <text x="67" y="90" class="mcllabelx" style="fill:#000;">3925 zł</text>
                    
                    <!-- mBank Premium -->
                    <rect x="95" y="88" width="35" height="57" fill="#4caf50" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">Premium</text>
                    <text x="112" y="83" class="mcllabelx" style="fill:#000;">3985 zł</text>
                    
                    <!-- mKonto Młodzi -->
                    <rect x="140" y="82" width="35" height="63" fill="#8bc34a" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">Młodzi</text>
                    <text x="157" y="77" class="mcllabelx" style="fill:#000;">4045 zł</text>
                    
                    <!-- Obecny klient -->
                    <rect x="185" y="78" width="35" height="67" fill="#cddc39" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">Obecny</text>
                    <text x="202" y="73" class="mcllabelx" style="fill:#000;">4085 zł</text>
                    
                    <!-- Nowy klient -->
                    <rect x="230" y="72" width="35" height="73" fill="#ffeb3b" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">Nowy</text>
                    <text x="247" y="67" class="mcllabelx" style="fill:#000;">4125 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Profile klientów mBank &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania mBank</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Typ oprocentowania mBank</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Różnica</th>
                        </tr>
                        <tr>
                            <td>Zmienne WIBOR + marża</td>
                            <td>7.25%</td>
                            <td>4 125 zł</td>
                            <td>1 237 500 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Stałe na 3 lata mBank</td>
                            <td>7.65%</td>
                            <td>4 285 zł</td>
                            <td>1 285 500 zł</td>
                            <td>+48 000 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 5 lat mBank</td>
                            <td>7.95%</td>
                            <td>4 425 zł</td>
                            <td>1 327 500 zł</td>
                            <td>+90 000 zł</td>
                        </tr>
                        <tr>
                            <td>Mieszane mBank</td>
                            <td>7.45%</td>
                            <td>4 225 zł</td>
                            <td>1 267 500 zł</td>
                            <td>+30 000 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-kredytu-hipotecznego-pko.html">PKO kalkulator kredytu</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Profile klientów mBank</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz profil mBank:</strong><br>
                        <a href="#" onclick="return setMBankProfile('private_mbank');">mBank Private</a><br>
                        <a href="#" onclick="return setMBankProfile('premium_mbank');">mBank Premium</a><br>
                        <a href="#" onclick="return setMBankProfile('young_mbank');">mKonto Młodzi</a><br>
                        <a href="#" onclick="return setMBankProfile('existing_mbank');">Obecny klient</a><br>
                        <a href="#" onclick="return setMBankProfile('new_mbank');">Nowy klient</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego mBank - oblicz ratę kredytu mieszkaniowego w mBank, sprawdź warunki i promocje.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateMBankRate() {
    var profile = document.getElementById('cmclientprofile').value;
    var rateField = document.getElementById('cminterestrate');
    var baseRate = 7.25;
    
    switch(profile) {
        case 'private_mbank':
            rateField.value = (baseRate - 0.45).toFixed(2);
            break;
        case 'premium_mbank':
            rateField.value = (baseRate - 0.30).toFixed(2);
            break;
        case 'young_mbank':
            rateField.value = (baseRate - 0.20).toFixed(2);
            break;
        case 'existing_mbank':
            rateField.value = (baseRate - 0.15).toFixed(2);
            break;
        case 'new_mbank':
            rateField.value = baseRate.toFixed(2);
            break;
    }
}

function updateMBankInterestType() {
    var type = document.getElementById('cminteresttype').value;
    var rateField = document.getElementById('cminterestrate');
    var baseRate = 7.25;
    
    switch(type) {
        case 'variable_mbank':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'fixed_3y_mbank':
            rateField.value = (baseRate + 0.40).toFixed(2);
            break;
        case 'fixed_5y_mbank':
            rateField.value = (baseRate + 0.70).toFixed(2);
            break;
        case 'mixed_mbank':
            rateField.value = (baseRate + 0.20).toFixed(2);
            break;
    }
}

function cshmbankfees() {
    var checkbox = document.getElementById('caddmbankfees');
    var feesDiv = document.getElementById('cmbankfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshmbankpromo() {
    var checkbox = document.getElementById('caddmbankpromo');
    var promoDiv = document.getElementById('cmbankpromo');
    if (checkbox.checked) {
        promoDiv.style.display = 'block';
    } else {
        promoDiv.style.display = 'none';
    }
}

function setMBankProfile(profile) {
    document.getElementById('cmclientprofile').value = profile;
    updateMBankRate();
    return false;
}

function cunitchangembank(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania mBank &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Profile klientów mBank</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Profile klientów mBank &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania mBank</a>';
    }
    return false;
}
</script>

</body>
</html>
