<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Minimal Test Calculator</title>
	<meta name="description" content="Test calculator">
	<link rel="stylesheet" href="style.css">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head><body>

<div id="contentout"><div id="content">
<h1>Minimal Test Calculator</h1>

<div class="clefthalf">
<div class="panel"><form name="calform">
<table align="center">
<tbody><tr><td align="right"><PERSON><PERSON></td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000" class="inhalf indollar"></td><td>zł</td></tr>
<tr><td align="right">Wkład własny</td><td align="right"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf"></td>
<td><select name="cdownpaymentunit" id="cdownpaymentunit"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania</td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie</td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td colspan="3" align="center"><input type="button" name="x" value="Oblicz" onclick="calculateMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Miesięczna rata: &nbsp; 0 zł</h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
<tr><td>Podatek od nieruchomości</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie domu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Ubezpieczenie kredytu</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr><td>Opłaty administracyjne</td><td align="right">0 zł</td><td align="right">0 zł</td></tr>
<tr bgcolor="#dddddd"><td><b>Łącznie</b></td><td align="right"><b>0 zł</b></td><td align="right"><b>0 zł</b></td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

</div></div>

<script>
function calculateMortgage() {
    alert("Function called!");
    
    // Get values
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    alert("Got values: " + housePrice + ", " + downPayment + ", " + loanTerm + ", " + interestRate);
    
    // Calculate down payment amount
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // Calculate monthly payment
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    alert("Calculated monthly payment: " + Math.round(monthlyPayment));
    
    // Update display
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 'Miesięczna rata: &nbsp; ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
        alert("Updated h2result successfully!");
    } else {
        alert("ERROR: h2result not found!");
    }
    
    // Update table
    var resultTable = document.querySelector('.crighthalf table table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        if (rows.length > 1 && rows[1].cells.length >= 3) {
            rows[1].cells[1].innerHTML = '<b>' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł</b>';
            rows[1].cells[2].innerHTML = '<b>' + Math.round(monthlyPayment * numPayments).toLocaleString('pl-PL') + ' zł</b>';
            alert("Updated table successfully!");
        } else {
            alert("ERROR: Table structure problem");
        }
    } else {
        alert("ERROR: Table not found!");
    }
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.25';
    calculateMortgage();
}

// Test on page load
window.onload = function() {
    alert("Page loaded, running test calculation...");
    calculateMortgage();
};
</script>

</body></html>
