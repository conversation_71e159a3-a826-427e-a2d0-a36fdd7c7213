<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Kalkulatora</title>
    <link rel="stylesheet" href="style.css">
    <script src="common.js"></script>
</head>
<body>
    <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
        <h1>Test Funkcji Kalkulatora</h1>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 1: Podstawowe funkcje matematyczne</h2>
            <div id="test1-results"></div>
        </div>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 2: Formatowanie <PERSON>d<PERSON></h2>
            <div id="test2-results"></div>
        </div>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 3: <PERSON><PERSON><PERSON> kredytu hipotecznego</h2>
            <div>
                <label>Kwota kredytu: <input type="text" id="test-principal" value="400000"></label><br><br>
                <label>Oprocentowanie (%): <input type="text" id="test-rate" value="7.25"></label><br><br>
                <label>Okres (lata): <input type="text" id="test-years" value="25"></label><br><br>
                <button onclick="testMortgageCalculation()">Oblicz</button>
            </div>
            <div id="test3-results"></div>
        </div>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 4: Kalkulator zdolności kredytowej</h2>
            <div>
                <label>Dochody netto: <input type="text" id="test-income" value="8000"></label><br><br>
                <label>Wydatki: <input type="text" id="test-expenses" value="3000"></label><br><br>
                <label>Liczba osób: <input type="text" id="test-family" value="2"></label><br><br>
                <button onclick="testCreditCapacity()">Oblicz zdolność</button>
            </div>
            <div id="test4-results"></div>
        </div>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 5: Kalkulator nadpłat</h2>
            <div>
                <label>Saldo kredytu: <input type="text" id="test-balance" value="350000"></label><br><br>
                <label>Miesięczna rata: <input type="text" id="test-payment" value="2500"></label><br><br>
                <label>Nadpłata miesięczna: <input type="text" id="test-prepayment" value="500"></label><br><br>
                <label>Pozostały okres (lata): <input type="text" id="test-remaining" value="20"></label><br><br>
                <button onclick="testPrepaymentCalculation()">Oblicz oszczędności</button>
            </div>
            <div id="test5-results"></div>
        </div>
        
        <div style="background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h2>Test 6: Tooltip i walidacja</h2>
            <div>
                <input type="text" id="test-validation" placeholder="Wpisz liczbę">
                <button onclick="testValidation()">Sprawdź walidację</button>
                <br><br>
                <span onmouseover="tooltip.show('To jest test tooltip systemu!');" onmouseout="tooltip.hide();" style="background: yellow; padding: 5px; cursor: pointer;">
                    Najedź myszką aby zobaczyć tooltip
                </span>
            </div>
            <div id="test6-results"></div>
        </div>
    </div>

    <script>
        // Test podstawowych funkcji
        function runBasicTests() {
            var results = [];
            
            // Test isNumber
            results.push("isNumber('123'): " + isNumber('123'));
            results.push("isNumber('abc'): " + isNumber('abc'));
            results.push("isNumber('123.45'): " + isNumber('123.45'));
            
            // Test isInteger
            results.push("isInteger('123'): " + isInteger('123'));
            results.push("isInteger('123.45'): " + isInteger('123.45'));
            
            // Test trimAll
            results.push("trimAll('  test  '): '" + trimAll('  test  ') + "'");
            
            document.getElementById('test1-results').innerHTML = results.join('<br>');
        }
        
        // Test formatowania pieniędzy
        function runMoneyFormatTests() {
            var results = [];
            
            results.push("formatAsMoney(1234.56): " + formatAsMoney(1234.56));
            results.push("formatAsMoney(1000000): " + formatAsMoney(1000000));
            results.push("formatAsMoney(0): " + formatAsMoney(0));
            results.push("formatAsMoneyFull(1234.56, 0): " + formatAsMoneyFull(1234.56, 0));
            
            document.getElementById('test2-results').innerHTML = results.join('<br>');
        }
        
        // Test kalkulatora kredytu
        function testMortgageCalculation() {
            var principal = parseFloat(document.getElementById('test-principal').value);
            var rate = parseFloat(document.getElementById('test-rate').value);
            var years = parseFloat(document.getElementById('test-years').value);
            
            var monthlyPayment = calculateMortgage(principal, rate, years);
            var totalInterest = calculateTotalInterest(monthlyPayment, years, principal);
            var totalCost = principal + totalInterest;
            
            var results = [];
            results.push("Miesięczna rata: " + formatAsMoney(monthlyPayment));
            results.push("Całkowite odsetki: " + formatAsMoney(totalInterest));
            results.push("Całkowity koszt: " + formatAsMoney(totalCost));
            results.push("Stosunek odsetek do kapitału: " + ((totalInterest/principal)*100).toFixed(2) + "%");
            
            document.getElementById('test3-results').innerHTML = results.join('<br>');
        }
        
        // Test zdolności kredytowej
        function testCreditCapacity() {
            var income = parseFloat(document.getElementById('test-income').value);
            var expenses = parseFloat(document.getElementById('test-expenses').value);
            var family = parseFloat(document.getElementById('test-family').value);
            
            var capacity = calculateCreditCapacity(income, 0, expenses, family, 25, 7.25);
            
            var results = [];
            results.push("Maksymalny kredyt: " + formatAsMoney(capacity.maxLoanAmount));
            results.push("Miesięczna rata: " + formatAsMoney(capacity.monthlyPayment));
            results.push("Wskaźnik DTI: " + capacity.dtiRatio.toFixed(2) + "%");
            results.push("Dostępne dochody: " + formatAsMoney(capacity.availableIncome));
            results.push("Całkowite dochody: " + formatAsMoney(capacity.totalIncome));
            results.push("Całkowite wydatki: " + formatAsMoney(capacity.totalExpenses));
            
            document.getElementById('test4-results').innerHTML = results.join('<br>');
        }
        
        // Test nadpłat
        function testPrepaymentCalculation() {
            var balance = parseFloat(document.getElementById('test-balance').value);
            var payment = parseFloat(document.getElementById('test-payment').value);
            var prepayment = parseFloat(document.getElementById('test-prepayment').value);
            var remaining = parseFloat(document.getElementById('test-remaining').value);
            
            var effect = calculatePrepaymentEffect(balance, payment, 7.25, remaining, prepayment, 'monthly');
            
            var results = [];
            results.push("Oszczędności całkowite: " + formatAsMoney(effect.savings));
            results.push("Oszczędności na odsetkach: " + formatAsMoney(effect.interestSavings));
            results.push("Skrócenie okresu: " + Math.floor(effect.timeSavedMonths/12) + " lat " + (effect.timeSavedMonths%12) + " miesięcy");
            results.push("Całkowite nadpłaty: " + formatAsMoney(effect.totalPrepayments));
            results.push("Efektywność: " + (effect.interestSavings/effect.totalPrepayments).toFixed(2) + " zł oszczędności na 1 zł nadpłaty");
            
            document.getElementById('test5-results').innerHTML = results.join('<br>');
        }
        
        // Test walidacji
        function testValidation() {
            var input = document.getElementById('test-validation');
            var value = input.value;
            
            var results = [];
            results.push("Wartość: '" + value + "'");
            results.push("isNumber: " + isNumber(value));
            results.push("isInteger: " + isInteger(value));
            
            // Test walidacji pola
            iptfieldCheck(input, 'r', 'n');
            
            document.getElementById('test6-results').innerHTML = results.join('<br>');
        }
        
        // Uruchom testy po załadowaniu strony
        document.addEventListener('DOMContentLoaded', function() {
            runBasicTests();
            runMoneyFormatTests();
            
            // Test automatycznego obliczania
            setTimeout(function() {
                testMortgageCalculation();
                testCreditCapacity();
                testPrepaymentCalculation();
            }, 1000);
        });
    </script>
</body>
</html>
