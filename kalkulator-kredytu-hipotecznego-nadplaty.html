<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego nadpłaty</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego nadpłaty - oblicz oszczędności z nadpłat kredytu mieszkaniowego, sprawdź wpływ nadpłat na okres spłaty i całkowity koszt kredytu.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego nadpłaty, nadpłata kredytu mieszkaniowego, oszczędności nadpłat, skrócenie okresu kredytu">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-nadplaty.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego nadpłaty</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego nadpłaty</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz oszczędności z nadpłat kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-nadplaty.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostała kwota kredytu do spłaty. Sprawdź w ostatnim wyciągu bankowym lub harmonogramie spłat.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentbalance" id="ccurrentbalance" value="420000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Aktualna miesięczna rata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wysokość obecnej miesięcznej raty kredytu (kapitał + odsetki).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentpayment" id="ccurrentpayment" value="3250" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego. Sprawdź w umowie lub wyciągu bankowym.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.8" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba lat pozostałych do końca spłaty kredytu według obecnego harmonogramu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cremainingterm" id="cremainingterm" value="20" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ nadpłaty</td>
                                <td align="left" colspan="2">
                                    <select name="coverpaymenttype" id="coverpaymenttype" onchange="updateOverpaymentType();">
                                        <option value="monthly" selected="">Miesięczna nadpłata</option>
                                        <option value="yearly">Roczna nadpłata</option>
                                        <option value="onetime">Jednorazowa nadpłata</option>
                                        <option value="mixed">Nadpłaty mieszane</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="monthlyoverpayment">
                                <td align="right">Miesięczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota płacona każdego miesiąca ponad obowiązkową ratę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyoverpayment" id="cmonthlyoverpayment" value="500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="yearlyoverpayment" style="display:none;">
                                <td align="right">Roczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota płacona raz w roku (np. z premii rocznej, zwrotu podatku).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cyearlyoverpayment" id="cyearlyoverpayment" value="8000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="onetimeoverpayment" style="display:none;">
                                <td align="right">Jednorazowa nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Jednorazowa większa nadpłata kredytu (np. ze sprzedaży inwestycji, spadku).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="conetimeoverpayment" id="conetimeoverpayment" value="50000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="mixedoverpayments" style="display:none;">
                                <td align="right">Miesięczna + roczna <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kombinacja miesięcznych nadpłat z dodatkowymi rocznymi nadpłatami.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right">
                                    <input type="text" name="cmixedmonthly" id="cmixedmonthly" value="300" class="in4char indollar"> + 
                                    <input type="text" name="cmixedyearly" id="cmixedyearly" value="5000" class="in4char indollar">
                                </td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Efekt nadpłaty</td>
                                <td align="left" colspan="2">
                                    <select name="coverpaymenteffect" id="coverpaymenteffect">
                                        <option value="reduce_term" selected="">Skrócenie okresu spłaty</option>
                                        <option value="reduce_payment">Zmniejszenie miesięcznej raty</option>
                                        <option value="mixed_effect">Częściowo okres, częściowo rata</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddoverpaymentfees" class="cbcontainer">
                                        <input type="checkbox" name="caddoverpaymentfees" id="caddoverpaymentfees" value="1" onclick="cshoverpaymentfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="coverpaymentfeesdesc">Prowizje za nadpłaty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="coverpaymentfees" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty za nadpłaty</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja za nadpłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja pobierana przez bank za nadpłatę kredytu. Sprawdź w umowie kredytowej.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="coverpaymentfee" id="coverpaymentfee" value="1.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Maksymalna prowizja <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Górny limit prowizji za nadpłatę określony w umowie kredytowej.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cmaxoverpaymentfee" id="cmaxoverpaymentfee" value="10000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Darmowe nadpłaty rocznie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba darmowych nadpłat w roku (bez prowizji). Sprawdź warunki umowy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cfreeoverpayments" id="cfreeoverpayments" value="2" class="innormal"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddscenarios" class="cbcontainer">
                                        <input type="checkbox" name="caddscenarios" id="caddscenarios" value="1" checked="" onclick="cshscenarios();">
                                        <span class="cbmark"></span>
                                        <b><span id="cscenariosdesc">Porównanie scenariuszy</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cscenarios" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Alternatywne scenariusze</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 1 - nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwszy alternatywny scenariusz nadpłaty do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario1" id="cscenario1" value="300" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 2 - nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Drugi alternatywny scenariusz nadpłaty do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario2" id="cscenario2" value="800" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Scenariusz 3 - nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Trzeci alternatywny scenariusz nadpłaty do porównania.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cscenario3" id="cscenario3" value="1200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz nadpłaty kredytu" onclick="calculateLoanOverpayments();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędność: &nbsp; 142 850 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBuYWRwxYJhdHk=', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBuYWRwxYJhdHk=', 'T3N6Y3rEmWRub8WbxIc=', 'MTQyIDg1MCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez nadpłat</b></td>
                                        <td align="right"><b>Z nadpłatami</b></td>
                                        <td align="right"><b>Oszczędność</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>3 250 zł</b></td>
                                        <td align="right"><b>3 750 zł</b></td>
                                        <td align="right"><b>+500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty</td>
                                        <td align="right">20 lat</td>
                                        <td align="right">15 lat 8 miesięcy</td>
                                        <td align="right">4 lata 4 miesiące</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">780 000 zł</td>
                                        <td align="right">702 150 zł</td>
                                        <td align="right">77 850 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">360 000 zł</td>
                                        <td align="right">282 150 zł</td>
                                        <td align="right">77 850 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Łączne nadpłaty</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">93 600 zł</td>
                                        <td align="right">-93 600 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Netto oszczędność</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                        <td align="right">142 850 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza nadpłat kredytu:</h3>
                                            <p><strong>Efektywność nadpłat:</strong> Każda złotówka nadpłaty oszczędza 1,53 zł odsetek</p>
                                            <p><strong>ROI nadpłat:</strong> 153% zwrotu z inwestycji</p>
                                            <p><strong>Skrócenie okresu:</strong> 4 lata 4 miesiące wcześniejsze zakończenie</p>
                                            <p><strong>Miesięczna oszczędność:</strong> Po spłacie kredytu: 3 250 zł/miesiąc</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Nadpłaty są bardzo opłacalne przy tym oprocentowaniu</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie scenariuszy nadpłat</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Nadpłata miesięczna</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">80K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">120K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">160K zł</text>
                    
                    <!-- 0 zł -->
                    <rect x="60" y="145" width="35" height="0" fill="#e74c3c" opacity="0.8"></rect>
                    <text x="77" y="155" class="mcllabelx">0 zł</text>
                    <text x="77" y="140" class="mcllabelx" style="fill:#000;">0 zł</text>
                    
                    <!-- 300 zł -->
                    <rect x="105" y="115" width="35" height="30" fill="#f39c12" opacity="0.8"></rect>
                    <text x="122" y="155" class="mcllabelx">300 zł</text>
                    <text x="122" y="110" class="mcllabelx" style="fill:#000;">85K zł</text>
                    
                    <!-- 500 zł -->
                    <rect x="150" y="85" width="35" height="60" fill="#27ae60" opacity="0.8"></rect>
                    <text x="167" y="155" class="mcllabelx">500 zł</text>
                    <text x="167" y="80" class="mcllabelx" style="fill:#000;">143K zł</text>
                    
                    <!-- 800 zł -->
                    <rect x="195" y="55" width="35" height="90" fill="#3498db" opacity="0.8"></rect>
                    <text x="212" y="155" class="mcllabelx">800 zł</text>
                    <text x="212" y="50" class="mcllabelx" style="fill:#000;">185K zł</text>
                    
                    <!-- 1200 zł -->
                    <rect x="240" y="25" width="35" height="120" fill="#9b59b6" opacity="0.8"></rect>
                    <text x="257" y="155" class="mcllabelx">1200 zł</text>
                    <text x="257" y="20" class="mcllabelx" style="fill:#000;">225K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Scenariusze nadpłat &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram nadpłat</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Nadpłata</th>
                            <th>Okres spłaty</th>
                            <th>Całkowity koszt</th>
                            <th>Oszczędność</th>
                            <th>ROI</th>
                        </tr>
                        <tr>
                            <td>0 zł</td>
                            <td>20 lat</td>
                            <td>780 000 zł</td>
                            <td>0 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>300 zł</td>
                            <td>17 lat 2 miesiące</td>
                            <td>695 200 zł</td>
                            <td>84 800 zł</td>
                            <td>145%</td>
                        </tr>
                        <tr>
                            <td>500 zł</td>
                            <td>15 lat 8 miesięcy</td>
                            <td>637 150 zł</td>
                            <td>142 850 zł</td>
                            <td>153%</td>
                        </tr>
                        <tr>
                            <td>800 zł</td>
                            <td>13 lat 10 miesięcy</td>
                            <td>595 200 zł</td>
                            <td>184 800 zł</td>
                            <td>158%</td>
                        </tr>
                        <tr>
                            <td>1200 zł</td>
                            <td>12 lat 4 miesiące</td>
                            <td>555 600 zł</td>
                            <td>224 400 zł</td>
                            <td>162%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego nadpłaty - maksymalizuj oszczędności 2025</h2>
            <p>Nadpłaty kredytu hipotecznego to jeden z najefektywniejszych sposobów oszczędzania na kosztach kredytu mieszkaniowego. Nasz kalkulator kredytu hipotecznego nadpłaty pomoże Ci precyzyjnie obliczyć oszczędności, porównać różne scenariusze nadpłat i wybrać optymalną strategię spłaty kredytu.</p>

            <h3>Rodzaje nadpłat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Miesięczne nadpłaty:</h4>
                <ul>
                    <li><strong>Regularne dopłaty</strong> - stała kwota każdego miesiąca</li>
                    <li><strong>Przewidywalność</strong> - łatwe planowanie budżetu domowego</li>
                    <li><strong>Największy efekt</strong> - systematyczne zmniejszanie salda kredytu</li>
                    <li><strong>Dyscyplina finansowa</strong> - wymaga stałego zaangażowania</li>
                    <li><strong>Optymalne dla stabilnych dochodów</strong> - pracownicy etatowi</li>
                </ul>

                <h4>Roczne nadpłaty:</h4>
                <ul>
                    <li><strong>Jednorazowe dopłaty</strong> - raz w roku większa kwota</li>
                    <li><strong>Źródła finansowania</strong> - premia roczna, zwrot podatku, 13. pensja</li>
                    <li><strong>Elastyczność</strong> - możliwość dostosowania do sytuacji finansowej</li>
                    <li><strong>Mniejszy efekt</strong> - niż regularne miesięczne nadpłaty</li>
                    <li><strong>Łatwiejsze do realizacji</strong> - nie obciąża miesięcznego budżetu</li>
                </ul>

                <h4>Jednorazowe nadpłaty:</h4>
                <ul>
                    <li><strong>Duże kwoty</strong> - ze sprzedaży inwestycji, spadku, premii</li>
                    <li><strong>Znaczący wpływ</strong> - drastyczne zmniejszenie salda kredytu</li>
                    <li><strong>Okazjonalne</strong> - zależne od nietypowych dochodów</li>
                    <li><strong>Wysokie oszczędności</strong> - szczególnie na początku kredytu</li>
                    <li><strong>Ryzyko prowizji</strong> - sprawdź warunki umowy kredytowej</li>
                </ul>

                <h4>Nadpłaty mieszane:</h4>
                <ul>
                    <li><strong>Kombinacja strategii</strong> - miesięczne + roczne nadpłaty</li>
                    <li><strong>Maksymalne oszczędności</strong> - najefektywniejsza metoda</li>
                    <li><strong>Elastyczność</strong> - dostosowanie do zmiennych dochodów</li>
                    <li><strong>Wymaga planowania</strong> - koordynacja różnych źródeł nadpłat</li>
                </ul>
            </div>

            <h3>Efekty nadpłat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Skrócenie okresu spłaty:</h4>
                <ul>
                    <li><strong>Wcześniejsze zakończenie</strong> - kredyt spłacony przed terminem</li>
                    <li><strong>Zachowanie raty</strong> - miesięczna rata pozostaje bez zmian</li>
                    <li><strong>Maksymalne oszczędności</strong> - największa redukcja odsetek</li>
                    <li><strong>Szybsza wolność finansowa</strong> - brak zobowiązań kredytowych</li>
                    <li><strong>Rekomendowane</strong> - przy stabilnych dochodach</li>
                </ul>

                <h4>Zmniejszenie miesięcznej raty:</h4>
                <ul>
                    <li><strong>Niższa rata</strong> - zmniejszenie miesięcznych zobowiązań</li>
                    <li><strong>Zachowanie okresu</strong> - kredyt spłacany do pierwotnego terminu</li>
                    <li><strong>Mniejsze oszczędności</strong> - niż przy skróceniu okresu</li>
                    <li><strong>Większa elastyczność</strong> - więcej środków w budżecie miesięcznym</li>
                    <li><strong>Rekomendowane</strong> - przy niestabilnych dochodach</li>
                </ul>

                <h4>Efekt mieszany:</h4>
                <ul>
                    <li><strong>Częściowe skrócenie</strong> - okres i rata zmniejszane proporcjonalnie</li>
                    <li><strong>Kompromis</strong> - między oszczędnościami a elastycznością</li>
                    <li><strong>Dostosowanie</strong> - do zmieniającej się sytuacji życiowej</li>
                </ul>
            </div>

            <h3>Opłacalność nadpłat - analiza ROI:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie kredytu</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">ROI nadpłat</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Opłacalność</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Rekomendacja</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">5.0% - 6.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">120% - 135%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Średnia</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rozważ inwestycje</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">6.0% - 7.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">135% - 150%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Dobra</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Nadpłacaj umiarkowanie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.0% - 8.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">150% - 165%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo dobra</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">✓ Nadpłacaj aktywnie</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">8.0% - 9.0%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">165% - 180%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Doskonała</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">✓ Maksymalizuj nadpłaty</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">9.0%+</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">180%+</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wyjątkowa</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">✓ Priorytet nr 1</td>
                    </tr>
                </table>
            </div>

            <h3>Prowizje za nadpłaty kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Rodzaje prowizji bankowych:</h4>
                <ul>
                    <li><strong>Prowizja procentowa</strong> - 0.5% - 3% nadpłacanej kwoty</li>
                    <li><strong>Prowizja stała</strong> - 100 - 500 zł za każdą nadpłatę</li>
                    <li><strong>Prowizja progresywna</strong> - wzrasta z wielkością nadpłaty</li>
                    <li><strong>Brak prowizji</strong> - w niektórych bankach lub promocjach</li>
                </ul>

                <h4>Limity darmowych nadpłat:</h4>
                <ul>
                    <li><strong>Roczny limit kwotowy</strong> - np. 50 000 zł rocznie bez prowizji</li>
                    <li><strong>Limit liczby nadpłat</strong> - np. 2-4 darmowe nadpłaty rocznie</li>
                    <li><strong>Procentowy limit</strong> - np. 25% salda kredytu rocznie</li>
                    <li><strong>Minimalna kwota</strong> - np. minimum 1000 zł za nadpłatę</li>
                </ul>

                <h4>Strategie minimalizacji prowizji:</h4>
                <ul>
                    <li><strong>Wykorzystaj darmowe limity</strong> - maksymalizuj bezpłatne nadpłaty</li>
                    <li><strong>Planuj większe nadpłaty</strong> - zamiast częstych małych</li>
                    <li><strong>Sprawdź promocje</strong> - banki czasem znoszą prowizje</li>
                    <li><strong>Rozważ refinansowanie</strong> - do banku bez prowizji</li>
                </ul>
            </div>

            <h3>Strategie nadpłat w różnych sytuacjach życiowych:</h3>
            <div style="margin: 15px 0;">
                <h4>Młodzi kredytobiorcy (25-35 lat):</h4>
                <ul>
                    <li><strong>Agresywne nadpłaty</strong> - maksymalizacja oszczędności</li>
                    <li><strong>Skrócenie okresu</strong> - wcześniejsza wolność finansowa</li>
                    <li><strong>Miesięczne nadpłaty</strong> - budowanie dyscypliny finansowej</li>
                    <li><strong>Rezerwa na rozwój</strong> - zachowanie środków na inwestycje w karierę</li>
                </ul>

                <h4>Rodziny z dziećmi (35-45 lat):</h4>
                <ul>
                    <li><strong>Umiarkowane nadpłaty</strong> - równowaga z wydatkami na dzieci</li>
                    <li><strong>Zmniejszenie raty</strong> - większa elastyczność budżetu</li>
                    <li><strong>Roczne nadpłaty</strong> - z premii i zwrotów podatkowych</li>
                    <li><strong>Rezerwa na edukację</strong> - przyszłe wydatki na dzieci</li>
                </ul>

                <h4>Przed emeryturą (45-60 lat):</h4>
                <ul>
                    <li><strong>Intensywne nadpłaty</strong> - spłata przed emeryturą</li>
                    <li><strong>Skrócenie okresu</strong> - brak zobowiązań na emeryturze</li>
                    <li><strong>Wykorzystanie oszczędności</strong> - inwestycja w bezpieczeństwo</li>
                    <li><strong>Planowanie spadku</strong> - wolna nieruchomość dla dzieci</li>
                </ul>
            </div>

            <h3>Nadpłaty vs alternatywne inwestycje:</h3>
            <ul>
                <li><strong>Nadpłaty kredytu (7.8% ROI)</strong> - gwarantowany zwrot, brak ryzyka</li>
                <li><strong>Lokaty bankowe (3-5%)</strong> - niższy zwrot niż nadpłaty</li>
                <li><strong>Obligacje skarbowe (4-6%)</strong> - niższy zwrot, ale płynność</li>
                <li><strong>Fundusze inwestycyjne (6-12%)</strong> - wyższy potencjał, ale ryzyko</li>
                <li><strong>Akcje (8-15%)</strong> - najwyższy potencjał, najwyższe ryzyko</li>
                <li><strong>Nieruchomości (5-10%)</strong> - dywersyfikacja, ale niższa płynność</li>
            </ul>

            <h3>Porady ekspertów dotyczące nadpłat:</h3>
            <ul>
                <li>Zawsze zachowaj rezerwę finansową na 6-12 miesięcy wydatków</li>
                <li>Nadpłacaj kredyt tylko przy oprocentowaniu powyżej 6-7%</li>
                <li>Sprawdź warunki prowizji za nadpłaty w umowie kredytowej</li>
                <li>Rozważ nadpłaty jako alternatywę dla lokat bankowych</li>
                <li>Planuj nadpłaty systematycznie, a nie sporadycznie</li>
                <li>Monitoruj zmiany oprocentowania - może wpłynąć na opłacalność</li>
                <li>Dokumentuj wszystkie nadpłaty dla celów podatkowych</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Nadpłata kredytu kalkulator</a>
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">Wcześniejsza spłata</a>
                <a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Scenariusze nadpłat</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz nadpłatę:</strong><br>
                        <a href="#" onclick="return setOverpayment('300');">300 zł (85K oszczędność)</a><br>
                        <a href="#" onclick="return setOverpayment('500');">500 zł (143K oszczędność)</a><br>
                        <a href="#" onclick="return setOverpayment('800');">800 zł (185K oszczędność)</a><br>
                        <a href="#" onclick="return setOverpayment('1200');">1200 zł (225K oszczędność)</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego nadpłaty - oblicz oszczędności z nadpłat kredytu mieszkaniowego i sprawdź wpływ na okres spłaty.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateOverpaymentType() {
    var type = document.getElementById('coverpaymenttype').value;
    var monthly = document.getElementById('monthlyoverpayment');
    var yearly = document.getElementById('yearlyoverpayment');
    var onetime = document.getElementById('onetimeoverpayment');
    var mixed = document.getElementById('mixedoverpayments');
    
    monthly.style.display = (type === 'monthly') ? 'table-row' : 'none';
    yearly.style.display = (type === 'yearly') ? 'table-row' : 'none';
    onetime.style.display = (type === 'onetime') ? 'table-row' : 'none';
    mixed.style.display = (type === 'mixed') ? 'table-row' : 'none';
}

function cshoverpaymentfees() {
    var checkbox = document.getElementById('caddoverpaymentfees');
    var feesDiv = document.getElementById('coverpaymentfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshscenarios() {
    var checkbox = document.getElementById('caddscenarios');
    var scenariosDiv = document.getElementById('cscenarios');
    if (checkbox.checked) {
        scenariosDiv.style.display = 'block';
    } else {
        scenariosDiv.style.display = 'none';
    }
}

function setOverpayment(amount) {
    document.getElementById('cmonthlyoverpayment').value = amount;
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram nadpłat &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Scenariusze nadpłat</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Scenariusze nadpłat &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram nadpłat</a>';
    }
    return false;
}
</script>

<script>
function calculateLoanOverpayments() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 获取nadpłaty信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;
    var overpaymentType = document.getElementById('coverpaymenttype').value;

    // 获取高级选项
    var overpaymentInc = parseFloat(document.getElementById('coverpaymentinc').value) || 0;
    var maxOverpayment = parseFloat(document.getElementById('cmaxoverpayment').value) || 0;
    var overpaymentBreak = parseFloat(document.getElementById('coverpaymentbreak').value) || 0;
    var overpaymentFee = parseFloat(document.getElementById('coverpaymentfee').value) || 0;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算没有nadpłaty的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;

    // 计算有nadpłaty的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;
    var totalFees = 0;
    var currentOverpayment = overpaymentAmount;

    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;

        // 添加nadpłaty
        var thisMonthOverpayment = 0;
        if (monthsPaid >= overpaymentBreak) {
            if (overpaymentFreq === 'monthly' ||
               (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
               (overpaymentFreq === 'once' && monthsPaid === 0)) {
                thisMonthOverpayment = currentOverpayment;

                // 应用最大限制
                if (maxOverpayment > 0 && thisMonthOverpayment > maxOverpayment) {
                    thisMonthOverpayment = maxOverpayment;
                }

                // 计算费用
                var fee = thisMonthOverpayment * (overpaymentFee / 100);
                totalFees += fee;

                // 年度增长
                if (overpaymentFreq === 'monthly' && monthsPaid % 12 === 11) {
                    currentOverpayment *= (1 + overpaymentInc / 100);
                }
            }
        }

        // 更新余额
        remainingBalance -= (monthlyPrincipal + thisMonthOverpayment);
        totalPaid += monthlyPayment + thisMonthOverpayment + fee;
        totalOverpayments += thisMonthOverpayment;
        monthsPaid++;

        if (remainingBalance <= 0) break;
    }

    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments - totalFees);

    // 计算效率
    var efficiency = totalOverpayments > 0 ? (moneySaved / totalOverpayments) * 100 : 0;

    // 更新显示
    updateLoanOverpaymentsResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                totalFees, efficiency, loanTerm, overpaymentType);
}

function updateLoanOverpaymentsResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                     timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                     totalFees, efficiency, loanTerm, overpaymentType) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Nadpłaty kredytu: &nbsp; ' + formatNumber(moneySaved) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 4) {
                switch(i) {
                    case 2: // Czas spłaty
                        cells[1].innerHTML = '<b>' + loanTerm + ' lat</b>';
                        cells[2].innerHTML = '<b>' + formatTime(monthsPaid) + '</b>';
                        cells[3].innerHTML = '<b>' + formatTime(timeSaved) + '</b>';
                        break;
                    case 3: // Miesięczna rata
                        cells[1].innerHTML = formatNumber(monthlyPayment) + ' zł';
                        cells[2].innerHTML = formatNumber(monthlyPayment + (totalOverpayments/monthsPaid)) + ' zł';
                        cells[3].innerHTML = '+' + formatNumber(totalOverpayments/monthsPaid) + ' zł';
                        break;
                    case 4: // Łączne spłaty
                        cells[1].innerHTML = formatNumber(totalWithoutOverpayment) + ' zł';
                        cells[2].innerHTML = formatNumber(totalPaid) + ' zł';
                        cells[3].innerHTML = formatNumber(moneySaved) + ' zł';
                        break;
                    case 5: // Odsetki łącznie
                        cells[1].innerHTML = formatNumber(totalWithoutOverpayment - parseFloat(document.getElementById('cloanamount').value)) + ' zł';
                        cells[2].innerHTML = formatNumber((totalPaid - parseFloat(document.getElementById('cloanamount').value) - totalOverpayments - totalFees)) + ' zł';
                        cells[3].innerHTML = formatNumber(interestSaved) + ' zł';
                        break;
                    case 6: // Łączne nadpłaty
                        cells[1].innerHTML = '0 zł';
                        cells[2].innerHTML = formatNumber(totalOverpayments) + ' zł';
                        cells[3].innerHTML = '-';
                        break;
                    case 7: // Opłaty za nadpłaty
                        cells[1].innerHTML = '0 zł';
                        cells[2].innerHTML = formatNumber(totalFees) + ' zł';
                        cells[3].innerHTML = formatNumber(totalFees) + ' zł';
                        break;
                }
            }
        }
    }

    // 更新analiza nadpłat kredytu
    var analysisSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (analysisSection) {
        var monthlyAfterPayoff = monthlyPayment + (totalOverpayments/monthsPaid);
        analysisSection.innerHTML =
            '<h3>Analiza nadpłat kredytu hipotecznego:</h3>' +
            '<p><strong>Efektywność nadpłat:</strong> ' + efficiency.toFixed(0) + '% (każda złotówka nadpłaty oszczędza ' + (efficiency/100).toFixed(2) + ' zł odsetek)</p>' +
            '<p><strong>Typ nadpłaty:</strong> ' + (overpaymentType === 'reduce_time' ? 'Skrócenie okresu spłaty' : 'Zmniejszenie miesięcznej raty') + '</p>' +
            '<p><strong>Skrócenie okresu:</strong> ' + formatTime(timeSaved) + ' (' + Math.round((timeSaved/(loanTerm*12))*100) + '% pierwotnego okresu)</p>' +
            '<p><strong>Redukcja odsetek:</strong> ' + formatNumber(interestSaved) + ' zł (' + Math.round((interestSaved/(totalWithoutOverpayment - parseFloat(document.getElementById('cloanamount').value)))*100) + '% pierwotnych odsetek)</p>' +
            '<p><strong>Miesięczne oszczędności:</strong> ' + formatNumber(monthlyAfterPayoff) + ' zł (po spłacie kredytu)</p>' +
            '<p><strong>Rekomendacja:</strong> ' + getOverpaymentRecommendation(efficiency) + '</p>';
    }
}

function getOverpaymentRecommendation(efficiency) {
    if (efficiency > 180) {
        return 'Nadpłaty bardzo opłacalne przy tym oprocentowaniu';
    } else if (efficiency > 150) {
        return 'Nadpłaty opłacalne, rozważ zwiększenie kwoty';
    } else if (efficiency > 120) {
        return 'Nadpłaty umiarkowanie opłacalne';
    } else {
        return 'Rozważ inne formy inwestycji zamiast nadpłat';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('coverpayment')) document.getElementById('coverpayment').value = '500';
    if (document.getElementById('coverpaymentfreq')) document.getElementById('coverpaymentfreq').value = 'monthly';
    if (document.getElementById('coverpaymenttype')) document.getElementById('coverpaymenttype').value = 'reduce_time';

    if (document.getElementById('coverpaymentinc')) document.getElementById('coverpaymentinc').value = '0';
    if (document.getElementById('cmaxoverpayment')) document.getElementById('cmaxoverpayment').value = '0';
    if (document.getElementById('coverpaymentbreak')) document.getElementById('coverpaymentbreak').value = '0';
    if (document.getElementById('coverpaymentfee')) document.getElementById('coverpaymentfee').value = '0';

    calculateLoanOverpayments();
}

function saveCalResult() {
    alert('Wyniki kalkulatora nadpłat kredytu zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateLoanOverpayments();
};
</script>

</body>
</html>
