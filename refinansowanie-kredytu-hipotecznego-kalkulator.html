<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Refinansowanie kredytu hipotecznego kalkulator</title>
    <meta name="description" content="Refinansowanie kredytu hipotecznego kalkulator - oblicz oszczędności z refinansowania, porównaj oferty banków i sprawdź opłacalność zmiany kredytu mieszkaniowego.">
    <meta name="keywords" content="refinansowanie kredytu hipotecznego kalkulator, zmiana banku kredytu, przeniesienie kredytu hipotecznego, oszczędności refinansowania">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html" itemprop="item"><span itemprop="name">refinansowanie kredytu hipotecznego kalkulator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Refinansowanie kredytu hipotecznego kalkulator</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz oszczędności z refinansowania kredytu hipotecznego"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/refinansowanie-kredytu-hipotecznego-kalkulator.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostała kwota kredytu do spłaty w obecnym banku. Sprawdź w ostatnim wyciągu bankowym.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentbalance" id="ccurrentbalance" value="380000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Aktualna miesięczna rata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wysokość obecnej miesięcznej raty w aktualnym banku (kapitał + odsetki).', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentpayment" id="ccurrentpayment" value="2950" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Aktualne oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obecne oprocentowanie kredytu w aktualnym banku. Sprawdź w umowie lub wyciągu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ccurrentrate" id="ccurrentrate" value="8.2" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres spłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba lat pozostałych do końca spłaty kredytu według obecnego harmonogramu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cremainingterm" id="cremainingterm" value="18" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Nowe oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Oprocentowanie oferowane przez nowy bank. Sprawdź aktualne oferty refinansowania.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cnewrate" id="cnewrate" value="7.1" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Nowy bank</td>
                                <td align="left" colspan="2">
                                    <select name="cnewbank" id="cnewbank" onchange="updateNewBankRate();">
                                        <option value="custom" selected="">Własne oprocentowanie</option>
                                        <option value="ing">ING Bank Śląski</option>
                                        <option value="mbank">mBank</option>
                                        <option value="santander">Santander Bank</option>
                                        <option value="millennium">Bank Millennium</option>
                                        <option value="alior">Alior Bank</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Nowy okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty w nowym banku. Może być taki sam lub wydłużony dla obniżenia raty.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cnewterm" id="cnewterm" value="18" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddrefinancingcosts" class="cbcontainer">
                                        <input type="checkbox" name="caddrefinancingcosts" id="caddrefinancingcosts" value="1" checked="" onclick="cshrefinancingcosts();">
                                        <span class="cbmark"></span>
                                        <b><span id="crefinancingcostsdesc">Koszty refinansowania</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="crefinancingcosts" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Koszty zmiany banku</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja nowego banku <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja przygotowawcza w nowym banku za udzielenie kredytu refinansowego.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cnewbankfee" id="cnewbankfee" value="1.5" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Opłata za wcześniejszą spłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja w obecnym banku za wcześniejszą spłatę kredytu przy refinansowaniu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cearlypaymentfee" id="cearlypaymentfee" value="1.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Koszty notarialne <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszty notariusza za przeniesienie hipoteki i podpisanie nowej umowy kredytowej.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cnotarialcosts" id="cnotarialcosts" value="3500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt nowej wyceny nieruchomości przez rzeczoznawcę nowego banku.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cvaluationcost" id="cvaluationcost" value="1200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne koszty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: ubezpieczenia, opłaty sądowe, koszty prawne.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cothercosts" id="cothercosts" value="2000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpromotions" class="cbcontainer">
                                        <input type="checkbox" name="caddpromotions" id="caddpromotions" value="1" onclick="cshpromotions();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpromotionsdesc">Promocje refinansowania</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpromotions" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Cashback od nowego banku <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Zwrot gotówki oferowany przez nowy bank za przeniesienie kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ccashback" id="ccashback" value="5000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zwolnienie z prowizji <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Procent zwolnienia z prowizji przygotowawczej w nowym banku.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cfeewaiver" id="cfeewaiver" value="50" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Darmowa wycena <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czy nowy bank oferuje darmową wycenę nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="left" colspan="2">
                                                        <select name="cfreevaluation" id="cfreevaluation">
                                                            <option value="0" selected="">Płatna wycena</option>
                                                            <option value="1">Darmowa wycena</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz refinansowanie" onclick="calculateRefinancing();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędność: &nbsp; 89 450 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('UmVmaW5hbnNvd2FuaWUga3JlZHl0dSBoaXBvdGVjem5lZ28=', 0, 'UmVmaW5hbnNvd2FuaWUga3JlZHl0dSBoaXBvdGVjem5lZ28=', 'T3N6Y3rEmWRub8WbxIc=', 'ODkgNDUwIHrFgw==');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Obecny bank</b></td>
                                        <td align="right"><b>Nowy bank</b></td>
                                        <td align="right"><b>Różnica</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Miesięczna rata</b></td>
                                        <td align="right"><b>2 950 zł</b></td>
                                        <td align="right"><b>2 685 zł</b></td>
                                        <td align="right"><b>-265 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Oprocentowanie</td>
                                        <td align="right">8.2%</td>
                                        <td align="right">7.1%</td>
                                        <td align="right">-1.1 p.p.</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowity koszt</td>
                                        <td align="right">637 200 zł</td>
                                        <td align="right">577 800 zł</td>
                                        <td align="right">-59 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">257 200 zł</td>
                                        <td align="right">197 800 zł</td>
                                        <td align="right">-59 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Koszty refinansowania</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">13 950 zł</td>
                                        <td align="right">+13 950 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Netto oszczędność</td>
                                        <td align="right">-</td>
                                        <td align="right">-</td>
                                        <td align="right">45 450 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza refinansowania:</h3>
                                            <p><strong>Opłacalność:</strong> ✓ Refinansowanie jest opłacalne</p>
                                            <p><strong>Okres zwrotu:</strong> 4 lata 5 miesięcy</p>
                                            <p><strong>Miesięczna oszczędność:</strong> 265 zł niższa rata</p>
                                            <p><strong>Redukcja oprocentowania:</strong> 1.1 punktu procentowego</p>
                                            <p><strong>Rekomendacja:</strong> ✓ Warto rozważyć refinansowanie</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie ofert refinansowania banków</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Bank</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">40K zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">60K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">80K zł</text>
                    
                    <!-- ING -->
                    <rect x="60" y="65" width="35" height="80" fill="#ff6600" opacity="0.8"></rect>
                    <text x="77" y="155" class="mcllabelx">ING</text>
                    <text x="77" y="60" class="mcllabelx" style="fill:#000;">52K zł</text>
                    
                    <!-- mBank -->
                    <rect x="105" y="75" width="35" height="70" fill="#00a651" opacity="0.8"></rect>
                    <text x="122" y="155" class="mcllabelx">mBank</text>
                    <text x="122" y="70" class="mcllabelx" style="fill:#000;">48K zł</text>
                    
                    <!-- Nowy bank -->
                    <rect x="150" y="85" width="35" height="60" fill="#45b7d1" opacity="0.8"></rect>
                    <text x="167" y="155" class="mcllabelx">Nowy</text>
                    <text x="167" y="80" class="mcllabelx" style="fill:#000;">45K zł</text>
                    
                    <!-- Santander -->
                    <rect x="195" y="95" width="35" height="50" fill="#ec0000" opacity="0.8"></rect>
                    <text x="212" y="155" class="mcllabelx">Santander</text>
                    <text x="212" y="90" class="mcllabelx" style="fill:#000;">42K zł</text>
                    
                    <!-- Millennium -->
                    <rect x="240" y="105" width="35" height="40" fill="#8b0000" opacity="0.8"></rect>
                    <text x="257" y="155" class="mcllabelx">Millennium</text>
                    <text x="257" y="100" class="mcllabelx" style="fill:#000;">38K zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Oszczędności banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Analiza kosztów</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Bank</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Koszty refinansowania</th>
                            <th>Netto oszczędność</th>
                        </tr>
                        <tr>
                            <td>ING Bank Śląski</td>
                            <td>6.95%</td>
                            <td>2 625 zł</td>
                            <td>12 500 zł</td>
                            <td>52 200 zł</td>
                        </tr>
                        <tr>
                            <td>mBank</td>
                            <td>7.05%</td>
                            <td>2 645 zł</td>
                            <td>14 200 zł</td>
                            <td>48 100 zł</td>
                        </tr>
                        <tr>
                            <td>Nowy bank</td>
                            <td>7.10%</td>
                            <td>2 685 zł</td>
                            <td>13 950 zł</td>
                            <td>45 450 zł</td>
                        </tr>
                        <tr>
                            <td>Santander Bank</td>
                            <td>7.25%</td>
                            <td>2 725 zł</td>
                            <td>15 800 zł</td>
                            <td>42 300 zł</td>
                        </tr>
                        <tr>
                            <td>Bank Millennium</td>
                            <td>7.35%</td>
                            <td>2 745 zł</td>
                            <td>16 500 zł</td>
                            <td>38 200 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Refinansowanie kredytu hipotecznego kalkulator - kompletny przewodnik 2025</h2>
            <p>Refinansowanie kredytu hipotecznego to przeniesienie kredytu z jednego banku do drugiego w celu uzyskania lepszych warunków. Nasz kalkulator refinansowania kredytu hipotecznego pomoże Ci precyzyjnie obliczyć oszczędności, uwzględnić wszystkie koszty i sprawdzić opłacalność zmiany banku.</p>

            <h3>Czym jest refinansowanie kredytu hipotecznego?</h3>
            <div style="margin: 15px 0;">
                <h4>Definicja refinansowania:</h4>
                <ul>
                    <li><strong>Przeniesienie kredytu</strong> - zmiana banku kredytującego</li>
                    <li><strong>Spłata obecnego kredytu</strong> - środkami z nowego banku</li>
                    <li><strong>Nowa umowa kredytowa</strong> - na lepszych warunkach</li>
                    <li><strong>Przeniesienie hipoteki</strong> - zabezpieczenie w nowym banku</li>
                    <li><strong>Zachowanie nieruchomości</strong> - bez zmiany właściciela</li>
                </ul>

                <h4>Rodzaje refinansowania:</h4>
                <ul>
                    <li><strong>Refinansowanie proste</strong> - przeniesienie na tych samych warunkach</li>
                    <li><strong>Refinansowanie z cash-out</strong> - dodatkowe środki na cele własne</li>
                    <li><strong>Refinansowanie konsolidacyjne</strong> - łączenie kilku kredytów</li>
                    <li><strong>Refinansowanie z wydłużeniem</strong> - obniżenie raty przez wydłużenie okresu</li>
                    <li><strong>Refinansowanie z skróceniem</strong> - szybsza spłata przy niższym oprocentowaniu</li>
                </ul>
            </div>

            <h3>Kiedy warto refinansować kredyt hipoteczny?</h3>
            <div style="margin: 15px 0;">
                <h4>Sytuacje sprzyjające refinansowaniu:</h4>
                <ul>
                    <li><strong>Spadek stóp procentowych</strong> - o minimum 0.5-1 punkt procentowy</li>
                    <li><strong>Poprawa zdolności kredytowej</strong> - wyższe dochody, lepsza historia</li>
                    <li><strong>Wzrost wartości nieruchomości</strong> - niższe LTV, lepsza marża</li>
                    <li><strong>Długi pozostały okres</strong> - minimum 5-7 lat do spłaty</li>
                    <li><strong>Wysokie saldo kredytu</strong> - minimum 200-300 tys. zł</li>
                    <li><strong>Atrakcyjne promocje</strong> - cashback, zwolnienia z opłat</li>
                </ul>

                <h4>Kiedy refinansowanie nie ma sensu:</h4>
                <ul>
                    <li><strong>Niewielka różnica oprocentowania</strong> - poniżej 0.5 punktu</li>
                    <li><strong>Krótki pozostały okres</strong> - mniej niż 5 lat</li>
                    <li><strong>Niskie saldo kredytu</strong> - poniżej 150 tys. zł</li>
                    <li><strong>Wysokie koszty refinansowania</strong> - powyżej 3% salda</li>
                    <li><strong>Problemy z zdolnością</strong> - pogorszenie sytuacji finansowej</li>
                    <li><strong>Planowana sprzedaż</strong> - nieruchomości w najbliższych latach</li>
                </ul>
            </div>

            <h3>Koszty refinansowania kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Opłaty w nowym banku:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Rodzaj opłaty</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wysokość</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Uwagi</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja przygotowawcza</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-2% kwoty kredytu</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Często zwolnienie w promocjach</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wycena nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">800-1500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Czasem darmowa w promocji</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">500-1500 zł/rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Można przenieść z obecnego</td>
                    </tr>
                </table>

                <h4>Opłaty w obecnym banku:</h4>
                <ul>
                    <li><strong>Prowizja za wcześniejszą spłatę:</strong> 0-3% spłacanej kwoty</li>
                    <li><strong>Opłata za wydanie dokumentów:</strong> 50-200 zł</li>
                    <li><strong>Opłata za zamknięcie rachunku:</strong> 0-100 zł</li>
                </ul>

                <h4>Koszty zewnętrzne:</h4>
                <ul>
                    <li><strong>Notariusz:</strong> 2000-5000 zł (przeniesienie hipoteki)</li>
                    <li><strong>Sąd wieczystoksięgowy:</strong> 200-500 zł (wpis hipoteki)</li>
                    <li><strong>Doradca prawny:</strong> 1000-3000 zł (opcjonalnie)</li>
                    <li><strong>Broker kredytowy:</strong> 0.5-1% kwoty kredytu (opcjonalnie)</li>
                </ul>
            </div>

            <h3>Proces refinansowania kredytu hipotecznego:</h3>
            <ol>
                <li><strong>Analiza opłacalności</strong> - użyj naszego kalkulatora refinansowania</li>
                <li><strong>Porównanie ofert</strong> - sprawdź warunki w różnych bankach</li>
                <li><strong>Złożenie wniosku</strong> - w wybranym banku refinansującym</li>
                <li><strong>Wycena nieruchomości</strong> - przez rzeczoznawcę nowego banku</li>
                <li><strong>Decyzja kredytowa</strong> - pozytywna lub negatywna</li>
                <li><strong>Podpisanie umowy</strong> - nowej umowy kredytowej</li>
                <li><strong>Spłata starego kredytu</strong> - środkami z nowego banku</li>
                <li><strong>Przeniesienie hipoteki</strong> - u notariusza</li>
                <li><strong>Wpis nowej hipoteki</strong> - w księdze wieczystej</li>
                <li><strong>Zamknięcie starego rachunku</strong> - w poprzednim banku</li>
            </ol>

            <h3>Najlepsze banki do refinansowania 2025:</h3>
            <div style="margin: 15px 0;">
                <h4>Ranking banków refinansujących:</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Bank</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Prowizja</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Promocje</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Ocena</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>ING Bank Śląski</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6.95%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-1.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Cashback 5000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">⭐⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">mBank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.05%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0-2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Darmowa wycena</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Santander Bank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.25%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1-2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zwolnienie z prowizji</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">⭐⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bank Millennium</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.35%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1-2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Cashback 3000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">⭐⭐⭐</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Alior Bank</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1.5-2%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Promocyjna marża</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">⭐⭐⭐</td>
                    </tr>
                </table>
            </div>

            <h3>Promocje refinansowania 2025:</h3>
            <ul>
                <li><strong>ING Bank Śląski:</strong> Cashback do 5000 zł + darmowa wycena</li>
                <li><strong>mBank:</strong> 100% zwolnienie z prowizji + darmowa wycena</li>
                <li><strong>Santander Bank:</strong> Cashback 3000 zł + obniżona marża</li>
                <li><strong>Bank Millennium:</strong> 50% zwolnienie z prowizji + promocyjna marża</li>
                <li><strong>Alior Bank:</strong> Marża od 2.19% przez pierwsze 12 miesięcy</li>
                <li><strong>Credit Agricole:</strong> Cashback 4000 zł + darmowe ubezpieczenie</li>
            </ul>

            <h3>Porady ekspertów dotyczące refinansowania:</h3>
            <ul>
                <li>Refinansuj tylko przy różnicy oprocentowania minimum 0.5-1 punkt procentowy</li>
                <li>Uwzględnij wszystkie koszty refinansowania w kalkulacji opłacalności</li>
                <li>Sprawdź okres zwrotu kosztów refinansowania (powinien być poniżej 3-4 lat)</li>
                <li>Negocjuj warunki - banki często idą na ustępstwa przy refinansowaniu</li>
                <li>Rozważ refinansowanie przy okazji innych zmian (wydłużenie, konsolidacja)</li>
                <li>Sprawdź możliwość przeniesienia ubezpieczeń z obecnego banku</li>
                <li>Monitoruj rynek - najlepszy moment to spadek stóp procentowych</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-kredytu-hipotecznego-pko.html">PKO kalkulator kredytu</a>
                <a href="/ing-kalkulator-kredytu-hipotecznego.html">ING kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/wczesniejsza-splata-kredytu-hipotecznego-kalkulator.html">Wcześniejsza spłata</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Banki refinansujące</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz bank:</strong><br>
                        <a href="#" onclick="return setBank('ing');">ING (6.95%)</a><br>
                        <a href="#" onclick="return setBank('mbank');">mBank (7.05%)</a><br>
                        <a href="#" onclick="return setBank('santander');">Santander (7.25%)</a><br>
                        <a href="#" onclick="return setBank('millennium');">Millennium (7.35%)</a><br>
                        <a href="#" onclick="return setBank('alior');">Alior (7.45%)</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Refinansowanie kredytu hipotecznego kalkulator - oblicz oszczędności z refinansowania i porównaj oferty banków.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateNewBankRate() {
    var bank = document.getElementById('cnewbank').value;
    var rateField = document.getElementById('cnewrate');
    
    switch(bank) {
        case 'ing':
            rateField.value = '6.95';
            break;
        case 'mbank':
            rateField.value = '7.05';
            break;
        case 'santander':
            rateField.value = '7.25';
            break;
        case 'millennium':
            rateField.value = '7.35';
            break;
        case 'alior':
            rateField.value = '7.45';
            break;
        case 'custom':
            rateField.value = '7.10';
            break;
    }
}

function cshrefinancingcosts() {
    var checkbox = document.getElementById('caddrefinancingcosts');
    var costsDiv = document.getElementById('crefinancingcosts');
    if (checkbox.checked) {
        costsDiv.style.display = 'block';
    } else {
        costsDiv.style.display = 'none';
    }
}

function cshpromotions() {
    var checkbox = document.getElementById('caddpromotions');
    var promotionsDiv = document.getElementById('cpromotions');
    if (checkbox.checked) {
        promotionsDiv.style.display = 'block';
    } else {
        promotionsDiv.style.display = 'none';
    }
}

function setBank(bank) {
    document.getElementById('cnewbank').value = bank;
    updateNewBankRate();
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Analiza kosztów &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Oszczędności banków</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Oszczędności banków &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Analiza kosztów</a>';
    }
    return false;
}
</script>

<script>
function calculateRefinancing() {
    // 获取当前贷款信息
    var currentBalance = parseFloat(document.getElementById('ccurrentbalance').value) || 0;
    var currentRate = parseFloat(document.getElementById('ccurrentrate').value) || 0;
    var remainingTerm = parseFloat(document.getElementById('cremainingterm').value) || 0;

    // 获取新贷款信息
    var newRate = parseFloat(document.getElementById('cnewrate').value) || 0;
    var newTerm = parseFloat(document.getElementById('cnewterm').value) || 0;

    // 获取refinansowanie费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', currentBalance);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.notary = parseFloat(document.getElementById('cnotary').value) || 0;
        costs.insurance = parseFloat(document.getElementById('cinsurance').value) || 0;
        costs.other = parseFloat(document.getElementById('cother').value) || 0;
    } else {
        costs = {provision: 0, valuation: 0, notary: 0, insurance: 0, other: 0};
    }

    // 计算当前贷款月供
    var currentMonthlyRate = currentRate / 100 / 12;
    var currentNumPayments = remainingTerm * 12;
    var currentMonthlyPayment = 0;

    if (currentMonthlyRate > 0) {
        currentMonthlyPayment = currentBalance * (currentMonthlyRate * Math.pow(1 + currentMonthlyRate, currentNumPayments)) /
                               (Math.pow(1 + currentMonthlyRate, currentNumPayments) - 1);
    } else {
        currentMonthlyPayment = currentBalance / currentNumPayments;
    }

    // 计算新贷款月供
    var newMonthlyRate = newRate / 100 / 12;
    var newNumPayments = newTerm * 12;
    var newMonthlyPayment = 0;

    if (newMonthlyRate > 0) {
        newMonthlyPayment = currentBalance * (newMonthlyRate * Math.pow(1 + newMonthlyRate, newNumPayments)) /
                           (Math.pow(1 + newMonthlyRate, newNumPayments) - 1);
    } else {
        newMonthlyPayment = currentBalance / newNumPayments;
    }

    // 计算总成本
    var currentTotalCost = currentMonthlyPayment * currentNumPayments;
    var newTotalCost = newMonthlyPayment * newNumPayments;
    var totalRefinancingCosts = costs.provision + costs.valuation + costs.notary + costs.insurance + costs.other;
    var newTotalCostWithFees = newTotalCost + totalRefinancingCosts;

    // 计算节省
    var monthlySavings = currentMonthlyPayment - newMonthlyPayment;
    var totalSavings = currentTotalCost - newTotalCostWithFees;
    var paybackPeriod = totalRefinancingCosts > 0 ? totalRefinancingCosts / Math.abs(monthlySavings) : 0;

    // 计算ROI
    var roi = totalRefinancingCosts > 0 ? (totalSavings / totalRefinancingCosts) * 100 : 0;

    // 更新显示
    updateRefinancingResults(currentBalance, currentMonthlyPayment, newMonthlyPayment,
                           monthlySavings, totalSavings, totalRefinancingCosts,
                           paybackPeriod, roi, remainingTerm, newTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateRefinancingResults(currentBalance, currentMonthlyPayment, newMonthlyPayment,
                                monthlySavings, totalSavings, totalRefinancingCosts,
                                paybackPeriod, roi, remainingTerm, newTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        var mainResult = monthlySavings > 0 ?
            'Oszczędność: ' + formatNumber(monthlySavings) + ' zł/miesiąc' :
            'Strata: ' + formatNumber(-monthlySavings) + ' zł/miesiąc';

        resultHeader.innerHTML =
            mainResult +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells && cells.length >= 4) {
                switch(i) {
                    case 2: // Miesięczna rata
                        cells[1].innerHTML = formatNumber(currentMonthlyPayment) + ' zł';
                        cells[2].innerHTML = formatNumber(newMonthlyPayment) + ' zł';
                        cells[3].innerHTML = (monthlySavings >= 0 ? '' : '+') + formatNumber(-monthlySavings) + ' zł';
                        break;
                    case 3: // Oprocentowanie
                        cells[1].innerHTML = document.getElementById('ccurrentrate').value + '%';
                        cells[2].innerHTML = document.getElementById('cnewrate').value + '%';
                        cells[3].innerHTML = (parseFloat(document.getElementById('ccurrentrate').value) - parseFloat(document.getElementById('cnewrate').value)).toFixed(2) + '%';
                        break;
                    case 4: // Okres kredytowania
                        cells[1].innerHTML = remainingTerm + ' lat';
                        cells[2].innerHTML = newTerm + ' lat';
                        cells[3].innerHTML = (newTerm - remainingTerm) + ' lat';
                        break;
                    case 5: // Koszty refinansowania
                        cells[2].innerHTML = formatNumber(totalRefinancingCosts) + ' zł';
                        break;
                    case 6: // Łączne oszczędności
                        cells[2].innerHTML = formatNumber(totalSavings) + ' zł';
                        break;
                    case 7: // Okres zwrotu
                        cells[2].innerHTML = paybackPeriod > 0 ? Math.ceil(paybackPeriod) + ' miesięcy' : 'Natychmiast';
                        break;
                }
            }
        }
    }

    // 更新analiza refinansowania
    var analysisSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (analysisSection) {
        var recommendation = getRefinancingRecommendation(roi, paybackPeriod, monthlySavings);

        analysisSection.innerHTML =
            '<h3>Analiza refinansowania kredytu:</h3>' +
            '<p><strong>ROI refinansowania:</strong> ' + roi.toFixed(1) + '% (zwrot z inwestycji w refinansowanie)</p>' +
            '<p><strong>Okres zwrotu kosztów:</strong> ' + (paybackPeriod > 0 ? Math.ceil(paybackPeriod) + ' miesięcy' : 'Natychmiast') + '</p>' +
            '<p><strong>Miesięczne oszczędności:</strong> ' + formatNumber(monthlySavings) + ' zł</p>' +
            '<p><strong>Łączne oszczędności:</strong> ' + formatNumber(totalSavings) + ' zł (przez cały okres)</p>' +
            '<p><strong>Rekomendacja:</strong> ' + recommendation + '</p>';
    }
}

function getRefinancingRecommendation(roi, paybackPeriod, monthlySavings) {
    if (monthlySavings <= 0) {
        return 'Refinansowanie nieopłacalne - wyższa rata';
    } else if (roi > 300 && paybackPeriod < 24) {
        return 'Refinansowanie bardzo opłacalne - zdecydowanie zalecane';
    } else if (roi > 200 && paybackPeriod < 36) {
        return 'Refinansowanie opłacalne - zalecane';
    } else if (roi > 100 && paybackPeriod < 48) {
        return 'Refinansowanie umiarkowanie opłacalne - rozważ';
    } else {
        return 'Refinansowanie mało opłacalne - sprawdź inne opcje';
    }
}

function clearForm(form) {
    if (document.getElementById('ccurrentbalance')) document.getElementById('ccurrentbalance').value = '300000';
    if (document.getElementById('ccurrentrate')) document.getElementById('ccurrentrate').value = '8.5';
    if (document.getElementById('cremainingterm')) document.getElementById('cremainingterm').value = '20';
    if (document.getElementById('cnewrate')) document.getElementById('cnewrate').value = '6.5';
    if (document.getElementById('cnewterm')) document.getElementById('cnewterm').value = '20';

    if (document.getElementById('cprovision')) document.getElementById('cprovision').value = '1.5';
    if (document.getElementById('cvaluation')) document.getElementById('cvaluation').value = '2000';
    if (document.getElementById('cnotary')) document.getElementById('cnotary').value = '3000';
    if (document.getElementById('cinsurance')) document.getElementById('cinsurance').value = '1500';
    if (document.getElementById('cother')) document.getElementById('cother').value = '1000';

    calculateRefinancing();
}

function saveCalResult() {
    alert('Wyniki kalkulatora refinansowania zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateRefinancing();
};
</script>

</body>
</html>
