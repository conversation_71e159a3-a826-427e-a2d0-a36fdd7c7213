<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Kalkulator kredytu hipotecznego Alior Bank</title>
	<meta name="description" content="Kalkulator kredytu hipotecznego Alior Bank - oblicz ratę kredytu mieszkaniowego w Alior Bank, sprawdź warunki i promocje Alior Bank na kredyty hipoteczne 2025.">
	<meta name="keywords" content="kalkulator kredytu hipotecznego alior bank, alior bank kredyt hipoteczny, rata kredytu alior bank, warunki kredytu alior bank">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulator-kredytu-hipotecznego-alior-bank.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego alior bank</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Kalkulator kredytu hipotecznego Alior Bank</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w Alior Bank"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/kalkulator-kredytu-hipotecznego-alior-bank.html">
<table align="center">
<tbody><tr><td align="right">Wartość nieruchomości Alior Bank</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="600000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny w Alior Bank. Minimum 10% wartości nieruchomości. Wyższy wkład własny = lepsza marża w Alior Bank.', '');" onmouseout="tooltip.hide();"></td><td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w Alior Bank. Maksymalnie 35 lat. Dłuższy okres = niższa rata w Alior Bank.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w Alior Bank. WIBOR 3M + marża Alior Bank.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.65" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Data rozpoczęcia</td><td align="left" colspan="2"><select name="cstartmonth" id="cstartmonth"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char"></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Uwzględnij opłaty Alior Bank poniżej</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Opłaty i koszty Alior Bank</td></tr>
	<tr><td align="right">Prowizja przygotowawcza Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja Alior Bank za przygotowanie kredytu. 0.5-2.5% kwoty kredytu w zależności od profilu klienta Alior Bank.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="1.8" class="innormal inpct"></td><td><select name="cprovisionunit" onchange="cunitchange('cprovision', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obowiązkowe ubezpieczenie nieruchomości w Alior Bank. Koszt zależy od wartości nieruchomości i zakresu ochrony.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1200" class="innormal  indollar"></td><td><select name="chomeinsunit" onchange="cunitchange('chomeins', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w Alior Bank. 0.22-0.38% kwoty kredytu rocznie. Opcjonalne w Alior Bank.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.30" class="innormal inpct"></td><td><select name="cpmiunit" onchange="cunitchange('cpmi', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Rachunek kredytowy Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w Alior Bank. 0-20 zł miesięcznie.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="18" class="innormal  indollar"></td><td><select name="choaunit" onchange="cunitchange('choa', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Inne koszty Alior Bank <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty Alior Bank: wycena nieruchomości (1200-1900 zł), koszty notarialne, wpis hipoteki.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="4500" class="innormal  indollar"></td><td><select name="cothercostunit" onchange="cunitchange('cothercost', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Alior Bank</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Profile klientów Alior Bank</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Profil klienta Alior Bank</td><td><select name="caliorprofile" id="caliorprofile" onchange="updateAliorProfile();"><option value="new" selected="">Nowy klient Alior Bank</option><option value="existing">Obecny klient Alior Bank</option><option value="premium">Alior Bank Premium</option><option value="private">Alior Bank Private</option><option value="business">Alior Bank Business</option><option value="young">Alior Bank Młodzi</option></select></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Typ oprocentowania Alior Bank</td><td><select name="caliorinteresttype" id="caliorinteresttype" onchange="updateAliorInterestType();"><option value="variable" selected="">Zmienne WIBOR + marża</option><option value="fixed3">Stałe na 3 lata Alior Bank</option><option value="fixed5">Stałe na 5 lat Alior Bank</option><option value="fixed10">Stałe na 10 lat Alior Bank</option><option value="mixed">Mieszane Alior Bank</option></select></td></tr>
	<tr><td colspan="3" style="padding:10px 0px 3px 0px;font-weight:bold;text-align:left;">Promocje Alior Bank 2025</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Cashback Alior Bank</td><td><input type="text" name="caliorcashback" id="caliorcashback" value="3000" class="innormal indollar"></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Zwolnienie z prowizji Alior Bank</td><td><select name="caliorprovisionwaiver" id="caliorprovisionwaiver"><option value="0" selected="">Brak zwolnienia</option><option value="50">50% zwolnienia</option><option value="100">100% zwolnienia</option></select></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Promocyjna marża Alior Bank</td><td><select name="caliorpromomargin" id="caliorpromomargin"><option value="0" selected="">Standardowa marża</option><option value="0.30">-0.30% przez 12 miesięcy</option><option value="0.50">-0.50% przez 6 miesięcy</option></select></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz kredyt Alior Bank" onclick="calculateAliorMortgage();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Rata Alior Bank: &nbsp; 3 785 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBBbGlvciBCYW5r', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBBbGlvciBCYW5r', 'UmF0YSBBbGlvciBCYW5r', 'MyA3ODUgekw=');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Rocznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata Alior Bank</b></td><td align="right"><b>3 665 zł</b></td><td align="right"><b>43 980 zł</b></td><td align="right"><b>1 099 500 zł</b></td></tr>
<tr><td>Kwota kredytu Alior Bank</td><td align="right">-</td><td align="right">-</td><td align="right">480 000 zł</td></tr>
<tr><td>Odsetki Alior Bank</td><td align="right">-</td><td align="right">-</td><td align="right">619 500 zł</td></tr>
<tr><td>Prowizja Alior Bank</td><td align="right">-</td><td align="right">-</td><td align="right">8 640 zł</td></tr>
<tr><td>Ubezpieczenie nieruchomości</td><td align="right">100 zł</td><td align="right">1 200 zł</td><td align="right">30 000 zł</td></tr>
<tr><td>Ubezpieczenie spłaty Alior Bank</td><td align="right">120 zł</td><td align="right">1 440 zł</td><td align="right">36 000 zł</td></tr>
<tr><td>Rachunek kredytowy Alior Bank</td><td align="right">18 zł</td><td align="right">216 zł</td><td align="right">5 400 zł</td></tr>
<tr><td>Inne koszty Alior Bank</td><td align="right">-</td><td align="right">-</td><td align="right">4 500 zł</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Szczegóły kredytu Alior Bank:</h3>
<p><strong>RRSO Alior Bank:</strong> 8.45% (z wszystkimi opłatami Alior Bank)</p>
<p><strong>Status klienta:</strong> Nowy klient Alior Bank (marża standardowa)</p>
<p><strong>Typ oprocentowania:</strong> Zmienne WIBOR + marża Alior Bank</p>
<p><strong>Całkowity koszt Alior Bank:</strong> 1 203 540 zł</p>
<p><strong>Przepłacone odsetki:</strong> 723 540 zł (151% kwoty kredytu)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Porównanie profili klientów Alior Bank</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Profile Alior Bank</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">3400 zł</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">3600 zł</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">3800 zł</text>

<!-- Alior Bank Private -->
<rect x="50" y="135" width="35" height="10" fill="#8e44ad" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Private</text>
<text x="67" y="130" class="mcllabelx" style="fill:#000;">3485 zł</text>

<!-- Alior Bank Premium -->
<rect x="95" y="125" width="35" height="20" fill="#9b59b6" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Premium</text>
<text x="112" y="120" class="mcllabelx" style="fill:#000;">3585 zł</text>

<!-- Alior Bank Młodzi -->
<rect x="140" y="115" width="35" height="30" fill="#3498db" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Młodzi</text>
<text x="157" y="110" class="mcllabelx" style="fill:#000;">3635 zł</text>

<!-- Alior Bank Business -->
<rect x="185" y="105" width="35" height="40" fill="#2ecc71" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Business</text>
<text x="202" y="100" class="mcllabelx" style="fill:#000;">3685 zł</text>

<!-- Obecny klient -->
<rect x="230" y="95" width="35" height="50" fill="#f39c12" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Obecny</text>
<text x="247" y="90" class="mcllabelx" style="fill:#000;">3735 zł</text>

<!-- Nowy klient -->
<rect x="275" y="85" width="35" height="60" fill="#e74c3c" opacity="0.8"></rect>
<text x="292" y="155" class="mcllabelx">Nowy</text>
<text x="292" y="80" class="mcllabelx" style="fill:#000;">3785 zł</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Profile klientów Alior Bank &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania Alior Bank</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Typ oprocentowania Alior Bank</th>
<th>Oprocentowanie</th>
<th>Miesięczna rata</th>
<th>Całkowity koszt</th>
<th>Różnica</th>
</tr>
<tr>
<td>Zmienne WIBOR + marża</td>
<td>7.65%</td>
<td>3 785 zł</td>
<td>1 135 500 zł</td>
<td>-</td>
</tr>
<tr>
<td>Stałe na 3 lata Alior Bank</td>
<td>8.05%</td>
<td>3 985 zł</td>
<td>1 195 500 zł</td>
<td>+60 000 zł</td>
</tr>
<tr>
<td>Stałe na 5 lat Alior Bank</td>
<td>8.35%</td>
<td>4 185 zł</td>
<td>1 255 500 zł</td>
<td>+120 000 zł</td>
</tr>
<tr>
<td>Stałe na 10 lat Alior Bank</td>
<td>8.75%</td>
<td>4 385 zł</td>
<td>1 315 500 zł</td>
<td>+180 000 zł</td>
</tr>
<tr>
<td>Mieszane Alior Bank</td>
<td>7.85%</td>
<td>3 885 zł</td>
<td>1 165 500 zł</td>
<td>+30 000 zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego Alior Bank - nowoczesny bank z polskimi korzeniami 2025</h2>
            <p>Alior Bank to jeden z najbardziej innowacyjnych banków w Polsce, łączący nowoczesne technologie z tradycyjną bankowością. Nasz kalkulator kredytu hipotecznego Alior Bank pomoże Ci precyzyjnie obliczyć ratę kredytu mieszkaniowego w Alior Bank, sprawdzić warunki i porównać profile klientów Alior Bank - banku znanego z elastycznego podejścia do klientów i konkurencyjnych warunków kredytowych.</p>

            <h3>Profile klientów Alior Bank:</h3>
            <div style="margin: 15px 0;">
                <h4>Alior Bank Private:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.05% (najniższa marża Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Aktywa minimum 600 000 zł w Alior Bank</li>
                    <li><strong>Korzyści:</strong> Dedykowany doradca, ekskluzywna obsługa VIP Alior Bank</li>
                    <li><strong>Prowizja:</strong> 0.5% - 1.5% (preferencyjne stawki Alior Bank)</li>
                    <li><strong>Dodatkowe usługi:</strong> Zarządzanie portfelem, inwestycje, concierge Alior Bank</li>
                    <li><strong>Dostępność:</strong> Ograniczona, tylko zamożni klienci Alior Bank</li>
                </ul>

                <h4>Alior Bank Premium:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.20% (preferencyjne warunki Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Dochody 12 000+ zł netto, pakiet Premium Alior Bank</li>
                    <li><strong>Korzyści:</strong> Obniżone opłaty, priorytetowa obsługa Alior Bank</li>
                    <li><strong>Prowizja:</strong> 1% - 2% (preferencyjne stawki Alior Bank)</li>
                    <li><strong>Dodatkowe usługi:</strong> Ubezpieczenia, karty premium Alior Bank</li>
                    <li><strong>Dostępność:</strong> Dobra, dla klientów o wysokich dochodach</li>
                </ul>

                <h4>Alior Bank Młodzi:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.30% (specjalna oferta dla młodych Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Wiek 18-35 lat, pierwsze mieszkanie</li>
                    <li><strong>Korzyści:</strong> Obniżona marża, darmowe prowadzenie konta Alior Bank</li>
                    <li><strong>Prowizja:</strong> 1% - 1.8% (promocyjne stawki Alior Bank)</li>
                    <li><strong>Dodatkowe usługi:</strong> Darmowa bankowość mobilna Alior Mobile</li>
                    <li><strong>Dostępność:</strong> Bardzo dobra, program dedykowany młodym</li>
                </ul>

                <h4>Alior Bank Business:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.40% (oferta dla przedsiębiorców Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Działalność gospodarcza, współpraca biznesowa z Alior Bank</li>
                    <li><strong>Korzyści:</strong> Powiązanie z kredytami biznesowymi, kompleksowa obsługa</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2.5% (negocjowalna dla dużych firm)</li>
                    <li><strong>Dodatkowe usługi:</strong> Kredyty biznesowe, faktoring, leasing Alior Bank</li>
                    <li><strong>Dostępność:</strong> Dobra, dla przedsiębiorców współpracujących z Alior Bank</li>
                </ul>

                <h4>Obecny klient Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.50% (preferencje dla stałych klientów Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Minimum 12 miesięcy jako klient Alior Bank</li>
                    <li><strong>Korzyści:</strong> Obniżona marża, uproszczona procedura Alior Bank</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2% (preferencyjne stawki Alior Bank)</li>
                    <li><strong>Dodatkowe usługi:</strong> Możliwość negocjacji warunków Alior Bank</li>
                    <li><strong>Dostępność:</strong> Bardzo dobra dla klientów Alior Bank</li>
                </ul>

                <h4>Nowy klient Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.65% (standardowe warunki Alior Bank)</li>
                    <li><strong>Wymagania:</strong> Standardowe wymagania kredytowe</li>
                    <li><strong>Korzyści:</strong> Dostęp do pełnej oferty Alior Bank</li>
                    <li><strong>Prowizja:</strong> 1.8% - 2.5% (standardowe stawki Alior Bank)</li>
                    <li><strong>Dodatkowe usługi:</strong> Pakiety startowe Alior Bank</li>
                    <li><strong>Dostępność:</strong> Powszechna, dla wszystkich klientów</li>
                </ul>
            </div>

            <h3>Typy oprocentowania w Alior Bank:</h3>
            <div style="margin: 15px 0;">
                <h4>Zmienne oprocentowanie Alior Bank (WIBOR + marża):</h4>
                <ul>
                    <li><strong>Bazowe oprocentowanie:</strong> WIBOR 3M + marża Alior Bank</li>
                    <li><strong>Aktualna marża:</strong> 3.45% - 4.45% w zależności od profilu Alior Bank</li>
                    <li><strong>Korzyści:</strong> Konkurencyjne oprocentowanie na starcie</li>
                    <li><strong>Ryzyko:</strong> Zmienność wraz z WIBOR</li>
                    <li><strong>Rekomendacja Alior Bank:</strong> Dla klientów akceptujących ryzyko</li>
                </ul>

                <h4>Stałe oprocentowanie na 3 lata Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.40% do zmiennego Alior Bank</li>
                    <li><strong>Okres stały:</strong> 36 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Alior Bank</li>
                    <li><strong>Korzyści:</strong> Krótkoterminowa stabilność w Alior Bank</li>
                    <li><strong>Rekomendacja Alior Bank:</strong> Dla ostrożnych na początku</li>
                </ul>

                <h4>Stałe oprocentowanie na 5 lat Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.70% do zmiennego Alior Bank</li>
                    <li><strong>Okres stały:</strong> 60 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Alior Bank</li>
                    <li><strong>Korzyści:</strong> Średnioterminowa przewidywalność w Alior Bank</li>
                    <li><strong>Rekomendacja Alior Bank:</strong> Dla planujących stabilność</li>
                </ul>

                <h4>Stałe oprocentowanie na 10 lat Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +1.10% do zmiennego Alior Bank</li>
                    <li><strong>Okres stały:</strong> 120 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Alior Bank</li>
                    <li><strong>Korzyści:</strong> Długoterminowa stabilność w Alior Bank</li>
                    <li><strong>Rekomendacja Alior Bank:</strong> Dla maksymalnej przewidywalności</li>
                </ul>

                <h4>Mieszane oprocentowanie Alior Bank:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.20% do zmiennego Alior Bank</li>
                    <li><strong>Mechanizm:</strong> Część stała, część zmienna</li>
                    <li><strong>Elastyczność:</strong> Kompromis między stabilnością a kosztem</li>
                    <li><strong>Korzyści:</strong> Ograniczone ryzyko zmian w Alior Bank</li>
                    <li><strong>Rekomendacja Alior Bank:</strong> Dla umiarkowanie konserwatywnych</li>
                </ul>
            </div>

            <h3>Promocje Alior Bank 2025:</h3>
            <ul>
                <li><strong>Cashback Alior Bank:</strong> Do 3 000 zł zwrotu dla nowych klientów</li>
                <li><strong>Zwolnienie z prowizji:</strong> 50% - 100% dla wybranych profili Alior Bank</li>
                <li><strong>Promocyjna marża:</strong> -0.30% przez 12 miesięcy lub -0.50% przez 6 miesięcy</li>
                <li><strong>Darmowa wycena:</strong> Dla klientów Premium i Private Alior Bank</li>
                <li><strong>Pakiet startowy:</strong> Darmowe prowadzenie konta przez 6 miesięcy</li>
                <li><strong>Ubezpieczenia grupowe:</strong> Preferencyjne stawki dla klientów Alior Bank</li>
                <li><strong>Program lojalnościowy:</strong> Punkty za aktywność bankową w Alior Bank</li>
            </ul>

            <h3>Zalety kredytu hipotecznego w Alior Bank:</h3>
            <ul>
                <li><strong>Nowoczesne technologie:</strong> Zaawansowana bankowość cyfrowa Alior Mobile</li>
                <li><strong>Elastyczne podejście:</strong> Indywidualne rozpatrywanie wniosków</li>
                <li><strong>Szeroka sieć:</strong> Ponad 800 oddziałów i punktów Alior Bank w Polsce</li>
                <li><strong>Konkurencyjne oprocentowanie:</strong> Atrakcyjne marże na rynku</li>
                <li><strong>Szybka obsługa:</strong> Decyzja kredytowa w 24-48 godzin</li>
                <li><strong>Innowacyjne rozwiązania:</strong> Pierwszy bank z pełną bankowością mobilną</li>
                <li><strong>Profesjonalna obsługa:</strong> Doświadczeni doradcy kredytowi Alior Bank</li>
                <li><strong>Stabilność finansowa:</strong> Silne fundamenty i rosnąca pozycja na rynku</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/alior-bank-kalkulator-kredytu-hipotecznego.html">Alior Bank kalkulator kredytu</a>
<a href="/santander-kalkulator-kredytu-hipotecznego.html">Santander kalkulator kredytu</a>
<a href="/pko-bp-kalkulator-kredytu-hipotecznego.html">PKO BP kalkulator kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Profile klientów Alior Bank</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Wybierz profil Alior Bank:</strong><br>
<a href="#" onclick="return setAliorProfile('private');">Alior Bank Private</a><br>
<a href="#" onclick="return setAliorProfile('premium');">Alior Bank Premium</a><br>
<a href="#" onclick="return setAliorProfile('young');">Alior Bank Młodzi</a><br>
<a href="#" onclick="return setAliorProfile('business');">Alior Bank Business</a><br>
<a href="#" onclick="return setAliorProfile('existing');">Obecny klient Alior Bank</a><br>
<a href="#" onclick="return setAliorProfile('new');">Nowy klient Alior Bank</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Kalkulator kredytu hipotecznego Alior Bank - oblicz ratę kredytu mieszkaniowego w Alior Bank, sprawdź warunki i promocje.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateAliorMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updateAliorResults(loanAmount, monthlyPayment, totalInterest, costs, 
                      totalCost, monthlyTotal, rrso, loanTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var value = parseFloat(document.getElementById(valueId).value) || 0;
    var unit = document.getElementById(unitId).value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateAliorResults(loanAmount, monthlyPayment, totalInterest, costs, 
                           totalCost, monthlyTotal, rrso, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    document.querySelector('.h2result').innerHTML = 
        'Rata Alior Bank: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
        '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult(\'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBBbGlvciBCYW5r\', 0, \'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBBbGlvciBCYW5r\', \'UmF0YSBBbGlvciBCYW5r\', \'' + btoa(formatNumber(monthlyTotal) + ' zł') + '\');">';
    
    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        if (rows.length >= 9) {
            // 更新各行数据
            if (rows[2]) {
                rows[2].cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                rows[2].cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment * 12) + ' zł</b>';
                rows[2].cells[3].innerHTML = '<b>' + formatNumber(monthlyPayment * loanTerm * 12) + ' zł</b>';
            }
            
            if (rows[3]) {
                rows[3].cells[3].innerHTML = formatNumber(loanAmount) + ' zł';
            }
            
            if (rows[4]) {
                rows[4].cells[3].innerHTML = formatNumber(totalInterest) + ' zł';
            }
            
            if (rows[5]) {
                rows[5].cells[3].innerHTML = formatNumber(costs.provision) + ' zł';
            }
            
            if (rows[6]) {
                rows[6].cells[1].innerHTML = formatNumber(costs.homeInsurance/12) + ' zł';
                rows[6].cells[2].innerHTML = formatNumber(costs.homeInsurance) + ' zł';
                rows[6].cells[3].innerHTML = formatNumber(costs.homeInsurance * loanTerm) + ' zł';
            }
            
            if (rows[7]) {
                rows[7].cells[1].innerHTML = formatNumber(costs.lifeInsurance/12) + ' zł';
                rows[7].cells[2].innerHTML = formatNumber(costs.lifeInsurance) + ' zł';
                rows[7].cells[3].innerHTML = formatNumber(costs.lifeInsurance * loanTerm) + ' zł';
            }
            
            if (rows[8]) {
                rows[8].cells[1].innerHTML = formatNumber(costs.accountFee) + ' zł';
                rows[8].cells[2].innerHTML = formatNumber(costs.accountFee * 12) + ' zł';
                rows[8].cells[3].innerHTML = formatNumber(costs.accountFee * 12 * loanTerm) + ' zł';
            }
            
            if (rows[9]) {
                rows[9].cells[3].innerHTML = formatNumber(costs.otherCosts) + ' zł';
            }
        }
    }
    
    // 更新szczegóły kredytu
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var additionalCosts = totalCost - loanAmount - totalInterest;
        detailsSection.innerHTML = 
            '<h3>Szczegóły kredytu Alior Bank:</h3>' +
            '<p><strong>RRSO Alior Bank:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi opłatami Alior Bank)</p>' +
            '<p><strong>Status klienta:</strong> ' + getAliorClientStatus() + '</p>' +
            '<p><strong>Typ oprocentowania:</strong> ' + getAliorInterestType() + '</p>' +
            '<p><strong>Całkowity koszt Alior Bank:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Przepłacone odsetki:</strong> ' + formatNumber(totalInterest + additionalCosts) + ' zł (' + Math.round(((totalInterest + additionalCosts)/loanAmount)*100) + '% kwoty kredytu)</p>';
    }
}

function getAliorClientStatus() {
    var profile = document.getElementById('caliorprofile').value;
    switch(profile) {
        case 'private': return 'Alior Bank Private (marża preferencyjna)';
        case 'premium': return 'Alior Bank Premium (marża preferencyjna)';
        case 'young': return 'Alior Bank Młodzi (marża promocyjna)';
        case 'business': return 'Alior Bank Business (marża biznesowa)';
        case 'existing': return 'Obecny klient Alior Bank (marża preferencyjna)';
        default: return 'Nowy klient Alior Bank (marża standardowa)';
    }
}

function getAliorInterestType() {
    var type = document.getElementById('caliorinteresttype').value;
    switch(type) {
        case 'fixed3': return 'Stałe na 3 lata Alior Bank';
        case 'fixed5': return 'Stałe na 5 lat Alior Bank';
        case 'fixed10': return 'Stałe na 10 lat Alior Bank';
        case 'mixed': return 'Mieszane Alior Bank';
        default: return 'Zmienne WIBOR + marża Alior Bank';
    }
}

function updateAliorProfile() {
    var profile = document.getElementById('caliorprofile').value;
    var rateField = document.getElementById('cinterestrate');
    var baseRate = 7.65;
    
    switch(profile) {
        case 'private':
            rateField.value = (baseRate - 0.60).toFixed(2);
            break;
        case 'premium':
            rateField.value = (baseRate - 0.45).toFixed(2);
            break;
        case 'young':
            rateField.value = (baseRate - 0.35).toFixed(2);
            break;
        case 'business':
            rateField.value = (baseRate - 0.25).toFixed(2);
            break;
        case 'existing':
            rateField.value = (baseRate - 0.15).toFixed(2);
            break;
        case 'new':
            rateField.value = baseRate.toFixed(2);
            break;
    }
    calculateAliorMortgage();
}

function updateAliorInterestType() {
    var type = document.getElementById('caliorinteresttype').value;
    var rateField = document.getElementById('cinterestrate');
    var baseRate = 7.65;
    
    switch(type) {
        case 'variable':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'fixed3':
            rateField.value = (baseRate + 0.40).toFixed(2);
            break;
        case 'fixed5':
            rateField.value = (baseRate + 0.70).toFixed(2);
            break;
        case 'fixed10':
            rateField.value = (baseRate + 1.10).toFixed(2);
            break;
        case 'mixed':
            rateField.value = (baseRate + 0.20).toFixed(2);
            break;
    }
    calculateAliorMortgage();
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij opłaty Alior Bank poniżej';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij opłaty Alior Bank poniżej';
    }
    calculateAliorMortgage();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Opcje Alior Bank</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Opcje Alior Bank</a>';
        hiddenField.value = '0';
    }
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
    calculateAliorMortgage();
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '600000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.65';
    document.getElementById('cstartmonth').value = '8';
    document.getElementById('cstartyear').value = '2025';
    
    document.getElementById('cprovision').value = '1.8';
    document.getElementById('chomeins').value = '1200';
    document.getElementById('cpmi').value = '0.30';
    document.getElementById('choa').value = '18';
    document.getElementById('cothercost').value = '4500';
    
    document.getElementById('caliorprofile').value = 'new';
    document.getElementById('caliorinteresttype').value = 'variable';
    
    document.getElementById('caddoptional').checked = true;
    cshtaxcost();
    calculateAliorMortgage();
}

function setAliorProfile(type) {
    document.getElementById('caliorprofile').value = type;
    updateAliorProfile();
    return false;
}

function saveCalResult(param1, param2, param3, param4, param5) {
    alert('Wyniki kalkulatora Alior Bank zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania Alior Bank &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Profile klientów Alior Bank</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Profile klientów Alior Bank &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania Alior Bank</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateAliorMortgage();
};
</script>

</body></html>
