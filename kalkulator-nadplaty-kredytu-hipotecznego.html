<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator nadpłaty kredytu hipotecznego</title>
    <meta name="description" content="Darmowy kalkulator nadpłaty kredytu hipotecznego. Oblicz oszczędności z nadpłat, skrócenie okresu kredytowania i zmniejszenie całkowitych kosztów kredytu.">
    <meta name="keywords" content="kalkulator nadpłaty kredytu hipotecznego, nadpłata kredytu, wcześ<PERSON>jsza spłata, oszczędności kredytu, skrócenie okresu">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator nadpłaty kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator nadpłaty kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Wprowadź parametry kredytu i nadpłaty, aby obliczyć oszczędności"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-nadplaty-kredytu-hipotecznego.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Aktualne saldo kredytu</td>
                                <td align="right"><input type="text" name="ccurrentbalance" id="ccurrentbalance" value="350000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Aktualna miesięczna rata</td>
                                <td align="right"><input type="text" name="ccurrentpayment" id="ccurrentpayment" value="2500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego. Sprawdź w umowie kredytowej lub ostatnim wyciągu bankowym.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Pozostały okres <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Liczba lat pozostałych do końca spłaty kredytu według pierwotnego harmonogramu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cremainingterm" id="cremainingterm" value="20" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td colspan="3"><hr style="margin: 15px 0;"></td>
                            </tr>
                            <tr>
                                <td align="right">Typ nadpłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cprepaymenttype" id="cprepaymenttype" onchange="togglePrepaymentOptions();">
                                        <option value="monthly" selected="">Miesięczna nadpłata</option>
                                        <option value="onetime">Jednorazowa nadpłata</option>
                                        <option value="yearly">Roczna nadpłata</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="monthlyprepayment">
                                <td align="right">Miesięczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota płacona miesięcznie ponad standardową ratę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyprepayment" id="cmonthlyprepayment" value="500" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="onetimeprepayment" style="display:none;">
                                <td align="right">Jednorazowa nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Jednorazowa większa kwota przeznaczona na wcześniejszą spłatę części kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="conetimeprepayment" id="conetimeprepayment" value="50000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr id="yearlyprepayment" style="display:none;">
                                <td align="right">Roczna nadpłata <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kwota płacona raz w roku jako dodatkowa spłata kredytu, np. z premii rocznej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cyearlyprepayment" id="cyearlyprepayment" value="10000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Efekt nadpłaty</td>
                                <td align="left" colspan="2">
                                    <select name="cprepaymenteffect" id="cprepaymenteffect">
                                        <option value="reduce_term" selected="">Skrócenie okresu kredytowania</option>
                                        <option value="reduce_payment">Zmniejszenie miesięcznej raty</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddcosts" class="cbcontainer">
                                        <input type="checkbox" name="caddcosts" id="caddcosts" value="1" onclick="cshcosts();">
                                        <span class="cbmark"></span>
                                        <b><span id="ccostsdesc">Uwzględnij prowizję za wcześniejszą spłatę</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ccosts" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Prowizja za wcześniejszą spłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Opłata pobierana przez bank za wcześniejszą spłatę kredytu. Sprawdź w umowie kredytowej wysokość prowizji.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cprepaymentfee" id="cprepaymentfee" value="1" class="innormal inpct"></td>
                                                    <td><select name="cprepaymentfeeunit" onchange="cunitchange('cprepaymentfee', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz oszczędności" onclick="calculateOverpayment();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Oszczędności: &nbsp; 89 450 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBuYWRwxYJhdHkga3JlZHl0dSBoaXBvdGVjem5lZ28=', 0, 'S2Fsa3VsYXRvciBuYWRwxYJhdHkga3JlZHl0dSBoaXBvdGVjem5lZ28=', 'T3N6Y3rEmWRub8WbY2k=', 'ODkgNDUwIHrFgg==');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Bez nadpłaty</b></td>
                                        <td align="right"><b>Z nadpłatą</b></td>
                                        <td align="right"><b>Oszczędność</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowity koszt</b></td>
                                        <td align="right"><b>600 000 zł</b></td>
                                        <td align="right"><b>510 550 zł</b></td>
                                        <td align="right"><b>89 450 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Całkowite odsetki</td>
                                        <td align="right">250 000 zł</td>
                                        <td align="right">160 550 zł</td>
                                        <td align="right">89 450 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Okres spłaty</td>
                                        <td align="right">20 lat</td>
                                        <td align="right">14 lat 8 miesięcy</td>
                                        <td align="right">5 lat 4 miesiące</td>
                                    </tr>
                                    <tr>
                                        <td>Całkowita nadpłata</td>
                                        <td align="right">0 zł</td>
                                        <td align="right">88 000 zł</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Analiza nadpłaty:</h3>
                                            <p><strong>Efektywność:</strong> Każda złotówka nadpłaty oszczędza 1,02 zł odsetek</p>
                                            <p><strong>Skrócenie okresu:</strong> 5 lat 4 miesiące wcześniejsze zakończenie spłaty</p>
                                            <p><strong>Miesięczna oszczędność odsetek:</strong> 509 zł</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie scenariuszy spłaty</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Rok</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">0 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">200K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">400K zł</text>
                    
                    <!-- Bez nadpłaty -->
                    <path d="M 48 87.689 L 292 145" fill="none" stroke="#ff6b6b" stroke-width="3" stroke-dasharray="5,5"></path>
                    <!-- Z nadpłatą -->
                    <path d="M 48 87.689 L 220 145" fill="none" stroke="#4ecdc4" stroke-width="3"></path>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                    <rect x="53" y="17" width="20" height="6" style="fill:#ff6b6b;"></rect>
                    <text x="78" y="24" class="mcllegend">Bez nadpłaty</text>
                    <rect x="53" y="35" width="20" height="6" style="fill:#4ecdc4;"></rect>
                    <text x="78" y="42" class="mcllegend">Z nadpłatą</text>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Harmonogram z nadpłatami &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Szczegółowy harmonogram</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Miesiąc</th>
                            <th>Rata + Nadpłata</th>
                            <th>Kapitał</th>
                            <th>Odsetki</th>
                            <th>Saldo</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Jak działają nadpłaty kredytu hipotecznego?</h2>
            <p>Nadpłata kredytu hipotecznego to dodatkowa kwota płacona ponad standardową miesięczną ratę, która jest przeznaczona na spłatę kapitału kredytu. Nadpłaty pozwalają znacznie zmniejszyć całkowity koszt kredytu i skrócić okres spłaty.</p>

            <h3>Rodzaje nadpłat kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>Nadpłaty miesięczne:</h4>
                <ul>
                    <li>Stała dodatkowa kwota płacona co miesiąc</li>
                    <li>Systematyczne zmniejszanie salda kredytu</li>
                    <li>Najefektywniejsza forma nadpłat</li>
                    <li>Łatwość planowania budżetu domowego</li>
                </ul>

                <h4>Nadpłaty jednorazowe:</h4>
                <ul>
                    <li>Większa kwota płacona okazjonalnie</li>
                    <li>Wykorzystanie premii, zwrotów podatku</li>
                    <li>Natychmiastowy efekt na saldo kredytu</li>
                    <li>Elastyczność w zarządzaniu finansami</li>
                </ul>

                <h4>Nadpłaty roczne:</h4>
                <ul>
                    <li>Regularna nadpłata raz w roku</li>
                    <li>Wykorzystanie 13. pensji lub premii</li>
                    <li>Kompromis między regularnością a elastycznością</li>
                    <li>Dobra opcja dla osób o zmiennych dochodach</li>
                </ul>
            </div>

            <h3>Efekty nadpłat:</h3>
            <div style="margin: 15px 0;">
                <h4>Skrócenie okresu kredytowania:</h4>
                <ul>
                    <li>Zachowanie wysokości miesięcznej raty</li>
                    <li>Wcześniejsze zakończenie spłaty kredytu</li>
                    <li>Maksymalne oszczędności na odsetkach</li>
                    <li>Szybsze uwolnienie nieruchomości od hipoteki</li>
                </ul>

                <h4>Zmniejszenie miesięcznej raty:</h4>
                <ul>
                    <li>Zachowanie pierwotnego okresu spłaty</li>
                    <li>Niższa miesięczna rata po nadpłacie</li>
                    <li>Poprawa płynności finansowej</li>
                    <li>Mniejsze oszczędności niż przy skróceniu okresu</li>
                </ul>
            </div>

            <h3>Kiedy warto dokonywać nadpłat?</h3>
            <ul>
                <li><strong>Wysokie oprocentowanie</strong> - powyżej 6-7% rocznie</li>
                <li><strong>Stabilna sytuacja finansowa</strong> - pewne źródło dochodów</li>
                <li><strong>Brak innych długów</strong> - spłacone kredyty konsumpcyjne</li>
                <li><strong>Rezerwa finansowa</strong> - 3-6 miesięcznych wydatków</li>
                <li><strong>Brak lepszych inwestycji</strong> - nadpłata jako bezpieczna lokata</li>
            </ul>

            <h3>Kiedy nie warto dokonywać nadpłat?</h3>
            <ul>
                <li><strong>Niskie oprocentowanie</strong> - poniżej 4-5% rocznie</li>
                <li><strong>Wysokie prowizje</strong> - za wcześniejszą spłatę</li>
                <li><strong>Lepsze inwestycje</strong> - wyższa stopa zwrotu</li>
                <li><strong>Brak rezerwy</strong> - wszystkie środki w kredycie</li>
                <li><strong>Inne długi</strong> - kredyty o wyższym oprocentowaniu</li>
            </ul>

            <h3>Prowizje za wcześniejszą spłatę:</h3>
            <p>Banki mogą pobierać prowizję za wcześniejszą spłatę kredytu hipotecznego:</p>
            <ul>
                <li><strong>Kredyty o stałym oprocentowaniu</strong> - do 3% nadpłacanej kwoty</li>
                <li><strong>Kredyty o zmiennym oprocentowaniu</strong> - do 1% nadpłacanej kwoty</li>
                <li><strong>Limity roczne</strong> - często 10-20% salda bez prowizji</li>
                <li><strong>Minimalne kwoty</strong> - prowizja od określonej kwoty</li>
            </ul>

            <h3>Strategie optymalizacji nadpłat:</h3>
            <ul>
                <li>Sprawdź warunki umowy kredytowej</li>
                <li>Wykorzystaj roczne limity bez prowizji</li>
                <li>Rozważ refinansowanie przy spadku stóp</li>
                <li>Porównaj z innymi formami oszczędzania</li>
                <li>Zachowaj rezerwę na nieprzewidziane wydatki</li>
                <li>Rozważ nadpłaty kapitału zamiast odsetek</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-zdolnosci-kredytowej.html">Kalkulator zdolności kredytowej</a>
                <a href="/kalkulator-raty-kredytu-hipotecznego.html">Kalkulator raty kredytu</a>
                <a href="/kalkulator-kredytu.html">Kalkulator kredytu</a>
                <a href="/kalkulator-leasingu.html">Kalkulator leasingu</a>
                <a href="/kalkulator-oszczednosci.html">Kalkulator oszczędności</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Strategie nadpłat</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Najlepsze strategie:</strong><br>
                        • Nadpłacaj kapitał, nie odsetki<br>
                        • Wykorzystaj premie i zwroty podatku<br>
                        • Rozważ refinansowanie przy spadku stóp<br>
                        • Sprawdź prowizje za wcześniejszą spłatę
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Oblicz oszczędności z nadpłat kredytu hipotecznego. Sprawdź jak wcześniejsza spłata wpływa na całkowity koszt kredytu.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function togglePrepaymentOptions() {
    var type = document.getElementById('cprepaymenttype').value;
    var monthly = document.getElementById('monthlyprepayment');
    var onetime = document.getElementById('onetimeprepayment');
    var yearly = document.getElementById('yearlyprepayment');
    
    monthly.style.display = (type === 'monthly') ? 'table-row' : 'none';
    onetime.style.display = (type === 'onetime') ? 'table-row' : 'none';
    yearly.style.display = (type === 'yearly') ? 'table-row' : 'none';
}

function cshcosts() {
    var checkbox = document.getElementById('caddcosts');
    var costsDiv = document.getElementById('ccosts');
    if (checkbox.checked) {
        costsDiv.style.display = 'block';
    } else {
        costsDiv.style.display = 'none';
    }
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Szczegółowy harmonogram &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Harmonogram z nadpłatami</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Harmonogram z nadpłatami &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Szczegółowy harmonogram</a>';
    }
    return false;
}
</script>

<script>
function calculateOverpayment() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;

    // 获取nadpłata信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentType = document.getElementById('coverpaymenttype').value;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;

    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 计算没有nadpłata的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;

    // 计算有nadpłata的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;

    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;

        // 添加nadpłata
        var currentOverpayment = 0;
        if (overpaymentFreq === 'monthly' ||
           (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
           (overpaymentFreq === 'once' && monthsPaid === 0)) {
            currentOverpayment = overpaymentAmount;
        }

        // 更新余额
        remainingBalance -= (monthlyPrincipal + currentOverpayment);
        totalPaid += monthlyPayment + currentOverpayment;
        totalOverpayments += currentOverpayment;
        monthsPaid++;

        if (remainingBalance <= 0) break;
    }

    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments);

    // 更新显示
    updateOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                           timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments);
}

function updateOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid,
                                timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Oszczędności: &nbsp; ' + formatNumber(moneySaved) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        for (var i = 0; i < rows.length; i++) {
            var cells = rows[i].cells;
            if (cells.length >= 3) {
                switch(i) {
                    case 2: // Miesięczna rata
                        cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                        break;
                    case 3: // Czas spłaty z nadpłatami
                        cells[1].innerHTML = Math.round(monthsPaid/12) + ' lat ' + (monthsPaid%12) + ' miesięcy';
                        break;
                    case 4: // Oszczędność czasu
                        cells[1].innerHTML = '<b>' + Math.round(timeSaved/12) + ' lat ' + (timeSaved%12) + ' miesięcy</b>';
                        break;
                    case 5: // Oszczędność pieniędzy
                        cells[1].innerHTML = '<b>' + formatNumber(moneySaved) + ' zł</b>';
                        break;
                    case 6: // Oszczędność odsetek
                        cells[1].innerHTML = '<b>' + formatNumber(interestSaved) + ' zł</b>';
                        break;
                }
            }
        }
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '400000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.25';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('coverpayment')) document.getElementById('coverpayment').value = '500';
    calculateOverpayment();
}

function saveCalResult() {
    alert('Wyniki kalkulatora nadpłaty zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateOverpayment();
};
</script>

</body>
</html>
