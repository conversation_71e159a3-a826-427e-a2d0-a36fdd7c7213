<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator zdolności kredytowej - sprawdź swoją zdolność kredytową online</title>
    <meta name="description" content="Darmowy kalkulator zdolności kredytowej do sprawdzenia maksymalnej kwoty kredytu hipotecznego. Oblicz swoją zdolność kredytową na podstawie dochodów i wydatków.">
    <meta name="keywords" content="kalkulator zdolności kredytowej, zdoln<PERSON><PERSON><PERSON> kredytowa, maksym<PERSON>ny kredyt, kredyt hipoteczny, dochody, wydatki">
    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">kalkulatory finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> /
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="kalkulator-zdolnosci-kredytowej.html" itemprop="item"><span itemprop="name">kalkulator zdolności kredytowej</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator zdolności kredytowej</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Wprowadź swoje dane finansowe i sprawdź zdolność kredytową"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-zdolnosci-kredytowej.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Miesięczne dochody netto</td>
                                <td align="right"><input type="text" name="cnetincome" id="cnetincome" value="8000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Dochody współmałżonka <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczne dochody netto współmałżonka lub partnera, które będą uwzględnione w ocenie zdolności kredytowej.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cspouseincome" id="cspouseincome" value="0" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Miesięczne wydatki stałe <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wszystkie stałe miesięczne wydatki takie jak: czynsz, media, ubezpieczenia, inne kredyty, alimenty, itp.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cmonthlyexpenses" id="cmonthlyexpenses" value="3000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Liczba osób w gospodarstwie</td>
                                <td align="right"><input type="text" name="cfamilysize" id="cfamilysize" value="2" class="inhalf"></td>
                                <td>osób</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Planowany okres spłaty kredytu hipotecznego w latach. Dłuższy okres oznacza niższą ratę, ale wyższą zdolność kredytową.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie kredytu</td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddexpenses" class="cbcontainer">
                                        <input type="checkbox" name="caddexpenses" id="caddexpenses" value="1" checked="" onclick="cshexpenses();">
                                        <span class="cbmark"></span>
                                        <b><span id="cexpensesdesc">Uwzględnij dodatkowe wydatki</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cexpenses" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Miesięczne wydatki</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Koszty utrzymania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Szacunkowe miesięczne koszty utrzymania na osobę w gospodarstwie domowym (żywność, odzież, transport, itp.).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="clivingcosts" id="clivingcosts" value="800" class="innormal indollar"></td>
                                                    <td>na osobę</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne kredyty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczne raty innych kredytów i zobowiązań finansowych (kredyty konsumpcyjne, karty kredytowe, itp.).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cothercredits" id="cothercredits" value="0" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenia <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczne składki na ubezpieczenia (życiowe, majątkowe, zdrowotne prywatne, itp.).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cinsurance" id="cinsurance" value="200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne wydatki <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pozostałe miesięczne wydatki nie uwzględnione w powyższych kategoriach.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cotherexpenses" id="cotherexpenses" value="500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz zdolność" onclick="calculateCreditworthiness();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Maksymalny kredyt: &nbsp; 485 000 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBaZG9sbm/FmWNpIEtyZWR5dG93ZWo=', 0, 'S2Fsa3VsYXRvciBaZG9sbm/FmWNpIEtyZWR5dG93ZWo=', 'TWFrc3ltYWxueSBrcmVkeXQ=', 'NDg1IDAwMCB6xYI=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Kwota</b></td>
                                        <td align="right"><b>Procent</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Maksymalny kredyt</b></td>
                                        <td align="right"><b>485 000 zł</b></td>
                                        <td align="right"><b>100%</b></td>
                                    </tr>
                                    <tr>
                                        <td>Miesięczna rata</td>
                                        <td align="right">2 847 zł</td>
                                        <td align="right">35,6%</td>
                                    </tr>
                                    <tr>
                                        <td>Dochody netto</td>
                                        <td align="right">8 000 zł</td>
                                        <td align="right">-</td>
                                    </tr>
                                    <tr>
                                        <td>Wydatki stałe</td>
                                        <td align="right">4 700 zł</td>
                                        <td align="right">58,8%</td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Wolne środki</b></td>
                                        <td align="right">3 300 zł</td>
                                        <td align="right">41,3%</td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="padding-top: 15px;">
                                            <h3>Analiza zdolności kredytowej:</h3>
                                            <p><strong>Wynik pozytywny!</strong> Twoja zdolność kredytowa pozwala na uzyskanie kredytu do kwoty 485 000 zł.</p>
                                            <p><strong>Wskaźnik DTI:</strong> 35,6% (rekomendowane poniżej 40%)</p>
                                            <p><strong>Rezerwa finansowa:</strong> 453 zł miesięcznie</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <div style="padding: 20px 0;">
            <h2>Jak działa kalkulator zdolności kredytowej?</h2>
            <p>Nasz kalkulator zdolności kredytowej pomaga określić maksymalną kwotę kredytu hipotecznego, na którą możesz liczyć w banku. Obliczenia bazują na aktualnych standardach bankowych stosowanych w Polsce.</p>
            
            <h3>Czynniki wpływające na zdolność kredytową:</h3>
            <ul>
                <li><strong>Dochody netto</strong> - miesięczne dochody po odliczeniu podatków i składek</li>
                <li><strong>Wydatki stałe</strong> - wszystkie regularne miesięczne zobowiązania</li>
                <li><strong>Liczba osób w gospodarstwie</strong> - wpływa na koszty utrzymania</li>
                <li><strong>Okres kredytowania</strong> - dłuższy okres oznacza niższą ratę</li>
                <li><strong>Oprocentowanie</strong> - aktualne stopy procentowe kredytów hipotecznych</li>
            </ul>
            
            <h3>Wskaźnik DTI (Debt-to-Income)</h3>
            <p>Banki stosują wskaźnik DTI, który określa stosunek miesięcznych zobowiązań do dochodów. W Polsce rekomendowany poziom to maksymalnie 40-50% dochodów netto.</p>
            
            <h3>Porady dotyczące zwiększenia zdolności kredytowej:</h3>
            <ul>
                <li>Zwiększ dochody poprzez dodatkowe źródła zarobku</li>
                <li>Spłać istniejące kredyty i zobowiązania</li>
                <li>Rozważ dłuższy okres kredytowania</li>
                <li>Uwzględnij dochody współmałżonka</li>
                <li>Zmniejsz miesięczne wydatki stałe</li>
            </ul>
        </div>
    </div>
    
    <div id="right">
        <div id="othercalc">
            <div id="octitle">Popularne Kalkulatory</div>
            <div id="occontent">
                <a href="/">Kalkulator kredytu hipotecznego</a>
                <a href="kalkulator-raty-kredytu-hipotecznego.html">Kalkulator raty kredytu</a>
                <a href="kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłat</a>
                <a href="pko-kalkulator-kredytu-hipotecznego.html">PKO Kalkulator</a>
                <a href="ing-kalkulator-kredytu-hipotecznego.html">ING Kalkulator</a>
                <a href="mbank-kalkulator-kredytu-hipotecznego.html">mBank Kalkulator</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Popularne wyszukiwania</div>
                <div id="occontent">
                    <a href="kalkulator-raty-kredytu-hipotecznego.html">Kalkulator raty kredytu</a>
                    <a href="kalkulator-kosztow-kredytu-hipotecznego.html">Kalkulator kosztów</a>
                    <a href="szczegolowy-kalkulator-kredytu-hipotecznego.html">Szczegółowy kalkulator</a>
                    <a href="kalkulator-refinansowania-kredytu-hipotecznego.html">Refinansowanie</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="o-nas.html">O nas</a> |
            <a href="kontakt.html">Kontakt</a> |
            <a href="polityka-prywatnosci.html">Polityka prywatności</a> |
            <a href="regulamin.html">Regulamin</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Profesjonalne kalkulatory finansowe dla każdego. Sprawdź swoją zdolność kredytową, oblicz ratę kredytu hipotecznego i zaplanuj swoje finanse.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function cshexpenses() {
    var checkbox = document.getElementById('caddexpenses');
    var expensesDiv = document.getElementById('cexpenses');
    if (checkbox.checked) {
        expensesDiv.style.display = 'block';
    } else {
        expensesDiv.style.display = 'none';
    }
}
</script>

<script>
function calculateCreditworthiness() {
    // 获取输入值
    var monthlyIncome = parseFloat(document.getElementById('cmonthlyincome').value) || 0;
    var monthlyExpenses = parseFloat(document.getElementById('cmonthlyexpenses').value) || 0;
    var existingLoans = parseFloat(document.getElementById('cexistingloans').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 计算净收入
    var netIncome = monthlyIncome - monthlyExpenses - existingLoans;

    // 计算最大zdolność kredytowa (通常是净收入的80%)
    var maxMonthlyPayment = netIncome * 0.8;

    // 计算最大kwota kredytu
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var maxLoanAmount = 0;

    if (monthlyRate > 0) {
        maxLoanAmount = maxMonthlyPayment * (Math.pow(1 + monthlyRate, numPayments) - 1) /
                       (monthlyRate * Math.pow(1 + monthlyRate, numPayments));
    } else {
        maxLoanAmount = maxMonthlyPayment * numPayments;
    }

    // 计算wskaźniki zdolności
    var debtToIncomeRatio = ((existingLoans + maxMonthlyPayment) / monthlyIncome) * 100;
    var creditworthinessRatio = (netIncome / monthlyIncome) * 100;
    var loanToIncomeRatio = (maxLoanAmount / (monthlyIncome * 12)) * 100;

    // 分析zdolność kredytowa
    var creditworthinessAnalysis = analyzeCreditworthiness(debtToIncomeRatio, creditworthinessRatio,
                                                          netIncome, monthlyIncome);

    // 更新显示
    updateCreditworthinessResults(maxLoanAmount, maxMonthlyPayment, netIncome,
                                 debtToIncomeRatio, creditworthinessRatio, loanToIncomeRatio,
                                 creditworthinessAnalysis, loanTerm);
}

function analyzeCreditworthiness(debtRatio, creditRatio, netIncome, grossIncome) {
    var analysis = {
        rating: '',
        recommendation: '',
        riskLevel: '',
        improvementTips: ''
    };

    // Ocena zdolności kredytowej
    if (debtRatio <= 40 && creditRatio >= 60 && netIncome >= 3000) {
        analysis.rating = 'Bardzo dobra zdolność kredytowa';
        analysis.recommendation = 'Możesz ubiegać się o kredyt na preferencyjnych warunkach';
        analysis.riskLevel = 'Niskie ryzyko';
        analysis.improvementTips = 'Utrzymaj obecną sytuację finansową';
    } else if (debtRatio <= 50 && creditRatio >= 50 && netIncome >= 2000) {
        analysis.rating = 'Dobra zdolność kredytowa';
        analysis.recommendation = 'Możesz ubiegać się o kredyt na standardowych warunkach';
        analysis.riskLevel = 'Umiarkowane ryzyko';
        analysis.improvementTips = 'Rozważ zwiększenie dochodów lub zmniejszenie wydatków';
    } else if (debtRatio <= 60 && creditRatio >= 40 && netIncome >= 1500) {
        analysis.rating = 'Średnia zdolność kredytowa';
        analysis.recommendation = 'Kredyt możliwy, ale warunki mogą być mniej korzystne';
        analysis.riskLevel = 'Średnie ryzyko';
        analysis.improvementTips = 'Zmniejsz istniejące zobowiązania przed aplikacją o kredyt';
    } else if (debtRatio <= 70 && creditRatio >= 30 && netIncome >= 1000) {
        analysis.rating = 'Niska zdolność kredytowa';
        analysis.recommendation = 'Kredyt trudny do uzyskania, wymagane dodatkowe zabezpieczenia';
        analysis.riskLevel = 'Wysokie ryzyko';
        analysis.improvementTips = 'Znacznie popraw sytuację finansową przed aplikacją';
    } else {
        analysis.rating = 'Bardzo niska zdolność kredytowa';
        analysis.recommendation = 'Kredyt prawdopodobnie niemożliwy do uzyskania';
        analysis.riskLevel = 'Bardzo wysokie ryzyko';
        analysis.improvementTips = 'Konieczna gruntowna poprawa sytuacji finansowej';
    }

    return analysis;
}

function updateCreditworthinessResults(maxLoanAmount, maxMonthlyPayment, netIncome,
                                      debtRatio, creditRatio, loanToIncomeRatio,
                                      analysis, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Zdolność kredytowa: &nbsp; ' + formatNumber(maxLoanAmount) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły zdolności
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegółowa analiza zdolności kredytowej:</h3>' +
            '<p><strong>Maksymalna kwota kredytu:</strong> ' + formatNumber(maxLoanAmount) + ' zł</p>' +
            '<p><strong>Maksymalna rata miesięczna:</strong> ' + formatNumber(maxMonthlyPayment) + ' zł</p>' +
            '<p><strong>Dochód netto dostępny:</strong> ' + formatNumber(netIncome) + ' zł</p>' +
            '<p><strong>Wskaźnik zadłużenia:</strong> ' + debtRatio.toFixed(1) + '% (zalecane max 50%)</p>' +
            '<p><strong>Wskaźnik zdolności:</strong> ' + creditRatio.toFixed(1) + '% (zalecane min 50%)</p>' +
            '<p><strong>Kredyt do rocznych dochodów:</strong> ' + loanToIncomeRatio.toFixed(1) + ' razy</p>' +
            '<p><strong>Ocena zdolności:</strong> ' + analysis.rating + '</p>' +
            '<p><strong>Poziom ryzyka:</strong> ' + analysis.riskLevel + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + analysis.recommendation + '</p>' +
            '<p><strong>Wskazówki:</strong> ' + analysis.improvementTips + '</p>';
    }
}

function clearForm(form) {
    if (document.getElementById('cmonthlyincome')) document.getElementById('cmonthlyincome').value = '8000';
    if (document.getElementById('cmonthlyexpenses')) document.getElementById('cmonthlyexpenses').value = '3000';
    if (document.getElementById('cexistingloans')) document.getElementById('cexistingloans').value = '500';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.5';

    calculateCreditworthiness();
}

function saveCalResult() {
    alert('Analiza zdolności kredytowej została zapisana!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateCreditworthiness();
};
</script>

</body>
</html>
