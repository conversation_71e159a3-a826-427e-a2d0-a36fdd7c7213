# 简化的链接检查脚本
Write-Host "=== 检查HTML文件中的404错误 ===" -ForegroundColor Green

# 获取所有HTML文件
$htmlFiles = Get-ChildItem -Name "*.html" | Where-Object { 
    $_ -notmatch "test|TEST|debug|WORKING|FINAL|check-|fix-" 
}

Write-Host "找到 $($htmlFiles.Count) 个HTML文件进行检查"

$allLinks = @()
$linkSources = @{}

# 检查每个HTML文件
foreach ($file in $htmlFiles) {
    Write-Host "检查文件: $file"
    
    $content = Get-Content $file -Raw -Encoding UTF8 -ErrorAction SilentlyContinue
    if ($content) {
        # 提取href链接
        $matches = [regex]::Matches($content, 'href="([^"]+\.html)"')
        
        foreach ($match in $matches) {
            $link = $match.Groups[1].Value
            
            # 过滤链接
            if ($link -notmatch "^http" -and 
                $link -notmatch "^//" -and 
                $link -notmatch "^/konto/" -and 
                $link -notmatch "^/kalkulatory-" -and 
                $link -match "\.html$") {
                
                # 移除开头的 /
                $cleanLink = $link -replace "^/", ""
                
                if ($allLinks -notcontains $cleanLink) {
                    $allLinks += $cleanLink
                }
                
                if (-not $linkSources.ContainsKey($cleanLink)) {
                    $linkSources[$cleanLink] = @()
                }
                $linkSources[$cleanLink] += $file
            }
        }
    }
}

Write-Host "总共找到 $($allLinks.Count) 个唯一的内部链接"

# 检查链接是否存在
$existingLinks = @()
$missingLinks = @()

foreach ($link in $allLinks) {
    if (Test-Path $link) {
        $existingLinks += $link
        Write-Host "✅ $link - 存在" -ForegroundColor Green
    } else {
        $missingLinks += $link
        Write-Host "❌ $link - 缺失" -ForegroundColor Red
        
        if ($linkSources.ContainsKey($link)) {
            $sources = $linkSources[$link] -join ", "
            Write-Host "   被以下文件引用: $sources" -ForegroundColor Gray
        }
    }
}

# 显示总结
Write-Host ""
Write-Host "=== 总结 ===" -ForegroundColor Green
Write-Host "✅ 存在的链接: $($existingLinks.Count)" -ForegroundColor Green
Write-Host "❌ 缺失的链接: $($missingLinks.Count)" -ForegroundColor Red

if ($missingLinks.Count -gt 0) {
    Write-Host ""
    Write-Host "=== 缺失的链接列表 ===" -ForegroundColor Red
    foreach ($link in $missingLinks) {
        Write-Host "- $link" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "=== 修复建议 ===" -ForegroundColor Yellow
    foreach ($link in $missingLinks) {
        $template = "o-nas.html"
        
        if ($link -match "kalkulator") {
            if ($existingLinks -contains "kalkulator-raty-kredytu-hipotecznego.html") {
                $template = "kalkulator-raty-kredytu-hipotecznego.html"
            }
        }
        
        if ($link -match "pko") { $template = "pko-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "ing") { $template = "ing-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "mbank") { $template = "mbank-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "santander") { $template = "santander-kalkulator-kredytu-hipotecznego.html" }
        
        Write-Host "Copy-Item '$template' '$link'" -ForegroundColor Yellow
    }
} else {
    Write-Host ""
    Write-Host "🎉 恭喜！没有发现404错误！" -ForegroundColor Green
}

# 保存报告
$report = @{
    timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
    total_html_files = $htmlFiles.Count
    total_unique_links = $allLinks.Count
    existing_links = $existingLinks.Count
    missing_links = $missingLinks.Count
    success_rate = if ($allLinks.Count -gt 0) { [math]::Round(($existingLinks.Count / $allLinks.Count) * 100, 2) } else { 100 }
    existing_links_list = $existingLinks | Sort-Object
    missing_links_list = $missingLinks | Sort-Object
}

$report | ConvertTo-Json -Depth 2 | Out-File -FilePath "link-report.json" -Encoding UTF8
Write-Host ""
Write-Host "报告已保存到: link-report.json" -ForegroundColor Green
