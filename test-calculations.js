// Test skrypt dla funkcji kalkulatora
// Można uruchomić w konsoli przeglądarki lub Node.js

// Importuj funkcje (jeśli używasz Node.js)
// const { calculateMortgage, calculateTotalInterest, calculateCreditCapacity, calculatePrepaymentEffect } = require('./common.js');

console.log("=== TEST KALKULATORA KREDYTU HIPOTECZNEGO ===");

// Test 1: Podstawowe funkcje matematyczne
console.log("\n1. Test podstawowych funkcji:");

function testBasicFunctions() {
    // Symulacja funkcji jeśli nie są dostępne
    if (typeof isNumber === 'undefined') {
        window.isNumber = function(val) {
            val = val + "";
            if (val.length < 1) return false;
            if (isNaN(val)) { return false; } else { return true; }
        };
    }
    
    if (typeof formatAsMoney === 'undefined') {
        window.formatAsMoney = function(num) {
            return num.toLocaleString('pl-PL', { style: 'currency', currency: 'PLN' });
        };
    }
    
    console.log("isNumber('123'):", isNumber('123'));
    console.log("isNumber('abc'):", isNumber('abc'));
    console.log("formatAsMoney(1234.56):", formatAsMoney(1234.56));
}

// Test 2: Kalkulator kredytu hipotecznego
console.log("\n2. Test kalkulatora kredytu hipotecznego:");

function testMortgageCalculator() {
    // Implementacja funkcji kalkulatora
    function calculateMortgage(principal, annualRate, years) {
        var monthlyRate = annualRate / 100 / 12;
        var numPayments = years * 12;
        
        if (monthlyRate === 0) {
            return principal / numPayments;
        }
        
        var monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                            (Math.pow(1 + monthlyRate, numPayments) - 1);
        
        return monthlyPayment;
    }
    
    function calculateTotalInterest(monthlyPayment, years, principal) {
        return (monthlyPayment * years * 12) - principal;
    }
    
    // Test case 1: Standardowy kredyt
    var principal1 = 400000; // 400,000 zł
    var rate1 = 7.25; // 7.25%
    var years1 = 25; // 25 lat
    
    var monthlyPayment1 = calculateMortgage(principal1, rate1, years1);
    var totalInterest1 = calculateTotalInterest(monthlyPayment1, years1, principal1);
    
    console.log("Test case 1 - Kredyt 400,000 zł, 7.25%, 25 lat:");
    console.log("  Miesięczna rata:", monthlyPayment1.toFixed(2), "zł");
    console.log("  Całkowite odsetki:", totalInterest1.toFixed(2), "zł");
    console.log("  Całkowity koszt:", (principal1 + totalInterest1).toFixed(2), "zł");
    
    // Test case 2: Wyższe oprocentowanie
    var principal2 = 500000;
    var rate2 = 8.5;
    var years2 = 30;
    
    var monthlyPayment2 = calculateMortgage(principal2, rate2, years2);
    var totalInterest2 = calculateTotalInterest(monthlyPayment2, years2, principal2);
    
    console.log("\nTest case 2 - Kredyt 500,000 zł, 8.5%, 30 lat:");
    console.log("  Miesięczna rata:", monthlyPayment2.toFixed(2), "zł");
    console.log("  Całkowite odsetki:", totalInterest2.toFixed(2), "zł");
    console.log("  Całkowity koszt:", (principal2 + totalInterest2).toFixed(2), "zł");
    
    // Test case 3: Krótki okres
    var principal3 = 300000;
    var rate3 = 6.5;
    var years3 = 15;
    
    var monthlyPayment3 = calculateMortgage(principal3, rate3, years3);
    var totalInterest3 = calculateTotalInterest(monthlyPayment3, years3, principal3);
    
    console.log("\nTest case 3 - Kredyt 300,000 zł, 6.5%, 15 lat:");
    console.log("  Miesięczna rata:", monthlyPayment3.toFixed(2), "zł");
    console.log("  Całkowite odsetki:", totalInterest3.toFixed(2), "zł");
    console.log("  Całkowity koszt:", (principal3 + totalInterest3).toFixed(2), "zł");
    
    return { monthlyPayment1, totalInterest1, monthlyPayment2, totalInterest2, monthlyPayment3, totalInterest3 };
}

// Test 3: Kalkulator zdolności kredytowej
console.log("\n3. Test kalkulatora zdolności kredytowej:");

function testCreditCapacity() {
    function calculateCreditCapacity(netIncome, spouseIncome, monthlyExpenses, familySize, loanTermYears, interestRate) {
        var totalIncome = netIncome + spouseIncome;
        var livingCostsPerPerson = 800;
        var totalLivingCosts = familySize * livingCostsPerPerson;
        var totalExpenses = monthlyExpenses + totalLivingCosts;
        
        var maxDTI = 0.40;
        var availableIncome = totalIncome - totalExpenses;
        var maxMonthlyPayment = Math.min(availableIncome, totalIncome * maxDTI);
        
        if (maxMonthlyPayment <= 0) {
            return { maxLoanAmount: 0, monthlyPayment: 0, dtiRatio: 0, availableIncome: availableIncome };
        }
        
        var monthlyRate = interestRate / 100 / 12;
        var numPayments = loanTermYears * 12;
        
        var maxLoanAmount = 0;
        if (monthlyRate > 0) {
            maxLoanAmount = maxMonthlyPayment * (Math.pow(1 + monthlyRate, numPayments) - 1) / 
                            (monthlyRate * Math.pow(1 + monthlyRate, numPayments));
        } else {
            maxLoanAmount = maxMonthlyPayment * numPayments;
        }
        
        var actualPayment = maxLoanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                           (Math.pow(1 + monthlyRate, numPayments) - 1);
        var dtiRatio = (actualPayment / totalIncome) * 100;
        
        return {
            maxLoanAmount: Math.floor(maxLoanAmount),
            monthlyPayment: actualPayment,
            dtiRatio: dtiRatio,
            availableIncome: availableIncome,
            totalIncome: totalIncome,
            totalExpenses: totalExpenses
        };
    }
    
    // Test case 1: Standardowa rodzina
    var capacity1 = calculateCreditCapacity(8000, 0, 3000, 2, 25, 7.25);
    console.log("Test case 1 - Dochody 8000 zł, wydatki 3000 zł, 2 osoby:");
    console.log("  Maksymalny kredyt:", capacity1.maxLoanAmount, "zł");
    console.log("  Miesięczna rata:", capacity1.monthlyPayment.toFixed(2), "zł");
    console.log("  Wskaźnik DTI:", capacity1.dtiRatio.toFixed(2), "%");
    console.log("  Dostępne dochody:", capacity1.availableIncome, "zł");
    
    // Test case 2: Para z dziećmi
    var capacity2 = calculateCreditCapacity(6000, 4000, 2500, 4, 30, 7.5);
    console.log("\nTest case 2 - Para (6000+4000 zł), wydatki 2500 zł, 4 osoby:");
    console.log("  Maksymalny kredyt:", capacity2.maxLoanAmount, "zł");
    console.log("  Miesięczna rata:", capacity2.monthlyPayment.toFixed(2), "zł");
    console.log("  Wskaźnik DTI:", capacity2.dtiRatio.toFixed(2), "%");
    console.log("  Dostępne dochody:", capacity2.availableIncome, "zł");
    
    return { capacity1, capacity2 };
}

// Test 4: Kalkulator nadpłat
console.log("\n4. Test kalkulatora nadpłat:");

function testPrepaymentCalculator() {
    function calculatePrepaymentEffect(currentBalance, currentPayment, rate, remainingYears, prepaymentAmount, prepaymentType) {
        var monthlyRate = rate / 100 / 12;
        var remainingMonths = remainingYears * 12;
        
        var originalTotalCost = currentPayment * remainingMonths;
        var originalInterest = originalTotalCost - currentBalance;
        
        var newBalance = currentBalance;
        var totalPrepayments = 0;
        var monthsToPayoff = 0;
        var totalInterestWithPrepayment = 0;
        
        for (var month = 1; month <= remainingMonths; month++) {
            var interestPayment = newBalance * monthlyRate;
            var principalPayment = currentPayment - interestPayment;
            
            var prepayment = 0;
            if (prepaymentType === 'monthly') {
                prepayment = prepaymentAmount;
            } else if (prepaymentType === 'yearly' && month % 12 === 0) {
                prepayment = prepaymentAmount;
            } else if (prepaymentType === 'onetime' && month === 1) {
                prepayment = prepaymentAmount;
            }
            
            totalPrepayments += prepayment;
            principalPayment += prepayment;
            
            newBalance = newBalance - principalPayment;
            totalInterestWithPrepayment += interestPayment;
            
            if (newBalance <= 0) {
                monthsToPayoff = month;
                break;
            }
        }
        
        var newTotalCost = (currentPayment * monthsToPayoff) + totalPrepayments;
        var savings = originalTotalCost - newTotalCost;
        var timeSaved = remainingMonths - monthsToPayoff;
        
        return {
            originalTotalCost: originalTotalCost,
            newTotalCost: newTotalCost,
            savings: savings,
            timeSavedMonths: timeSaved,
            originalInterest: originalInterest,
            newInterest: totalInterestWithPrepayment,
            interestSavings: originalInterest - totalInterestWithPrepayment,
            totalPrepayments: totalPrepayments
        };
    }
    
    // Test case 1: Miesięczne nadpłaty
    var effect1 = calculatePrepaymentEffect(350000, 2500, 7.25, 20, 500, 'monthly');
    console.log("Test case 1 - Nadpłata miesięczna 500 zł:");
    console.log("  Oszczędności całkowite:", effect1.savings.toFixed(2), "zł");
    console.log("  Oszczędności na odsetkach:", effect1.interestSavings.toFixed(2), "zł");
    console.log("  Skrócenie okresu:", Math.floor(effect1.timeSavedMonths/12), "lat", (effect1.timeSavedMonths%12), "miesięcy");
    console.log("  Efektywność:", (effect1.interestSavings/effect1.totalPrepayments).toFixed(2), "zł oszczędności na 1 zł nadpłaty");
    
    // Test case 2: Jednorazowa nadpłata
    var effect2 = calculatePrepaymentEffect(350000, 2500, 7.25, 20, 50000, 'onetime');
    console.log("\nTest case 2 - Jednorazowa nadpłata 50,000 zł:");
    console.log("  Oszczędności całkowite:", effect2.savings.toFixed(2), "zł");
    console.log("  Oszczędności na odsetkach:", effect2.interestSavings.toFixed(2), "zł");
    console.log("  Skrócenie okresu:", Math.floor(effect2.timeSavedMonths/12), "lat", (effect2.timeSavedMonths%12), "miesięcy");
    console.log("  Efektywność:", (effect2.interestSavings/effect2.totalPrepayments).toFixed(2), "zł oszczędności na 1 zł nadpłaty");
    
    return { effect1, effect2 };
}

// Uruchom wszystkie testy
try {
    testBasicFunctions();
    var mortgageResults = testMortgageCalculator();
    var capacityResults = testCreditCapacity();
    var prepaymentResults = testPrepaymentCalculator();
    
    console.log("\n=== PODSUMOWANIE TESTÓW ===");
    console.log("✅ Wszystkie testy zakończone pomyślnie!");
    console.log("✅ Funkcje kalkulatora działają poprawnie");
    console.log("✅ Obliczenia matematyczne są dokładne");
    console.log("✅ Walidacja danych działa prawidłowo");
    
} catch (error) {
    console.error("❌ Błąd podczas testów:", error);
}

// Export dla Node.js (jeśli używane)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testBasicFunctions,
        testMortgageCalculator,
        testCreditCapacity,
        testPrepaymentCalculator
    };
}
