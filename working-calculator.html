<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Kalkulator kredytu hipotecznego - WORKING VERSION</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #fff; }
        .clefthalf { float: left; width: 45%; }
        .crighthalf { float: right; width: 45%; }
        #clear { clear: both; }
        table { width: 100%; border-collapse: collapse; }
        td { padding: 8px; border: 1px solid #ddd; }
        input, select { padding: 5px; }
        input[type="button"] { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .h2result { font-size: 20px; font-weight: bold; color: #333; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Kalkulator kredytu hipotecznego - WORKING VERSION</h1>
    
    <div class="clefthalf">
        <form name="calform">
            <table>
                <tr>
                    <td><PERSON><PERSON></td>
                    <td><input type="text" name="chouseprice" id="chouseprice" value="500000"></td>
                    <td>zł</td>
                </tr>
                <tr>
                    <td>Wkład własny</td>
                    <td><input type="text" name="cdownpayment" id="cdownpayment" value="20"></td>
                    <td>
                        <select name="cdownpaymentunit" id="cdownpaymentunit">
                            <option value="p" selected>%</option>
                            <option value="d">zł</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>Okres kredytowania</td>
                    <td><input type="text" name="cloanterm" id="cloanterm" value="25"></td>
                    <td>lat</td>
                </tr>
                <tr>
                    <td>Oprocentowanie</td>
                    <td><input type="text" name="cinterestrate" id="cinterestrate" value="7.25"></td>
                    <td>%</td>
                </tr>
                <tr>
                    <td colspan="3" align="center">
                        <input type="button" name="x" value="Oblicz" onclick="calc();">
                        <input type="button" value="Wyczyść" onclick="reset();">
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <div class="crighthalf">
        <table>
            <tr>
                <td>
                    <h2 class="h2result" id="result">Miesięczna rata: 0 zł</h2>
                    <table cellpadding="3" width="100%">
                        <tr>
                            <td></td>
                            <td align="right"><b>Miesięcznie</b></td>
                            <td align="right"><b>Łącznie</b></td>
                        </tr>
                        <tr bgcolor="#dddddd">
                            <td><b>Rata kredytu</b></td>
                            <td align="right" id="monthly"><b>0 zł</b></td>
                            <td align="right" id="total"><b>0 zł</b></td>
                        </tr>
                        <tr>
                            <td>Podatek od nieruchomości</td>
                            <td align="right">0 zł</td>
                            <td align="right">0 zł</td>
                        </tr>
                        <tr>
                            <td>Ubezpieczenie domu</td>
                            <td align="right">0 zł</td>
                            <td align="right">0 zł</td>
                        </tr>
                        <tr>
                            <td>Ubezpieczenie kredytu</td>
                            <td align="right">0 zł</td>
                            <td align="right">0 zł</td>
                        </tr>
                        <tr>
                            <td>Opłaty administracyjne</td>
                            <td align="right">0 zł</td>
                            <td align="right">0 zł</td>
                        </tr>
                        <tr bgcolor="#dddddd">
                            <td><b>Łącznie</b></td>
                            <td align="right" id="monthlyTotal"><b>0 zł</b></td>
                            <td align="right" id="totalCost"><b>0 zł</b></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    
    <div id="clear"></div>

    <script>
        function calc() {
            var price = parseFloat(document.getElementById('chouseprice').value) || 0;
            var down = parseFloat(document.getElementById('cdownpayment').value) || 0;
            var unit = document.getElementById('cdownpaymentunit').value;
            var years = parseFloat(document.getElementById('cloanterm').value) || 0;
            var rate = parseFloat(document.getElementById('cinterestrate').value) || 0;
            
            var downAmount = (unit === 'p') ? price * (down / 100) : down;
            var loanAmount = price - downAmount;
            var monthlyRate = rate / 100 / 12;
            var numPayments = years * 12;
            var monthlyPayment = (monthlyRate > 0) ? 
                loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1) : 
                loanAmount / numPayments;
            
            var totalPayment = monthlyPayment * numPayments;
            
            function format(num) {
                return Math.round(num).toLocaleString('pl-PL') + ' zł';
            }
            
            document.getElementById('result').innerHTML = 'Miesięczna rata: ' + format(monthlyPayment);
            document.getElementById('monthly').innerHTML = '<b>' + format(monthlyPayment) + '</b>';
            document.getElementById('total').innerHTML = '<b>' + format(totalPayment) + '</b>';
            document.getElementById('monthlyTotal').innerHTML = '<b>' + format(monthlyPayment) + '</b>';
            document.getElementById('totalCost').innerHTML = '<b>' + format(totalPayment) + '</b>';
        }
        
        function reset() {
            document.getElementById('chouseprice').value = '500000';
            document.getElementById('cdownpayment').value = '20';
            document.getElementById('cdownpaymentunit').value = 'p';
            document.getElementById('cloanterm').value = '25';
            document.getElementById('cinterestrate').value = '7.25';
            calc();
        }
        
        // Auto calculate on load
        window.onload = function() {
            calc();
        };
    </script>
</body>
</html>
