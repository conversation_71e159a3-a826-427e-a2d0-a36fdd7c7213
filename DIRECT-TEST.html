<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>直接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 20px 0; padding: 15px; border: 2px solid #007bff; background: #f8f9fa; }
        button { padding: 15px 30px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; font-size: 18px; }
        .big { padding: 20px 40px; font-size: 24px; background: #28a745; }
        iframe { width: 100%; height: 500px; border: 2px solid #ccc; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 直接测试计算器</h1>
    
    <div class="test">
        <h2>步骤1: 基础JavaScript测试</h2>
        <button class="big" onclick="alert('✅ JavaScript工作正常！如果你看到这个提示，说明JavaScript已启用。')">点击测试JavaScript</button>
        <p><strong>如果点击按钮没有弹出提示框，说明JavaScript被禁用，需要在浏览器设置中启用。</strong></p>
    </div>
    
    <div class="test">
        <h2>步骤2: 测试修复后的计算器</h2>
        <button onclick="testCalculator()">加载在线计算器</button>
        <button onclick="testCalculator2()">加载分期计算器</button>
        <button onclick="testCalculator3()">加载ING计算器</button>
        <div id="calculatorFrame"></div>
    </div>
    
    <div class="test">
        <h2>步骤3: 如果计算器不工作</h2>
        <p><strong>可能的原因和解决方案：</strong></p>
        <ol>
            <li><strong>JavaScript被禁用</strong> - 在浏览器设置中启用JavaScript</li>
            <li><strong>浏览器版本太旧</strong> - 更新到最新版本</li>
            <li><strong>浏览器扩展干扰</strong> - 使用隐私模式测试</li>
            <li><strong>本地文件限制</strong> - 使用本地服务器或上传到网站</li>
        </ol>
    </div>
    
    <div class="test">
        <h2>步骤4: 手动测试说明</h2>
        <p><strong>在加载的计算器中：</strong></p>
        <ol>
            <li>找到"Oblicz"按钮（可能是"Oblicz online"、"Oblicz ratę"等）</li>
            <li>点击按钮</li>
            <li>检查页面顶部是否显示计算结果</li>
            <li>或者检查是否弹出alert显示结果</li>
        </ol>
        <p><strong>预期结果：月供约2,847 zł</strong></p>
    </div>

    <script>
        function testCalculator() {
            var iframe = document.createElement('iframe');
            iframe.src = 'kalkulator-kredytu-hipotecznego-online.html';
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '2px solid #007bff';
            
            var container = document.getElementById('calculatorFrame');
            container.innerHTML = '<h3>🧪 测试: 在线计算器</h3><p>加载中...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.style.background = '#fff3cd';
                instructions.style.padding = '15px';
                instructions.style.border = '1px solid #ffeaa7';
                instructions.style.margin = '10px 0';
                instructions.innerHTML = 
                    '<h4>📋 测试步骤：</h4>' +
                    '<ol>' +
                    '<li>等待计算器完全加载</li>' +
                    '<li>找到并点击"Oblicz online"按钮</li>' +
                    '<li>检查页面顶部是否显示"Rata online: X zł"</li>' +
                    '<li>或检查是否弹出alert显示结果</li>' +
                    '</ol>' +
                    '<p><strong>如果什么都没发生，说明JavaScript被禁用或有其他问题。</strong></p>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        function testCalculator2() {
            var iframe = document.createElement('iframe');
            iframe.src = 'kalkulator-raty-kredytu-hipotecznego.html';
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '2px solid #007bff';
            
            var container = document.getElementById('calculatorFrame');
            container.innerHTML = '<h3>🧪 测试: 分期计算器</h3><p>加载中...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.style.background = '#fff3cd';
                instructions.style.padding = '15px';
                instructions.style.border = '1px solid #ffeaa7';
                instructions.style.margin = '10px 0';
                instructions.innerHTML = 
                    '<h4>📋 测试步骤：</h4>' +
                    '<ol>' +
                    '<li>等待计算器完全加载</li>' +
                    '<li>找到并点击"Oblicz ratę"按钮</li>' +
                    '<li>检查页面顶部是否显示"Rata kredytu: X zł"</li>' +
                    '<li>或检查是否弹出alert显示结果</li>' +
                    '</ol>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        function testCalculator3() {
            var iframe = document.createElement('iframe');
            iframe.src = 'ing-kalkulator-kredytu-hipotecznego.html';
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '2px solid #007bff';
            
            var container = document.getElementById('calculatorFrame');
            container.innerHTML = '<h3>🧪 测试: ING计算器</h3><p>加载中...</p>';
            container.appendChild(iframe);
            
            setTimeout(function() {
                var instructions = document.createElement('div');
                instructions.style.background = '#fff3cd';
                instructions.style.padding = '15px';
                instructions.style.border = '1px solid #ffeaa7';
                instructions.style.margin = '10px 0';
                instructions.innerHTML = 
                    '<h4>📋 测试步骤：</h4>' +
                    '<ol>' +
                    '<li>等待计算器完全加载</li>' +
                    '<li>找到并点击"Oblicz w ING"按钮</li>' +
                    '<li>检查页面顶部是否显示"Rata ING: X zł"</li>' +
                    '<li>或检查是否弹出alert显示结果</li>' +
                    '</ol>';
                container.appendChild(instructions);
            }, 2000);
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log("测试页面已加载");
        };
    </script>
</body>
</html>
