<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego Pekao</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego Pekao - oblicz ratę kredytu mieszkaniowego w Pekao SA, sprawdź warunki i promocje Pekao na kredyty hipoteczne 2025.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego pekao, pekao sa kredyt hipoteczny, rata kredytu pekao, warunki kredytu pekao">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-pekao.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego pekao</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego Pekao</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w Pekao SA"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-pekao.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości w Pekao SA. Na podstawie tej kwoty Pekao obliczy maksymalną kwotę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpekaopropertyvalue" id="cpekaopropertyvalue" value="800000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny w Pekao SA. Minimum 10% wartości nieruchomości. Wyższy wkład = lepsza marża Pekao.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="cpekaodownpayment" id="cpekaodownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="cpekaodownpaymentunit" onchange="cunitchangepekao('cpekaodownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w Pekao SA. Maksymalnie 35 lat. Dłuższy okres = niższa rata Pekao.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpekaoloanterm" id="cpekaoloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w Pekao SA. WIBOR 3M + marża Pekao.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cpekaointerestrate" id="cpekaointerestrate" value="7.55" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Profil klienta Pekao</td>
                                <td align="left" colspan="2">
                                    <select name="cpekaoclientprofile" id="cpekaoclientprofile" onchange="updatePekaoRate();">
                                        <option value="new_pekao" selected="">Nowy klient Pekao</option>
                                        <option value="existing_pekao">Obecny klient Pekao</option>
                                        <option value="premium_pekao">Pekao Premium</option>
                                        <option value="private_pekao">Pekao Private Banking</option>
                                        <option value="business_pekao">Pekao Business</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania Pekao</td>
                                <td align="left" colspan="2">
                                    <select name="cpekaointeresttype" id="cpekaointeresttype" onchange="updatePekaoInterestType();">
                                        <option value="variable_pekao" selected="">Zmienne Pekao (WIBOR + marża)</option>
                                        <option value="fixed_3y_pekao">Stałe na 3 lata Pekao</option>
                                        <option value="fixed_5y_pekao">Stałe na 5 lat Pekao</option>
                                        <option value="fixed_10y_pekao">Stałe na 10 lat Pekao</option>
                                        <option value="mixed_pekao">Mieszane Pekao</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpekaofees" class="cbcontainer">
                                        <input type="checkbox" name="caddpekaofees" id="caddpekaofees" value="1" checked="" onclick="cshpekaofees();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpekaofeesdesc">Opłaty Pekao</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpekaofees" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty i koszty Pekao</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przygotowawcza Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja Pekao SA za przygotowanie kredytu. 0-2.5% kwoty kredytu w zależności od profilu klienta Pekao.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpekaoprovision" id="cpekaoprovision" value="2.0" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rachunek kredytowy Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w Pekao SA. 0-25 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpekaoaccount" id="cpekaoaccount" value="18" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w Pekao SA. 0.20-0.40% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpekaoinsurance" id="cpekaoinsurance" value="0.32" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę Pekao SA. 1200-2000 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpekaovaluation" id="cpekaovaluation" value="1600" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty Pekao <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty Pekao SA: notariusz, wpis hipoteki, ubezpieczenie nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpekaoother" id="cpekaoother" value="4200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddpekaopromo" class="cbcontainer">
                                        <input type="checkbox" name="caddpekaopromo" id="caddpekaopromo" value="1" onclick="cshpekaopromo();">
                                        <span class="cbmark"></span>
                                        <b><span id="cpekaopromodesc">Promocje Pekao 2025</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="cpekaopromo" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Cashback Pekao</td>
                                                    <td align="right"><input type="text" name="cpekaocashback" id="cpekaocashback" value="2500" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zwolnienie z prowizji Pekao</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cpekaoprovisionwaiver" id="cpekaoprovisionwaiver">
                                                            <option value="0" selected="">Brak zwolnienia</option>
                                                            <option value="25">25% zwolnienia</option>
                                                            <option value="50">50% zwolnienia</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Promocyjna marża Pekao</td>
                                                    <td align="left" colspan="2">
                                                        <select name="cpekaopromomargin" id="cpekaopromomargin">
                                                            <option value="0" selected="">Standardowa marża</option>
                                                            <option value="0.20">-0.20% przez 12 miesięcy</option>
                                                            <option value="0.35">-0.35% przez 6 miesięcy</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz kredyt Pekao" onclick="calculatePekaoMortgageAlt();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata Pekao: &nbsp; 4 685 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBQZWthbw==', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBQZWthbw==', 'UmF0YSBQZWTHBW8=', 'NCA2ODUgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Pekao</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata Pekao</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>4 685 zł</b></td>
                                        <td align="right"><b>1 405 500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu Pekao</td>
                                        <td align="right">640 000 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">640 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał + odsetki Pekao</td>
                                        <td align="right">-</td>
                                        <td align="right">4 525 zł</td>
                                        <td align="right">1 357 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie Pekao</td>
                                        <td align="right">-</td>
                                        <td align="right">171 zł</td>
                                        <td align="right">51 300 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Rachunek kredytowy Pekao</td>
                                        <td align="right">-</td>
                                        <td align="right">18 zł</td>
                                        <td align="right">5 400 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty jednorazowe Pekao</td>
                                        <td align="right">18 600 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">18 600 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Szczegóły kredytu Pekao:</h3>
                                            <p><strong>RRSO Pekao:</strong> 8.45% (z wszystkimi opłatami Pekao)</p>
                                            <p><strong>Status klienta:</strong> Nowy klient Pekao SA (marża standardowa)</p>
                                            <p><strong>Typ oprocentowania:</strong> Zmienne WIBOR + marża Pekao</p>
                                            <p><strong>Całkowity koszt Pekao:</strong> 1 432 800 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 792 800 zł (124% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie profili klientów Pekao</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Profil Pekao</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">4200 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">4400 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4600 zł</text>
                    
                    <!-- Pekao Private Banking -->
                    <rect x="50" y="115" width="35" height="30" fill="#1e3a8a" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">Private</text>
                    <text x="67" y="110" class="mcllabelx" style="fill:#000;">4385 zł</text>
                    
                    <!-- Pekao Premium -->
                    <rect x="95" y="105" width="35" height="40" fill="#3b82f6" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">Premium</text>
                    <text x="112" y="100" class="mcllabelx" style="fill:#000;">4485 zł</text>
                    
                    <!-- Pekao Business -->
                    <rect x="140" y="95" width="35" height="50" fill="#60a5fa" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">Business</text>
                    <text x="157" y="90" class="mcllabelx" style="fill:#000;">4585 zł</text>
                    
                    <!-- Obecny klient -->
                    <rect x="185" y="85" width="35" height="60" fill="#93c5fd" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">Obecny</text>
                    <text x="202" y="80" class="mcllabelx" style="fill:#000;">4635 zł</text>
                    
                    <!-- Nowy klient -->
                    <rect x="230" y="75" width="35" height="70" fill="#dbeafe" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">Nowy</text>
                    <text x="247" y="70" class="mcllabelx" style="fill:#000;">4685 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Profile klientów Pekao &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania Pekao</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Typ oprocentowania Pekao</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Różnica</th>
                        </tr>
                        <tr>
                            <td>Zmienne WIBOR + marża</td>
                            <td>7.55%</td>
                            <td>4 685 zł</td>
                            <td>1 405 500 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Stałe na 3 lata Pekao</td>
                            <td>7.95%</td>
                            <td>4 885 zł</td>
                            <td>1 465 500 zł</td>
                            <td>+60 000 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 5 lat Pekao</td>
                            <td>8.25%</td>
                            <td>5 085 zł</td>
                            <td>1 525 500 zł</td>
                            <td>+120 000 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 10 lat Pekao</td>
                            <td>8.65%</td>
                            <td>5 285 zł</td>
                            <td>1 585 500 zł</td>
                            <td>+180 000 zł</td>
                        </tr>
                        <tr>
                            <td>Mieszane Pekao</td>
                            <td>7.75%</td>
                            <td>4 785 zł</td>
                            <td>1 435 500 zł</td>
                            <td>+30 000 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego Pekao - tradycyjny bank z długą historią 2025</h2>
            <p>Pekao SA to jeden z najstarszych i najbardziej tradycyjnych banków w Polsce z ponad 90-letnią historią. Nasz kalkulator kredytu hipotecznego Pekao pomoże Ci precyzyjnie obliczyć ratę kredytu mieszkaniowego, sprawdzić warunki i porównać profile klientów Pekao SA - banku o ugruntowanej pozycji na polskim rynku finansowym.</p>

            <h3>Profile klientów Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Pekao Private Banking:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 6.95% (najniższa marża Pekao)</li>
                    <li><strong>Wymagania:</strong> Aktywa minimum 500 000 zł w Pekao</li>
                    <li><strong>Korzyści:</strong> Dedykowany doradca, ekskluzywna obsługa VIP</li>
                    <li><strong>Prowizja:</strong> 0.5% - 1.5% (negocjowalna)</li>
                    <li><strong>Dodatkowe usługi:</strong> Zarządzanie portfelem, inwestycje, concierge</li>
                    <li><strong>Dostępność:</strong> Ograniczona, tylko zamożni klienci Pekao</li>
                </ul>

                <h4>Pekao Premium:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.15% (preferencyjne warunki Pekao)</li>
                    <li><strong>Wymagania:</strong> Dochody 12 000+ zł netto, pakiet Premium Pekao</li>
                    <li><strong>Korzyści:</strong> Obniżone opłaty, priorytetowa obsługa Pekao</li>
                    <li><strong>Prowizja:</strong> 1% - 2%</li>
                    <li><strong>Dodatkowe usługi:</strong> Ubezpieczenia, karty premium Pekao</li>
                    <li><strong>Dostępność:</strong> Dobra, dla klientów o wysokich dochodach</li>
                </ul>

                <h4>Pekao Business:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.25% (specjalna oferta dla przedsiębiorców)</li>
                    <li><strong>Wymagania:</strong> Działalność gospodarcza, współpraca biznesowa z Pekao</li>
                    <li><strong>Korzyści:</strong> Powiązanie z kredytami biznesowymi, kompleksowa obsługa</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2%</li>
                    <li><strong>Dodatkowe usługi:</strong> Kredyty biznesowe, faktoring, leasing</li>
                    <li><strong>Dostępność:</strong> Dobra, dla przedsiębiorców współpracujących z Pekao</li>
                </ul>

                <h4>Obecny klient Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.35% (preferencje dla stałych klientów)</li>
                    <li><strong>Wymagania:</strong> Minimum 12 miesięcy jako klient Pekao</li>
                    <li><strong>Korzyści:</strong> Obniżona marża, uproszczona procedura</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2.5%</li>
                    <li><strong>Dodatkowe usługi:</strong> Możliwość negocjacji warunków Pekao</li>
                    <li><strong>Dostępność:</strong> Bardzo dobra dla klientów Pekao</li>
                </ul>

                <h4>Nowy klient Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.55% (standardowe warunki Pekao)</li>
                    <li><strong>Wymagania:</strong> Standardowe wymagania kredytowe</li>
                    <li><strong>Korzyści:</strong> Dostęp do pełnej oferty Pekao</li>
                    <li><strong>Prowizja:</strong> 2% - 2.5%</li>
                    <li><strong>Dodatkowe usługi:</strong> Pakiety startowe Pekao</li>
                    <li><strong>Dostępność:</strong> Powszechna, dla wszystkich klientów</li>
                </ul>
            </div>

            <h3>Typy oprocentowania w Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <h4>Zmienne oprocentowanie Pekao (WIBOR + marża):</h4>
                <ul>
                    <li><strong>Bazowe oprocentowanie:</strong> WIBOR 3M + marża Pekao</li>
                    <li><strong>Aktualna marża:</strong> 3.35% - 4.35% w zależności od profilu</li>
                    <li><strong>Korzyści:</strong> Standardowe oprocentowanie rynkowe</li>
                    <li><strong>Ryzyko:</strong> Zmienność wraz z WIBOR</li>
                    <li><strong>Rekomendacja:</strong> Dla klientów akceptujących ryzyko</li>
                </ul>

                <h4>Stałe oprocentowanie na 3 lata Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.40% do zmiennego Pekao</li>
                    <li><strong>Okres stały:</strong> 36 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Pekao</li>
                    <li><strong>Korzyści:</strong> Krótkoterminowa stabilność</li>
                    <li><strong>Rekomendacja:</strong> Dla ostrożnych na początku</li>
                </ul>

                <h4>Stałe oprocentowanie na 5 lat Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.70% do zmiennego Pekao</li>
                    <li><strong>Okres stały:</strong> 60 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Pekao</li>
                    <li><strong>Korzyści:</strong> Średnioterminowa przewidywalność</li>
                    <li><strong>Rekomendacja:</strong> Dla planujących stabilność</li>
                </ul>

                <h4>Stałe oprocentowanie na 10 lat Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +1.10% do zmiennego Pekao</li>
                    <li><strong>Okres stały:</strong> 120 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne Pekao</li>
                    <li><strong>Korzyści:</strong> Długoterminowa stabilność</li>
                    <li><strong>Rekomendacja:</strong> Dla maksymalnej przewidywalności</li>
                </ul>

                <h4>Mieszane oprocentowanie Pekao:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.20% do zmiennego Pekao</li>
                    <li><strong>Mechanizm:</strong> Część stała, część zmienna</li>
                    <li><strong>Elastyczność:</strong> Kompromis między stabilnością a kosztem</li>
                    <li><strong>Korzyści:</strong> Ograniczone ryzyko zmian</li>
                    <li><strong>Rekomendacja:</strong> Dla umiarkowanie konserwatywnych</li>
                </ul>
            </div>

            <h3>Opłaty i koszty Pekao SA:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Rodzaj opłaty Pekao</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wysokość</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Uwagi Pekao</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja przygotowawcza</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.5% - 2.5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zależna od profilu klienta Pekao</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rachunek kredytowy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0 - 25 zł/miesiąc</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Darmowy dla Premium Pekao</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie spłaty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.20% - 0.40%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rocznie od kwoty kredytu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wycena nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1200 - 2000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Przez rzeczoznawcę Pekao</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">600 - 1600 zł/rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Obowiązkowe w Pekao</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Koszty notarialne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2800 - 5000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zależne od kwoty kredytu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wpis hipoteki</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">200 - 600 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Opłata sądowa</td>
                    </tr>
                </table>
            </div>

            <h3>Promocje Pekao SA 2025:</h3>
            <ul>
                <li><strong>Cashback Pekao:</strong> Do 2 500 zł zwrotu dla nowych klientów</li>
                <li><strong>Zwolnienie z prowizji:</strong> 25% - 50% dla wybranych profili Pekao</li>
                <li><strong>Promocyjna marża:</strong> -0.20% przez 12 miesięcy lub -0.35% przez 6 miesięcy</li>
                <li><strong>Darmowa wycena:</strong> Dla klientów Premium i Private Banking Pekao</li>
                <li><strong>Pakiet startowy:</strong> Darmowe prowadzenie konta przez 3 miesiące</li>
                <li><strong>Ubezpieczenia grupowe:</strong> Preferencyjne stawki dla klientów Pekao</li>
                <li><strong>Program lojalnościowy:</strong> Punkty za długoterminową współpracę</li>
            </ul>

            <h3>Zalety kredytu hipotecznego w Pekao:</h3>
            <ul>
                <li><strong>Długa tradycja:</strong> Ponad 90 lat doświadczenia na rynku</li>
                <li><strong>Stabilność finansowa:</strong> Solidne fundamenty i bezpieczeństwo</li>
                <li><strong>Szeroka sieć:</strong> Ponad 800 oddziałów w całej Polsce</li>
                <li><strong>Tradycyjna obsługa:</strong> Osobisty kontakt z doradcą</li>
                <li><strong>Elastyczne warunki:</strong> Możliwość negocjacji parametrów kredytu</li>
                <li><strong>Kompleksowa oferta:</strong> Pełna gama produktów bankowych</li>
                <li><strong>Profesjonalna obsługa:</strong> Doświadczeni specjaliści kredytowi</li>
                <li><strong>Bezpieczeństwo:</strong> Gwarancja Bankowego Funduszu Gwarancyjnego</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/pko-kalkulator-kredytu-hipotecznego.html">PKO kalkulator kredytu</a>
                <a href="/mbank-kalkulator-kredytu-hipotecznego.html">mBank kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/nadplata-kredytu-hipotecznego-kalkulator.html">Kalkulator nadpłaty</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Profile klientów Pekao</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz profil Pekao:</strong><br>
                        <a href="#" onclick="return setPekaoProfile('private_pekao');">Pekao Private Banking</a><br>
                        <a href="#" onclick="return setPekaoProfile('premium_pekao');">Pekao Premium</a><br>
                        <a href="#" onclick="return setPekaoProfile('business_pekao');">Pekao Business</a><br>
                        <a href="#" onclick="return setPekaoProfile('existing_pekao');">Obecny klient Pekao</a><br>
                        <a href="#" onclick="return setPekaoProfile('new_pekao');">Nowy klient Pekao</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego Pekao - oblicz ratę kredytu mieszkaniowego w Pekao SA, sprawdź warunki i promocje.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updatePekaoRate() {
    var profile = document.getElementById('cpekaoclientprofile').value;
    var rateField = document.getElementById('cpekaointerestrate');
    var baseRate = 7.55;
    
    switch(profile) {
        case 'private_pekao':
            rateField.value = (baseRate - 0.60).toFixed(2);
            break;
        case 'premium_pekao':
            rateField.value = (baseRate - 0.40).toFixed(2);
            break;
        case 'business_pekao':
            rateField.value = (baseRate - 0.30).toFixed(2);
            break;
        case 'existing_pekao':
            rateField.value = (baseRate - 0.20).toFixed(2);
            break;
        case 'new_pekao':
            rateField.value = baseRate.toFixed(2);
            break;
    }
}

function updatePekaoInterestType() {
    var type = document.getElementById('cpekaointeresttype').value;
    var rateField = document.getElementById('cpekaointerestrate');
    var baseRate = 7.55;
    
    switch(type) {
        case 'variable_pekao':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'fixed_3y_pekao':
            rateField.value = (baseRate + 0.40).toFixed(2);
            break;
        case 'fixed_5y_pekao':
            rateField.value = (baseRate + 0.70).toFixed(2);
            break;
        case 'fixed_10y_pekao':
            rateField.value = (baseRate + 1.10).toFixed(2);
            break;
        case 'mixed_pekao':
            rateField.value = (baseRate + 0.20).toFixed(2);
            break;
    }
}

function cshpekaofees() {
    var checkbox = document.getElementById('caddpekaofees');
    var feesDiv = document.getElementById('cpekaofees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshpekaopromo() {
    var checkbox = document.getElementById('caddpekaopromo');
    var promoDiv = document.getElementById('cpekaopromo');
    if (checkbox.checked) {
        promoDiv.style.display = 'block';
    } else {
        promoDiv.style.display = 'none';
    }
}

function setPekaoProfile(profile) {
    document.getElementById('cpekaoclientprofile').value = profile;
    updatePekaoRate();
    return false;
}

function cunitchangepekao(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania Pekao &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Profile klientów Pekao</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Profile klientów Pekao &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania Pekao</a>';
    }
    return false;
}
</script>

<script>
function calculatePekaoMortgageAlt() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 获取Pekao特殊信息
    var pekaoProfile = document.getElementById('cpekaoprofile').value;
    var pekaoProgram = document.getElementById('cpekaoprogram').value;
    var pekaoAdvantage = document.getElementById('cpekaoadvantage').value;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // 调整利率基于Pekao程序
    var adjustedRate = adjustPekaoAltRate(interestRate, pekaoProfile, pekaoProgram, pekaoAdvantage);

    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 获取Pekao费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }

    // Pekao特殊korzyści
    var pekaoBenefits = getPekaoAltBenefits(pekaoProfile, pekaoProgram, pekaoAdvantage);

    // 应用Pekao korzyści
    if (pekaoBenefits.reducedProvision) costs.provision *= 0.5;
    if (pekaoBenefits.freeAccount) costs.accountFee = 0;
    if (pekaoBenefits.discountInsurance) costs.lifeInsurance *= 0.75;
    if (pekaoBenefits.freeValuation) costs.valuation = 0;

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees +
                   costs.valuation + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // 更新显示
    updatePekaoAltResults(loanAmount, monthlyPayment, totalInterest, costs,
                         totalCost, monthlyTotal, rrso, loanTerm, pekaoProfile,
                         pekaoProgram, pekaoBenefits, adjustedRate);
}

function adjustPekaoAltRate(baseRate, profile, program, advantage) {
    var adjustment = 0;

    // 基于客户档案调整
    switch(profile) {
        case 'premium': adjustment -= 0.3; break;
        case 'private': adjustment -= 0.5; break;
        case 'young': adjustment -= 0.2; break;
        case 'existing': adjustment -= 0.1; break;
        case 'corporate': adjustment -= 0.4; break; // Pekao企业客户
    }

    // 基于程序调整
    switch(program) {
        case 'first': adjustment -= 0.3; break;
        case 'family': adjustment -= 0.2; break;
        case 'green': adjustment -= 0.4; break;
        case 'investment': adjustment += 0.2; break;
        case 'loyalty': adjustment -= 0.35; break; // Pekao忠诚客户
    }

    // 基于优势调整
    switch(advantage) {
        case 'long_relationship': adjustment -= 0.15; break;
        case 'high_income': adjustment -= 0.1; break;
        case 'multiple_products': adjustment -= 0.2; break;
    }

    return Math.max(baseRate + adjustment, 2.0); // Pekao最低2.0%
}

function getPekaoAltBenefits(profile, program, advantage) {
    var benefits = {
        reducedProvision: false,
        freeAccount: false,
        discountInsurance: false,
        freeValuation: false,
        loyaltyBonus: false,
        corporateService: false,
        priorityService: false
    };

    // Premium和wyższe korzyści
    if (profile === 'premium' || profile === 'private') {
        benefits.freeAccount = true;
        benefits.discountInsurance = true;
        benefits.priorityService = true;
    }

    // Corporate korzyści
    if (profile === 'corporate') {
        benefits.reducedProvision = true;
        benefits.freeValuation = true;
        benefits.corporateService = true;
    }

    // Program korzyści
    if (program === 'loyalty') {
        benefits.loyaltyBonus = true;
        benefits.discountInsurance = true;
    }

    return benefits;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updatePekaoAltResults(loanAmount, monthlyPayment, totalInterest, costs,
                              totalCost, monthlyTotal, rrso, loanTerm, pekaoProfile,
                              pekaoProgram, pekaoBenefits, adjustedRate) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Kredyt Pekao: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły kredytu Pekao
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var benefitsText = '';
        if (pekaoBenefits.reducedProvision) benefitsText += '• 50% zniżka na prowizję<br>';
        if (pekaoBenefits.freeAccount) benefitsText += '• Bezpłatne prowadzenie rachunku<br>';
        if (pekaoBenefits.discountInsurance) benefitsText += '• 25% zniżka na ubezpieczenie<br>';
        if (pekaoBenefits.freeValuation) benefitsText += '• Bezpłatna wycena nieruchomości<br>';
        if (pekaoBenefits.loyaltyBonus) benefitsText += '• Bonus za lojalność<br>';
        if (pekaoBenefits.corporateService) benefitsText += '• Obsługa korporacyjna<br>';
        if (pekaoBenefits.priorityService) benefitsText += '• Obsługa priorytetowa<br>';

        detailsSection.innerHTML =
            '<h3>Szczegóły kredytu w Banku Pekao SA:</h3>' +
            '<p><strong>RRSO Pekao:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi opłatami Banku Pekao SA)</p>' +
            '<p><strong>Efektywne oprocentowanie:</strong> ' + adjustedRate.toFixed(2) + '% (po zniżkach Pekao)</p>' +
            '<p><strong>Status klienta:</strong> ' + getPekaoAltClientStatus(pekaoProfile) + '</p>' +
            '<p><strong>Program kredytowy:</strong> ' + getPekaoAltProgramName(pekaoProgram) + '</p>' +
            '<p><strong>Korzyści Pekao:</strong><br>' + (benefitsText || 'Standardowe warunki') + '</p>' +
            '<p><strong>Całkowity koszt Pekao:</strong> ' + formatNumber(totalCost) + ' zł (kredyt + wszystkie opłaty)</p>';
    }
}

function getPekaoAltClientStatus(profile) {
    switch(profile) {
        case 'premium': return 'Klient Pekao Premium (marża preferencyjna -0.3%)';
        case 'private': return 'Pekao Private Banking (najlepsza marża -0.5%)';
        case 'corporate': return 'Klient korporacyjny Pekao (marża preferencyjna -0.4%)';
        case 'young': return 'Pekao dla młodych (marża promocyjna -0.2%)';
        case 'existing': return 'Obecny klient Pekao (marża preferencyjna -0.1%)';
        default: return 'Nowy klient Banku Pekao SA';
    }
}

function getPekaoAltProgramName(program) {
    switch(program) {
        case 'first': return 'Pierwszy kredyt Pekao (dla młodych)';
        case 'family': return 'Kredyt rodzinny Pekao (z dopłatami)';
        case 'green': return 'Kredyt ekologiczny Pekao (energooszczędny)';
        case 'investment': return 'Kredyt inwestycyjny Pekao';
        case 'loyalty': return 'Program lojalnościowy Pekao (dla stałych klientów)';
        default: return 'Kredyt hipoteczny standardowy Pekao';
    }
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '500000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.15';
    if (document.getElementById('cpekaoprofile')) document.getElementById('cpekaoprofile').value = 'new';
    if (document.getElementById('cpekaoprogram')) document.getElementById('cpekaoprogram').value = 'standard';
    if (document.getElementById('cpekaoadvantage')) document.getElementById('cpekaoadvantage').value = 'none';
    calculatePekaoMortgageAlt();
}

function saveCalResult() {
    alert('Wyniki kalkulatora Pekao zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculatePekaoMortgageAlt();
};
</script>

</body>
</html>
