<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Simple Calculator Test</title>
</head>
<body>
    <h1>Simple Calculator Test</h1>
    
    <p>House Price: <input type="text" id="chouseprice" value="500000"></p>
    <p>Down Payment: <input type="text" id="cdownpayment" value="20"></p>
    <p>Unit: <select id="cdownpaymentunit"><option value="p">%</option><option value="d">zł</option></select></p>
    <p>Loan Term: <input type="text" id="cloanterm" value="25"></p>
    <p>Interest Rate: <input type="text" id="cinterestrate" value="7.25"></p>
    
    <button onclick="calculateMortgage()">Calculate</button>
    
    <h2 class="h2result">Result: 0 zł</h2>
    
    <div id="debug"></div>

    <script>
        function calculateMortgage() {
            document.getElementById('debug').innerHTML = "Function called!";
            
            var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
            var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
            var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
            var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
            var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
            
            document.getElementById('debug').innerHTML += "<br>Values: " + housePrice + ", " + downPayment + ", " + downPaymentUnit + ", " + loanTerm + ", " + interestRate;
            
            // Calculate down payment amount
            var downPaymentAmount;
            if (downPaymentUnit === 'p') {
                downPaymentAmount = housePrice * (downPayment / 100);
            } else {
                downPaymentAmount = downPayment;
            }
            var loanAmount = housePrice - downPaymentAmount;
            
            // Calculate monthly payment
            var monthlyRate = interestRate / 100 / 12;
            var numPayments = loanTerm * 12;
            var monthlyPayment = 0;
            
            if (monthlyRate > 0) {
                monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                (Math.pow(1 + monthlyRate, numPayments) - 1);
            } else {
                monthlyPayment = loanAmount / numPayments;
            }
            
            document.getElementById('debug').innerHTML += "<br>Monthly Payment: " + Math.round(monthlyPayment);
            
            // Update result
            var resultHeader = document.querySelector('.h2result');
            if (resultHeader) {
                resultHeader.innerHTML = 'Result: ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł';
                document.getElementById('debug').innerHTML += "<br>Updated h2result successfully!";
            } else {
                document.getElementById('debug').innerHTML += "<br>ERROR: h2result not found!";
            }
        }
    </script>
</body>
</html>
