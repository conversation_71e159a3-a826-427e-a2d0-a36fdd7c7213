<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JavaScript测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test { margin: 20px 0; padding: 15px; border: 2px solid #007bff; background: #f8f9fa; }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        button { padding: 10px 20px; margin: 10px 5px; background: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; }
        .big-button { padding: 20px 40px; font-size: 20px; background: #28a745; }
        input { padding: 8px; margin: 5px; font-size: 16px; }
        #result { font-size: 18px; font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 JavaScript功能测试</h1>
    
    <div class="test">
        <h2>测试1：基础JavaScript</h2>
        <p><strong>点击下面的按钮，如果弹出提示框，说明JavaScript工作正常：</strong></p>
        <button class="big-button" onclick="alert('✅ JavaScript工作正常！')">点击测试JavaScript</button>
    </div>
    
    <div class="test">
        <h2>测试2：DOM操作</h2>
        <p><strong>点击按钮，如果下面显示文字，说明DOM操作正常：</strong></p>
        <button onclick="document.getElementById('test2').innerHTML='✅ DOM操作正常！'">测试DOM操作</button>
        <div id="test2" style="margin: 10px 0; font-size: 18px; color: #28a745;"></div>
    </div>
    
    <div class="test">
        <h2>测试3：计算功能</h2>
        <p><strong>输入数字并点击计算：</strong></p>
        <input type="number" id="num1" value="500000" placeholder="房价">
        <input type="number" id="num2" value="20" placeholder="首付%">
        <button onclick="
            var price = parseFloat(document.getElementById('num1').value) || 500000;
            var down = parseFloat(document.getElementById('num2').value) || 20;
            var downAmount = price * (down / 100);
            var loanAmount = price - downAmount;
            var monthlyRate = 7.25 / 100 / 12;
            var numPayments = 25 * 12;
            var monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1);
            document.getElementById('result').innerHTML = '月供: ' + Math.round(monthlyPayment).toLocaleString() + ' zł';
        ">计算月供</button>
        <div id="result">结果将显示在这里</div>
    </div>
    
    <div class="test">
        <h2>如果以上测试都失败</h2>
        <p><strong style="color: #dc3545;">JavaScript被禁用，请按以下步骤启用：</strong></p>
        <ol>
            <li><strong>Chrome:</strong> 设置 → 隐私设置和安全性 → 网站设置 → JavaScript → 允许</li>
            <li><strong>Firefox:</strong> 地址栏输入 about:config → 搜索 javascript.enabled → 设为 true</li>
            <li><strong>Edge:</strong> 设置 → Cookie和网站权限 → JavaScript → 开启</li>
        </ol>
    </div>
    
    <div class="test success">
        <h2>如果测试成功</h2>
        <p><strong style="color: #28a745;">恭喜！JavaScript工作正常，现在可以测试计算器了：</strong></p>
        <ol>
            <li>打开任何计算器文件</li>
            <li>点击"Oblicz"按钮</li>
            <li>应该能看到计算结果</li>
        </ol>
    </div>
    
    <script>
        // 页面加载时自动测试
        window.onload = function() {
            // 如果这行代码执行，说明JavaScript基本工作
            console.log("JavaScript已加载");
            
            // 自动执行计算测试
            setTimeout(function() {
                var price = 500000;
                var down = 20;
                var downAmount = price * (down / 100);
                var loanAmount = price - downAmount;
                var monthlyRate = 7.25 / 100 / 12;
                var numPayments = 25 * 12;
                var monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1);
                
                if (document.getElementById('result')) {
                    document.getElementById('result').innerHTML = '自动计算结果: ' + Math.round(monthlyPayment).toLocaleString() + ' zł';
                }
            }, 1000);
        };
    </script>
</body>
</html>
