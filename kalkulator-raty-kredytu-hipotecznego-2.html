<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator raty kredytu hipotecznego</title>
    <meta name="description" content="Darmowy kalkulator raty kredytu hipotecznego do obliczania miesięcznej raty, całkowitego kosztu kredytu i harmonogramu spłat z opcjami podatków, ubezpieczenia i wcześniejszej spłaty.">
    <meta name="keywords" content="kalkulator raty kredytu hipotecznego, rata kredytu, oprocentowanie, kredyt mieszkaniowy, harmonogram spłat">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-raty-kredytu-hipotecznego-2.html" itemprop="item"><span itemprop="name">kalkulator raty kredytu hipotecznego</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator raty kredytu hipotecznego</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Zmodyfikuj wartości i kliknij przycisk oblicz, aby użyć"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-raty-kredytu-hipotecznego-2.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Kwota kredytu</td>
                                <td align="right"><input type="text" name="cloanamount" id="cloanamount" value="400000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Czas spłaty kredytu w latach. Dłuższy okres oznacza niższą miesięczną ratę, ale wyższy całkowity koszt kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczna stopa procentowa kredytu. Może być stała przez cały okres lub zmienna.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Typ raty</td>
                                <td align="left" colspan="2">
                                    <select name="cpaymenttype" id="cpaymenttype">
                                        <option value="annuity" selected="">Annuitetowa (stała)</option>
                                        <option value="decreasing">Malejąca</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Data rozpoczęcia</td>
                                <td align="left" colspan="2">
                                    <select name="cstartmonth" id="cstartmonth">
                                        <option value="1">Styczeń</option>
                                        <option value="2">Luty</option>
                                        <option value="3">Marzec</option>
                                        <option value="4">Kwiecień</option>
                                        <option value="5">Maj</option>
                                        <option value="6">Czerwiec</option>
                                        <option value="7">Lipiec</option>
                                        <option value="8" selected="">Sierpień</option>
                                        <option value="9">Wrzesień</option>
                                        <option value="10">Październik</option>
                                        <option value="11">Listopad</option>
                                        <option value="12">Grudzień</option>
                                    </select> 
                                    <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddcosts" class="cbcontainer">
                                        <input type="checkbox" name="caddcosts" id="caddcosts" value="1" checked="" onclick="cshcosts();">
                                        <span class="cbmark"></span>
                                        <b><span id="ccostsdesc">Uwzględnij dodatkowe koszty</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ccosts" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Miesięczne koszty</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna składka ubezpieczenia spłaty kredytu. Chroni przed niespłaceniem kredytu w przypadku utraty zdolności do pracy.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ccreditinsurance" id="ccreditinsurance" value="150" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna składka ubezpieczenia nieruchomości od ognia i innych zdarzeń losowych. Wymagane przez bank.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cpropertyinsurance" id="cpropertyinsurance" value="100" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja bankowa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego i obsługę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cbankfee" id="cbankfee" value="20" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe miesięczne opłaty związane z kredytem (np. opłaty za przelewy, wyciągi, itp.).', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="cotherfees" id="cotherfees" value="30" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz ratę" onclick="calculateInstallment();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Miesięczna rata: &nbsp; 2 847 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBSYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 0, 'S2Fsa3VsYXRvciBSYXR5IEtyZWR5dHUgSGlwb3RlY3puZWdv', 'TWllc2nEmWN6bmEgcmF0YQ==', 'MiA4NDcgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata kredytu</b></td>
                                        <td align="right"><b>2 847 zł</b></td>
                                        <td align="right"><b>854 100 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie kredytu</td>
                                        <td align="right">150 zł</td>
                                        <td align="right">45 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie nieruchomości</td>
                                        <td align="right">100 zł</td>
                                        <td align="right">30 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Prowizja bankowa</td>
                                        <td align="right">20 zł</td>
                                        <td align="right">6 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Inne opłaty</td>
                                        <td align="right">30 zł</td>
                                        <td align="right">9 000 zł</td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Całkowity koszt miesięczny</b></td>
                                        <td align="right">3 147 zł</td>
                                        <td align="right">944 100 zł</td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="padding-top: 15px;">
                                            <table cellpadding="3" width="100%">
                                                <tr bgcolor="#f0f0f0">
                                                    <td><b>Szczegóły kredytu:</b></td>
                                                    <td align="right">&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td>Kwota kredytu</td>
                                                    <td align="right">400 000 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowita kwota do spłaty</td>
                                                    <td align="right">854 100 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Całkowite odsetki</td>
                                                    <td align="right">454 100 zł</td>
                                                </tr>
                                                <tr>
                                                    <td>Data zakończenia spłaty</td>
                                                    <td align="right">Sierpień 2050</td>
                                                </tr>
                                                <tr>
                                                    <td>Liczba rat</td>
                                                    <td align="right">300</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Harmonogram spłat</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Rok</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">0 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">200K zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">400K zł</text>
                    <line x1="45" y1="37.542" x2="292" y2="37.542" class="mclgrid"></line>
                    <text x="43" y="37.542" class="mcllabely">600K zł</text>
                    
                    <path d="M 48 87.689 L 292 145" fill="none" stroke="#2b7ddb" stroke-width="3"></path>
                    <path d="M 48 145 L 292 71.835" fill="none" stroke="#8bbc21" stroke-width="3"></path>
                    <path d="M 48 145 L 292 14.524" fill="none" stroke="#910000" stroke-width="3"></path>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                    <rect x="53" y="17" width="20" height="6" style="fill:#2b7ddb;"></rect>
                    <text x="78" y="24" class="mcllegend">Saldo</text>
                    <rect x="53" y="35" width="20" height="6" style="fill:#8bbc21;"></rect>
                    <text x="78" y="42" class="mcllegend">Odsetki</text>
                    <rect x="53" y="53" width="20" height="6" style="fill:#910000;"></rect>
                    <text x="78" y="60" class="mcllegend">Płatność</text>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram miesięczny</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Miesiąc</th>
                            <th>Rata</th>
                            <th>Kapitał</th>
                            <th>Odsetki</th>
                            <th>Saldo</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Jak obliczyć ratę kredytu hipotecznego?</h2>
            <p>Nasz kalkulator raty kredytu hipotecznego pomaga precyzyjnie obliczyć miesięczną ratę kredytu mieszkaniowego. Kalkulator uwzględnia wszystkie kluczowe parametry wpływające na wysokość raty oraz całkowity koszt kredytu.</p>

            <h3>Główne parametry wpływające na ratę kredytu:</h3>
            <ul>
                <li><strong>Kwota kredytu</strong> - suma pożyczona od banku na zakup nieruchomości</li>
                <li><strong>Okres kredytowania</strong> - czas spłaty kredytu (zwykle 10-30 lat)</li>
                <li><strong>Oprocentowanie</strong> - koszt kredytu wyrażony w skali rocznej</li>
                <li><strong>Typ raty</strong> - annuitetowa (stała) lub malejąca</li>
                <li><strong>Dodatkowe koszty</strong> - ubezpieczenia, prowizje, opłaty</li>
            </ul>

            <h3>Rodzaje rat kredytowych:</h3>
            <div style="margin: 15px 0;">
                <h4>Rata annuitetowa (stała):</h4>
                <ul>
                    <li>Stała wysokość raty przez cały okres kredytowania</li>
                    <li>Na początku większa część to odsetki, pod koniec większa część to kapitał</li>
                    <li>Łatwiejsze planowanie budżetu domowego</li>
                    <li>Wyższy całkowity koszt kredytu</li>
                    <li>Najpopularniejszy typ raty w Polsce</li>
                </ul>

                <h4>Rata malejąca:</h4>
                <ul>
                    <li>Stała część kapitałowa, malejące odsetki</li>
                    <li>Najwyższa rata na początku, stopniowo maleje</li>
                    <li>Niższy całkowity koszt kredytu</li>
                    <li>Wymaga wyższej zdolności kredytowej na początku</li>
                    <li>Szybsza spłata kapitału</li>
                </ul>
            </div>

            <h3>Wzór na ratę annuitetową:</h3>
            <p>Rata = K × [r × (1+r)^n] / [(1+r)^n - 1]</p>
            <p>Gdzie:</p>
            <ul>
                <li><strong>K</strong> - kwota kredytu</li>
                <li><strong>r</strong> - miesięczna stopa procentowa (roczna/12)</li>
                <li><strong>n</strong> - liczba rat (lata × 12)</li>
            </ul>

            <h3>Dodatkowe koszty miesięczne:</h3>
            <ul>
                <li><strong>Ubezpieczenie kredytu</strong> - 0,1-0,5% kwoty kredytu rocznie</li>
                <li><strong>Ubezpieczenie nieruchomości</strong> - 0,1-0,3% wartości nieruchomości rocznie</li>
                <li><strong>Prowizja bankowa</strong> - 10-50 zł miesięcznie</li>
                <li><strong>Inne opłaty</strong> - przelewy, wyciągi, SMS-y</li>
            </ul>

            <h3>Czynniki wpływające na oprocentowanie:</h3>
            <ul>
                <li><strong>Stopy procentowe NBP</strong> - podstawa dla oprocentowania zmiennego</li>
                <li><strong>Marża banku</strong> - zysk banku (zwykle 2-4%)</li>
                <li><strong>Wkład własny</strong> - wyższy wkład = niższe oprocentowanie</li>
                <li><strong>Zdolność kredytowa</strong> - lepsza zdolność = lepsze warunki</li>
                <li><strong>Okres kredytowania</strong> - krótszy okres = niższe oprocentowanie</li>
                <li><strong>Ubezpieczenia</strong> - dodatkowe ubezpieczenia mogą obniżyć oprocentowanie</li>
            </ul>

            <h3>Porady dotyczące optymalizacji raty:</h3>
            <ul>
                <li>Porównaj oferty różnych banków</li>
                <li>Negocjuj warunki, szczególnie marżę</li>
                <li>Rozważ wpłatę wyższego wkładu własnego</li>
                <li>Sprawdź możliwość wcześniejszej spłaty bez prowizji</li>
                <li>Monitoruj zmiany stóp procentowych</li>
                <li>Rozważ refinansowanie przy spadku stóp</li>
                <li>Uwzględnij wszystkie koszty w budżecie</li>
            </ul>

            <h3>Kiedy warto wybrać ratę malejącą?</h3>
            <ul>
                <li>Gdy masz wysoką zdolność kredytową</li>
                <li>Gdy spodziewasz się wzrostu dochodów</li>
                <li>Gdy chcesz minimalizować całkowity koszt kredytu</li>
                <li>Gdy planujesz wcześniejszą spłatę</li>
                <li>Gdy masz stabilną sytuację finansową</li>
            </ul>

            <h3>Harmonogram spłat - co pokazuje?</h3>
            <p>Nasz kalkulator generuje szczegółowy harmonogram spłat, który pokazuje:</p>
            <ul>
                <li>Podział każdej raty na kapitał i odsetki</li>
                <li>Pozostałe saldo kredytu po każdej racie</li>
                <li>Całkowitą kwotę odsetek do zapłaty</li>
                <li>Datę zakończenia spłaty kredytu</li>
                <li>Graficzną wizualizację spłaty</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/kalkulator-zdolnosci-kredytowej.html">Kalkulator zdolności kredytowej</a>
                <a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
                <a href="/kalkulator-kredytu.html">Kalkulator kredytu</a>
                <a href="/kalkulator-leasingu.html">Kalkulator leasingu</a>
                <a href="/kalkulator-oszczednosci.html">Kalkulator oszczędności</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Porównaj stopy procentowe</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Aktualne stawki:</strong><br>
                        25 lat: <a href="#" onclick="return setRate('7.25', '25');">7.25%</a><br>
                        20 lat: <a href="#" onclick="return setRate('6.95', '20');">6.95%</a><br>
                        15 lat: <a href="#" onclick="return setRate('6.75', '15');">6.75%</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Oblicz ratę kredytu hipotecznego szybko i dokładnie. Porównaj różne warianty i wybierz najlepszą opcję finansowania nieruchomości.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function cshcosts() {
    var checkbox = document.getElementById('caddcosts');
    var costsDiv = document.getElementById('ccosts');
    if (checkbox.checked) {
        costsDiv.style.display = 'block';
    } else {
        costsDiv.style.display = 'none';
    }
}

function setRate(rate, term) {
    document.getElementById('cinterestrate').value = rate;
    document.getElementById('cloanterm').value = term;
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram miesięczny &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Harmonogram roczny</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Harmonogram roczny &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram miesięczny</a>';
    }
    return false;
}
</script>

<script>
function calculateInstallment() {
    // 获取输入值
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var installmentType = document.getElementById('cinstallmenttype').value;
    var paymentFrequency = document.getElementById('cpaymentfreq').value;

    // 计算基于频率的参数
    var paymentsPerYear = getPaymentsPerYear(paymentFrequency);
    var periodRate = interestRate / 100 / paymentsPerYear;
    var numPayments = loanTerm * paymentsPerYear;

    var installmentAmount = 0;
    var totalInterest = 0;
    var totalCost = 0;

    // 计算不同类型的分期付款
    if (installmentType === 'equal') {
        // 等额本息
        if (periodRate > 0) {
            installmentAmount = loanAmount * (periodRate * Math.pow(1 + periodRate, numPayments)) /
                              (Math.pow(1 + periodRate, numPayments) - 1);
        } else {
            installmentAmount = loanAmount / numPayments;
        }
        totalCost = installmentAmount * numPayments;
        totalInterest = totalCost - loanAmount;
    } else if (installmentType === 'decreasing') {
        // 等额本金
        var principalPayment = loanAmount / numPayments;
        var firstInterest = loanAmount * periodRate;
        installmentAmount = principalPayment + firstInterest; // 第一期分期

        // 计算总利息（等差数列求和）
        var lastInterest = principalPayment * periodRate;
        totalInterest = (firstInterest + lastInterest) * numPayments / 2;
        totalCost = loanAmount + totalInterest;
    } else if (installmentType === 'interest_only') {
        // 只付利息
        installmentAmount = loanAmount * periodRate;
        totalInterest = installmentAmount * numPayments;
        totalCost = loanAmount + totalInterest; // 最后还本金
    }

    // 计算年化数据
    var annualInstallment = installmentAmount * paymentsPerYear;
    var installmentRatio = (installmentAmount / loanAmount) * 100;

    // 计算分期付款分析
    var installmentAnalysis = analyzeInstallment(loanAmount, installmentAmount, totalInterest,
                                               installmentType, paymentFrequency, loanTerm);

    // 更新显示
    updateInstallmentResults(loanAmount, installmentAmount, totalInterest, totalCost,
                           annualInstallment, installmentRatio, loanTerm, installmentType,
                           paymentFrequency, installmentAnalysis);
}

function getPaymentsPerYear(frequency) {
    switch(frequency) {
        case 'monthly': return 12;
        case 'quarterly': return 4;
        case 'semi_annual': return 2;
        case 'annual': return 1;
        case 'weekly': return 52;
        case 'bi_weekly': return 26;
        default: return 12;
    }
}

function analyzeInstallment(loanAmount, installmentAmount, totalInterest, type, frequency, term) {
    var analysis = {
        affordability: '',
        efficiency: '',
        recommendation: '',
        comparison: ''
    };

    var installmentRatio = (installmentAmount / loanAmount) * 100;
    var interestRatio = (totalInterest / loanAmount) * 100;

    // Analiza dostępności
    if (installmentRatio < 2) {
        analysis.affordability = 'Bardzo niska rata - łatwa do udźwignięcia';
    } else if (installmentRatio < 4) {
        analysis.affordability = 'Umiarkowana rata - dostępna dla większości';
    } else if (installmentRatio < 6) {
        analysis.affordability = 'Wysoka rata - wymaga stabilnych dochodów';
    } else {
        analysis.affordability = 'Bardzo wysoka rata - ryzykowna';
    }

    // Analiza efektywności
    if (interestRatio < 80) {
        analysis.efficiency = 'Niski koszt odsetek - efektywne finansowanie';
    } else if (interestRatio < 120) {
        analysis.efficiency = 'Umiarkowany koszt odsetek - standardowe warunki';
    } else if (interestRatio < 160) {
        analysis.efficiency = 'Wysoki koszt odsetek - rozważ alternatywy';
    } else {
        analysis.efficiency = 'Bardzo wysoki koszt odsetek - nieefektywne';
    }

    // Rekomendacje
    switch(type) {
        case 'equal':
            analysis.recommendation = 'Równe raty - przewidywalne obciążenie budżetu';
            break;
        case 'decreasing':
            analysis.recommendation = 'Malejące raty - niższy całkowity koszt odsetek';
            break;
        case 'interest_only':
            analysis.recommendation = 'Tylko odsetki - najniższa rata, ale wyższy całkowity koszt';
            break;
    }

    // Porównanie częstotliwości
    switch(frequency) {
        case 'monthly':
            analysis.comparison = 'Miesięczne raty - standard rynkowy';
            break;
        case 'quarterly':
            analysis.comparison = 'Kwartalne raty - wyższe pojedyncze płatności';
            break;
        case 'weekly':
            analysis.comparison = 'Tygodniowe raty - szybsza spłata kredytu';
            break;
        case 'bi_weekly':
            analysis.comparison = 'Dwutygodniowe raty - oszczędność na odsetkach';
            break;
    }

    return analysis;
}

function updateInstallmentResults(loanAmount, installmentAmount, totalInterest, totalCost,
                                annualInstallment, installmentRatio, loanTerm, installmentType,
                                paymentFrequency, installmentAnalysis) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Rata: &nbsp; ' + formatNumber(installmentAmount) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły rat
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        detailsSection.innerHTML =
            '<h3>Szczegółowa analiza rat:</h3>' +
            '<p><strong>Typ spłaty:</strong> ' + getInstallmentTypeName(installmentType) + '</p>' +
            '<p><strong>Częstotliwość płatności:</strong> ' + getPaymentFrequencyName(paymentFrequency) + '</p>' +
            '<p><strong>Roczne obciążenie:</strong> ' + formatNumber(annualInstallment) + ' zł (' + installmentRatio.toFixed(1) + '% kwoty kredytu)</p>' +
            '<p><strong>Łączne odsetki:</strong> ' + formatNumber(totalInterest) + ' zł</p>' +
            '<p><strong>Całkowity koszt:</strong> ' + formatNumber(totalCost) + ' zł</p>' +
            '<p><strong>Dostępność:</strong> ' + installmentAnalysis.affordability + '</p>' +
            '<p><strong>Efektywność:</strong> ' + installmentAnalysis.efficiency + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + installmentAnalysis.recommendation + '</p>' +
            '<p><strong>Częstotliwość:</strong> ' + installmentAnalysis.comparison + '</p>';
    }
}

function getInstallmentTypeName(type) {
    switch(type) {
        case 'equal': return 'Równe raty (annuitetowe)';
        case 'decreasing': return 'Malejące raty (równy kapitał)';
        case 'interest_only': return 'Tylko odsetki';
        default: return 'Standardowe raty';
    }
}

function getPaymentFrequencyName(frequency) {
    switch(frequency) {
        case 'monthly': return 'Miesięczne';
        case 'quarterly': return 'Kwartalne';
        case 'semi_annual': return 'Półroczne';
        case 'annual': return 'Roczne';
        case 'weekly': return 'Tygodniowe';
        case 'bi_weekly': return 'Dwutygodniowe';
        default: return 'Miesięczne';
    }
}

function clearForm(form) {
    if (document.getElementById('cloanamount')) document.getElementById('cloanamount').value = '300000';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.5';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '20';
    if (document.getElementById('cinstallmenttype')) document.getElementById('cinstallmenttype').value = 'equal';
    if (document.getElementById('cpaymentfreq')) document.getElementById('cpaymentfreq').value = 'monthly';

    calculateInstallment();
}

function saveCalResult() {
    alert('Analiza rat została zapisana!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateInstallment();
};
</script>

</body>
</html>
