<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Kalkulator kosztów kredytu hipotecznego</title>
	<meta name="description" content="Kalkulator kosztów kredytu hipotecznego - oblicz wszystkie koszty kredytu mieszkaniowego, sprawdź opłaty, prowizje i całkowity koszt kredytu hipotecznego 2025.">
	<meta name="keywords" content="kalkulator kosztów kredytu hipotecznego, koszty kredytu hipotecznego, opłaty kredytu mieszkaniowego, prowizje kredytowe">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulator-kosztow-kredytu-hipotecznego.html" itemprop="item"><span itemprop="name">kalkulator kosztów kredytu hipotecznego</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Kalkulator kosztów kredytu hipotecznego</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz wszystkie koszty kredytu hipotecznego"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/kalkulator-kosztow-kredytu-hipotecznego.html">
<table align="center">
<tbody><tr><td align="right">Wartość nieruchomości</td><td align="right"><input type="text" name="chouseprice" id="chouseprice" value="500000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Wkład własny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny to część ceny nieruchomości pokrywana przez kredytobiorcę. Zazwyczaj banki wymagają minimum 10-20% wkładu własnego. Wyższy wkład własny = niższa marża i lepsze warunki kredytu.', '');" onmouseout="tooltip.hide();"></td><td align="right" valign="bottom"><input type="text" name="cdownpayment" id="cdownpayment" value="20" class="inhalf inpct"></td><td><select name="cdownpaymentunit" onchange="cunitchange('cdownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w latach. Większość kredytów hipotecznych to 15, 20, 25 lub 30 lat. Krótszy okres = wyższa rata, ale niższe odsetki.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczne oprocentowanie kredytu hipotecznego. WIBOR 3M + marża banku.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Data rozpoczęcia</td><td align="left" colspan="2"><select name="cstartmonth" id="cstartmonth"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char"></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Uwzględnij opłaty i koszty poniżej</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Roczne opłaty i koszty</td></tr>
	<tr><td align="right">Prowizja przygotowawcza <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja banku za przygotowanie kredytu. Zazwyczaj 0-3% kwoty kredytu. Płatna jednorazowo przy uruchomieniu kredytu.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cprovision" id="cprovision" value="2.0" class="innormal inpct"></td><td><select name="cprovisionunit" onchange="cunitchange('cprovision', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie nieruchomości <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Obowiązkowe ubezpieczenie nieruchomości od ognia i innych zdarzeń losowych. Koszt zależy od wartości nieruchomości i zakresu ochrony.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="chomeins" id="chomeins" value="1200" class="innormal  indollar"></td><td><select name="chomeinsunit" onchange="cunitchange('chomeins', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Ubezpieczenie spłaty kredytu <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie na wypadek śmierci, trwałego kalectwa lub utraty pracy. Zazwyczaj 0.2-0.5% kwoty kredytu rocznie. Opcjonalne, ale często wymagane przez bank.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cpmi" id="cpmi" value="0.35" class="innormal inpct"></td><td><select name="cpmiunit" onchange="cunitchange('cpmi', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td></tr>
	<tr><td align="right">Rachunek kredytowy <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego. Zazwyczaj 0-30 zł miesięcznie, zależy od banku i pakietu.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="choa" id="choa" value="20" class="innormal  indollar"></td><td><select name="choaunit" onchange="cunitchange('choa', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
	<tr><td align="right">Inne koszty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe koszty: wycena nieruchomości (1000-2000 zł), koszty notarialne (2000-5000 zł), wpis hipoteki (200-500 zł), opłaty administracyjne.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cothercost" id="cothercost" value="5000" class="innormal  indollar"></td><td><select name="cothercostunit" onchange="cunitchange('cothercost', this.value);"><option value="p">%</option><option value="d" selected="">zł</option></select></td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Więcej opcji</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Roczny wzrost kosztów</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wzrost ubezpieczenia nieruchomości</td><td><input type="text" name="cptinc" value="3" class="in4char inpct"></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wzrost ubezpieczenia spłaty</td><td><input type="text" name="chiinc" value="0" class="in4char inpct"></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wzrost opłaty za rachunek</td><td><input type="text" name="choainc" value="2" class="in4char inpct"></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Wzrost innych kosztów</td><td><input type="text" name="cocinc" value="3" class="in4char inpct"></td></tr>
	<tr><td colspan="3" style="padding:10px 0px 3px 0px;font-weight:bold;text-align:left;">Dodatkowe płatności</td></tr>
	<tr><td colspan="3" style="padding-left:10px;text-align:left;">Dodatkowa płatność miesięczna
		<div style="padding-bottom:5px;"><input type="text" name="cexma" id="cexma" value="0" class="innormal indollar">
		od <select name="cexmsm"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cexmsy" value="2025" class="in4char"></div>
	</td></tr>
	<tr><td colspan="2" style="padding-left:10px;text-align:left;">Dodatkowa płatność roczna
		<div style="padding-bottom:5px;"><input type="text" name="cexya" id="cexya" value="0" class="innormal indollar">
		od <select name="cexysm"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cexysy" value="2025" class="in4char"></div>
	</td></tr>
	<tr><td colspan="2" style="padding-left:10px;text-align:left;">Jednorazowa płatność
		<div style="padding-bottom:5px;"><input type="text" name="cexoa" id="cexoa" value="0" class="innormal indollar">
		w <select name="cexosm"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cexosy" value="2025" class="in4char"></div>
	</td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz koszty kredytu" onclick="calculateMortgageCosts();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Całkowity koszt: &nbsp; 1 285 600 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrb3N6dMOzdyBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 0, 'S2Fsa3VsYXRvciBrb3N6dMOzdyBrcmVkeXR1IGhpcG90ZWN6bmVnbw==', 'Q2HDgmtvd2l0eSBrb3N6dA==', 'MSAyODUgNjAwIHrFgg==');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Miesięcznie</b></td><td align="right"><b>Rocznie</b></td><td align="right"><b>Łącznie</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Rata kredytu</b></td><td align="right"><b>3 485 zł</b></td><td align="right"><b>41 820 zł</b></td><td align="right"><b>1 045 500 zł</b></td></tr>
<tr><td>Kwota kredytu</td><td align="right">-</td><td align="right">-</td><td align="right">400 000 zł</td></tr>
<tr><td>Odsetki kredytu</td><td align="right">-</td><td align="right">-</td><td align="right">645 500 zł</td></tr>
<tr><td>Prowizja przygotowawcza</td><td align="right">-</td><td align="right">-</td><td align="right">8 000 zł</td></tr>
<tr><td>Ubezpieczenie nieruchomości</td><td align="right">100 zł</td><td align="right">1 200 zł</td><td align="right">30 000 zł</td></tr>
<tr><td>Ubezpieczenie spłaty kredytu</td><td align="right">117 zł</td><td align="right">1 400 zł</td><td align="right">35 000 zł</td></tr>
<tr><td>Rachunek kredytowy</td><td align="right">20 zł</td><td align="right">240 zł</td><td align="right">6 000 zł</td></tr>
<tr><td>Inne koszty</td><td align="right">-</td><td align="right">-</td><td align="right">5 000 zł</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Podsumowanie kosztów kredytu:</h3>
<p><strong>RRSO:</strong> 8.85% (z wszystkimi kosztami)</p>
<p><strong>Całkowita rata miesięczna:</strong> 3 722 zł (kredyt + ubezpieczenia + opłaty)</p>
<p><strong>Koszty dodatkowe:</strong> 84 000 zł (21% kwoty kredytu)</p>
<p><strong>Udział odsetek:</strong> 161% kwoty kredytu</p>
<p><strong>Najdroższy element:</strong> Odsetki kredytu (645 500 zł)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Szczegółowa analiza kosztów</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Struktura kosztów</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">400K zł</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">600K zł</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">800K zł</text>

<!-- Kwota kredytu -->
<rect x="50" y="105" width="35" height="40" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Kredyt</text>
<text x="67" y="100" class="mcllabelx" style="fill:#000;">400K zł</text>

<!-- Odsetki -->
<rect x="95" y="65" width="35" height="80" fill="#e74c3c" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Odsetki</text>
<text x="112" y="60" class="mcllabelx" style="fill:#000;">646K zł</text>

<!-- Ubezpieczenia -->
<rect x="140" y="125" width="35" height="20" fill="#f39c12" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Ubezp.</text>
<text x="157" y="120" class="mcllabelx" style="fill:#000;">65K zł</text>

<!-- Prowizje -->
<rect x="185" y="135" width="35" height="10" fill="#9b59b6" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Prowizje</text>
<text x="202" y="130" class="mcllabelx" style="fill:#000;">8K zł</text>

<!-- Inne -->
<rect x="230" y="140" width="35" height="5" fill="#2ecc71" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Inne</text>
<text x="247" y="135" class="mcllabelx" style="fill:#000;">11K zł</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Analiza kosztów &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Rok</th>
<th>Rata kredytu</th>
<th>Ubezpieczenia</th>
<th>Opłaty</th>
<th>Łącznie</th>
</tr>
<tr>
<td>1</td>
<td>41 820 zł</td>
<td>2 600 zł</td>
<td>240 zł</td>
<td>44 660 zł</td>
</tr>
<tr>
<td>5</td>
<td>41 820 zł</td>
<td>2 756 zł</td>
<td>250 zł</td>
<td>44 826 zł</td>
</tr>
<tr>
<td>10</td>
<td>41 820 zł</td>
<td>3 048 zł</td>
<td>268 zł</td>
<td>45 136 zł</td>
</tr>
<tr>
<td>15</td>
<td>41 820 zł</td>
<td>3 372 zł</td>
<td>287 zł</td>
<td>45 479 zł</td>
</tr>
<tr>
<td>20</td>
<td>41 820 zł</td>
<td>3 731 zł</td>
<td>307 zł</td>
<td>45 858 zł</td>
</tr>
<tr>
<td>25</td>
<td>41 820 zł</td>
<td>4 128 zł</td>
<td>329 zł</td>
<td>46 277 zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kosztów kredytu hipotecznego - kompleksowa analiza wydatków 2025</h2>
            <p>Kredyt hipoteczny to nie tylko rata miesięczna - to szereg dodatkowych kosztów, które mogą znacząco wpłynąć na Twój budżet. Nasz kalkulator kosztów kredytu hipotecznego pomoże Ci precyzyjnie obliczyć wszystkie koszty kredytu mieszkaniowego, sprawdzić opłaty, prowizje i całkowity koszt kredytu hipotecznego, aby uniknąć nieprzyjemnych niespodzianek finansowych.</p>

            <h3>Główne kategorie kosztów kredytu hipotecznego:</h3>
            <div style="margin: 15px 0;">
                <h4>1. Koszty jednorazowe (przy uruchomieniu kredytu):</h4>
                <ul>
                    <li><strong>Prowizja przygotowawcza:</strong> 0-3% kwoty kredytu (8 000 - 12 000 zł)</li>
                    <li><strong>Wycena nieruchomości:</strong> 1 000 - 2 500 zł</li>
                    <li><strong>Koszty notarialne:</strong> 2 000 - 5 000 zł</li>
                    <li><strong>Wpis hipoteki:</strong> 200 - 500 zł</li>
                    <li><strong>Opłaty administracyjne:</strong> 200 - 800 zł</li>
                    <li><strong>Łącznie jednorazowo:</strong> 11 400 - 20 800 zł</li>
                </ul>

                <h4>2. Koszty miesięczne (przez cały okres kredytu):</h4>
                <ul>
                    <li><strong>Rata kredytu:</strong> Kapitał + odsetki (główny koszt)</li>
                    <li><strong>Ubezpieczenie nieruchomości:</strong> 80 - 150 zł/miesiąc</li>
                    <li><strong>Ubezpieczenie spłaty kredytu:</strong> 100 - 200 zł/miesiąc</li>
                    <li><strong>Rachunek kredytowy:</strong> 0 - 30 zł/miesiąc</li>
                    <li><strong>Łącznie miesięcznie:</strong> 180 - 380 zł dodatkowo</li>
                </ul>

                <h4>3. Koszty roczne (zmienne w czasie):</h4>
                <ul>
                    <li><strong>Wzrost ubezpieczeń:</strong> 2-5% rocznie</li>
                    <li><strong>Wzrost opłat bankowych:</strong> 1-3% rocznie</li>
                    <li><strong>Dodatkowe usługi:</strong> Według potrzeb</li>
                    <li><strong>Wpływ inflacji:</strong> Realny wzrost kosztów</li>
                </ul>
            </div>

            <h3>Szczegółowa analiza kosztów:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Rodzaj kosztu</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Kwota/Stawka</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Częstotliwość</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Koszt 25 lat</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Rata kredytu (odsetki)</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>645 500 zł</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Przez cały okres</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>645 500 zł</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja przygotowawcza</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2% kwoty kredytu</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Jednorazowo</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">8 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 200 zł/rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rocznie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">30 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie spłaty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.35% kwoty kredytu</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rocznie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">35 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rachunek kredytowy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">20 zł/miesiąc</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Miesięcznie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">6 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Inne koszty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Jednorazowo</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5 000 zł</td>
                    </tr>
                    <tr style="background: #f0f0f0;">
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>ŁĄCZNIE</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>-</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>-</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>729 500 zł</strong></td>
                    </tr>
                </table>
            </div>

            <h3>Strategie minimalizacji kosztów:</h3>
            <ul>
                <li><strong>Negocjacja prowizji:</strong> Możliwość obniżenia lub całkowitego zwolnienia</li>
                <li><strong>Porównanie ubezpieczeń:</strong> Wybór najtańszych opcji spoza banku</li>
                <li><strong>Wyższy wkład własny:</strong> Mniejsza kwota kredytu = niższe koszty</li>
                <li><strong>Krótszy okres:</strong> Wyższe raty, ale znacznie niższe koszty całkowite</li>
                <li><strong>Pakiety bankowe:</strong> Darmowe prowadzenie rachunku kredytowego</li>
                <li><strong>Nadpłaty:</strong> Skrócenie okresu i redukcja odsetek</li>
                <li><strong>Refinansowanie:</strong> Zmiana na lepsze warunki po kilku latach</li>
            </ul>

            <h3>Porównanie scenariuszy kosztów:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Scenariusz</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Miesięczne koszty</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Koszty 25 lat</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oszczędność</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Minimalny</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 485 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 045 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">240 100 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Konserwatywny</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 652 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 095 600 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">190 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Standardowy</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 722 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 116 600 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">169 000 zł</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Premium</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3 825 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1 147 500 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">138 100 zł</td>
                    </tr>
                </table>
            </div>

            <h3>Wskazówki ekspertów:</h3>
            <ul>
                <li>Zawsze uwzględniaj wszystkie koszty przy porównywaniu ofert kredytowych</li>
                <li>RRSO (Rzeczywista Roczna Stopa Oprocentowania) to najlepszy wskaźnik porównawczy</li>
                <li>Koszty dodatkowe mogą stanowić 15-25% całkowitego kosztu kredytu</li>
                <li>Ubezpieczenia można często wykupić taniej poza bankiem</li>
                <li>Negocjuj wszystkie opłaty, szczególnie prowizję przygotowawczą</li>
                <li>Rozważ krótszy okres kredytowania dla znacznych oszczędności</li>
                <li>Planuj nadpłaty już na etapie wyboru kredytu</li>
                <li>Regularnie przeglądaj koszty i rozważaj refinansowanie</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Szybkie ustawienia</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Ustaw scenariusz:</strong><br>
<a href="#" onclick="return setScenario('conservative');">Konserwatywny (niskie koszty)</a><br>
<a href="#" onclick="return setScenario('standard');">Standardowy (średnie koszty)</a><br>
<a href="#" onclick="return setScenario('premium');">Premium (wysokie koszty)</a><br>
<a href="#" onclick="return setScenario('minimal');">Minimalny (tylko kredyt)</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Kalkulator kosztów kredytu hipotecznego - oblicz wszystkie koszty kredytu mieszkaniowego, sprawdź opłaty i prowizje.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateMortgageCosts() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    
    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;
    
    // 计算月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 获取费用
    var costs = {};
    if (document.getElementById('caddoptional').checked) {
        // 计算各项费用
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.otherCosts = calculateCost('cothercost', 'cothercostunit', loanAmount);
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, otherCosts: 0};
    }
    
    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;
    
    var totalCost = loanAmount + totalInterest + costs.provision + 
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees + costs.otherCosts;
    
    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;
    
    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;
    
    // 更新显示
    updateCostResults(loanAmount, monthlyPayment, totalInterest, costs, 
                     totalCost, monthlyTotal, rrso, loanTerm);
}

function calculateCost(valueId, unitId, loanAmount) {
    var value = parseFloat(document.getElementById(valueId).value) || 0;
    var unit = document.getElementById(unitId).value;
    
    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateCostResults(loanAmount, monthlyPayment, totalInterest, costs, 
                          totalCost, monthlyTotal, rrso, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    // 更新主要结果
    document.querySelector('.h2result').innerHTML = 
        'Całkowity koszt: &nbsp; ' + formatNumber(totalCost) + ' zł' +
        '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult(\'S2Fsa3VsYXRvciBrb3N6dMOzdyBrcmVkeXR1IGhpcG90ZWN6bmVnbw==\', 0, \'S2Fsa3VsYXRvciBrb3N6dMOzdyBrcmVkeXR1IGhpcG90ZWN6bmVnbw==\', \'Q2HDgmtvd2l0eSBrb3N6dA==\', \'' + btoa(formatNumber(totalCost) + ' zł') + '\');">';
    
    // 更新详细结果表格
    var resultTable = document.querySelector('.crighthalf table tbody');
    if (resultTable) {
        var rows = resultTable.querySelectorAll('tr');
        if (rows.length >= 9) {
            // 更新各行数据
            if (rows[2]) {
                rows[2].cells[1].innerHTML = '<b>' + formatNumber(monthlyPayment) + ' zł</b>';
                rows[2].cells[2].innerHTML = '<b>' + formatNumber(monthlyPayment * 12) + ' zł</b>';
                rows[2].cells[3].innerHTML = '<b>' + formatNumber(monthlyPayment * loanTerm * 12) + ' zł</b>';
            }
            
            if (rows[3]) {
                rows[3].cells[3].innerHTML = formatNumber(loanAmount) + ' zł';
            }
            
            if (rows[4]) {
                rows[4].cells[3].innerHTML = formatNumber(totalInterest) + ' zł';
            }
            
            if (rows[5]) {
                rows[5].cells[3].innerHTML = formatNumber(costs.provision) + ' zł';
            }
            
            if (rows[6]) {
                rows[6].cells[1].innerHTML = formatNumber(costs.homeInsurance/12) + ' zł';
                rows[6].cells[2].innerHTML = formatNumber(costs.homeInsurance) + ' zł';
                rows[6].cells[3].innerHTML = formatNumber(costs.homeInsurance * loanTerm) + ' zł';
            }
            
            if (rows[7]) {
                rows[7].cells[1].innerHTML = formatNumber(costs.lifeInsurance/12) + ' zł';
                rows[7].cells[2].innerHTML = formatNumber(costs.lifeInsurance) + ' zł';
                rows[7].cells[3].innerHTML = formatNumber(costs.lifeInsurance * loanTerm) + ' zł';
            }
            
            if (rows[8]) {
                rows[8].cells[1].innerHTML = formatNumber(costs.accountFee) + ' zł';
                rows[8].cells[2].innerHTML = formatNumber(costs.accountFee * 12) + ' zł';
                rows[8].cells[3].innerHTML = formatNumber(costs.accountFee * 12 * loanTerm) + ' zł';
            }
            
            if (rows[9]) {
                rows[9].cells[3].innerHTML = formatNumber(costs.otherCosts) + ' zł';
            }
        }
    }
    
    // 更新podsumowanie
    var summarySection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (summarySection) {
        var additionalCosts = totalCost - loanAmount - totalInterest;
        summarySection.innerHTML = 
            '<h3>Podsumowanie kosztów kredytu:</h3>' +
            '<p><strong>RRSO:</strong> ' + rrso.toFixed(2) + '% (z wszystkimi kosztami)</p>' +
            '<p><strong>Całkowita rata miesięczna:</strong> ' + formatNumber(monthlyTotal) + ' zł (kredyt + ubezpieczenia + opłaty)</p>' +
            '<p><strong>Koszty dodatkowe:</strong> ' + formatNumber(additionalCosts) + ' zł (' + Math.round((additionalCosts/loanAmount)*100) + '% kwoty kredytu)</p>' +
            '<p><strong>Udział odsetek:</strong> ' + Math.round((totalInterest/loanAmount)*100) + '% kwoty kredytu</p>' +
            '<p><strong>Najdroższy element:</strong> Odsetki kredytu (' + formatNumber(totalInterest) + ' zł)</p>';
    }
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij opłaty i koszty poniżej';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Uwzględnij opłaty i koszty poniżej';
    }
    calculateMortgageCosts();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Mniej opcji</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Więcej opcji</a>';
        hiddenField.value = '0';
    }
}

function cunitchange(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
    calculateMortgageCosts();
}

function clearForm(form) {
    document.getElementById('chouseprice').value = '500000';
    document.getElementById('cdownpayment').value = '20';
    document.getElementById('cdownpaymentunit').value = 'p';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('cinterestrate').value = '7.25';
    document.getElementById('cstartmonth').value = '8';
    document.getElementById('cstartyear').value = '2025';
    
    document.getElementById('cprovision').value = '2.0';
    document.getElementById('chomeins').value = '1200';
    document.getElementById('cpmi').value = '0.35';
    document.getElementById('choa').value = '20';
    document.getElementById('cothercost').value = '5000';
    
    document.getElementById('caddoptional').checked = true;
    cshtaxcost();
    calculateMortgageCosts();
}

function setScenario(type) {
    switch(type) {
        case 'conservative':
            document.getElementById('cprovision').value = '1.0';
            document.getElementById('chomeins').value = '800';
            document.getElementById('cpmi').value = '0.25';
            document.getElementById('choa').value = '0';
            document.getElementById('cothercost').value = '3000';
            break;
        case 'standard':
            document.getElementById('cprovision').value = '2.0';
            document.getElementById('chomeins').value = '1200';
            document.getElementById('cpmi').value = '0.35';
            document.getElementById('choa').value = '20';
            document.getElementById('cothercost').value = '5000';
            break;
        case 'premium':
            document.getElementById('cprovision').value = '3.0';
            document.getElementById('chomeins').value = '1800';
            document.getElementById('cpmi').value = '0.45';
            document.getElementById('choa').value = '30';
            document.getElementById('cothercost').value = '8000';
            break;
        case 'minimal':
            document.getElementById('cprovision').value = '0';
            document.getElementById('chomeins').value = '0';
            document.getElementById('cpmi').value = '0';
            document.getElementById('choa').value = '0';
            document.getElementById('cothercost').value = '0';
            break;
    }
    calculateMortgageCosts();
    return false;
}

function saveCalResult(param1, param2, param3, param4, param5) {
    alert('Wyniki kalkulatora kosztów zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Harmonogram spłat &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Analiza kosztów</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Analiza kosztów &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Harmonogram spłat</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateMortgageCosts();
};
</script>

</body></html>
