# PowerShell脚本检查404错误
Write-Host "=== 检查HTML文件中的404错误 ===" -ForegroundColor Green

# 获取所有HTML文件
$htmlFiles = Get-ChildItem -Name "*.html" | Where-Object { 
    $_ -notmatch "test|TEST|debug|WORKING|FINAL|check-|fix-" 
}

Write-Host "找到 $($htmlFiles.Count) 个HTML文件进行检查" -ForegroundColor Yellow

$allLinks = @{}
$missingLinks = @()
$existingLinks = @()

# 检查每个HTML文件
foreach ($file in $htmlFiles) {
    Write-Host "检查文件: $file" -ForegroundColor Cyan
    
    try {
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # 提取href链接
        $matches = [regex]::Matches($content, 'href="([^"]+\.html)"')
        
        $fileLinks = @()
        foreach ($match in $matches) {
            $link = $match.Groups[1].Value
            
            # 过滤掉外部链接和特殊路径
            if ($link -notmatch "^http" -and 
                $link -notmatch "^//" -and 
                $link -notmatch "^/konto/" -and 
                $link -notmatch "^/kalkulatory-" -and 
                $link -match "\.html$") {
                
                # 移除开头的 / 如果存在
                $cleanLink = $link -replace "^/", ""
                
                if (-not $allLinks.ContainsKey($cleanLink)) {
                    $allLinks[$cleanLink] = @()
                }
                $allLinks[$cleanLink] += $file
                $fileLinks += $cleanLink
            }
        }
        
        Write-Host "  找到 $($fileLinks.Count) 个内部链接" -ForegroundColor Gray

    } catch {
        Write-Host "  错误: $_" -ForegroundColor Red
    }
}
}

Write-Host "`n总共找到 $($allLinks.Count) 个唯一的内部链接" -ForegroundColor Yellow

# 检查链接是否存在
Write-Host "`n=== 检查链接是否存在 ===" -ForegroundColor Green

foreach ($link in $allLinks.Keys) {
    if (Test-Path $link) {
        $existingLinks += $link
        Write-Host "✅ $link - 存在" -ForegroundColor Green
    } else {
        $missingLinks += $link
        Write-Host "❌ $link - 缺失" -ForegroundColor Red
        
        # 显示哪些文件引用了这个链接
        $sources = $allLinks[$link] -join ", "
        Write-Host "   被以下文件引用: $sources" -ForegroundColor Gray
    }
}

# 显示总结
Write-Host "`n=== 总结 ===" -ForegroundColor Green
Write-Host "✅ 存在的链接: $($existingLinks.Count)" -ForegroundColor Green
Write-Host "❌ 缺失的链接: $($missingLinks.Count)" -ForegroundColor Red

if ($missingLinks.Count -gt 0) {
    Write-Host "`n=== 缺失的链接列表 ===" -ForegroundColor Red
    foreach ($link in $missingLinks) {
        Write-Host "- $link" -ForegroundColor Red
    }
    
    Write-Host "`n=== 修复建议 ===" -ForegroundColor Yellow
    foreach ($link in $missingLinks) {
        $template = "o-nas.html"  # 默认模板
        
        # 智能选择模板
        if ($link -match "kalkulator") {
            if ($existingLinks -contains "kalkulator-raty-kredytu-hipotecznego.html") {
                $template = "kalkulator-raty-kredytu-hipotecznego.html"
            }
        }
        
        if ($link -match "pko") { $template = "pko-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "ing") { $template = "ing-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "mbank") { $template = "mbank-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "santander") { $template = "santander-kalkulator-kredytu-hipotecznego.html" }
        
        Write-Host "Copy-Item `"$template`" `"$link`"" -ForegroundColor Yellow
    }
    
    # 生成修复脚本
    $fixScript = @"
# 自动修复404错误的PowerShell脚本
Write-Host "开始修复404错误..." -ForegroundColor Green

"@
    
    foreach ($link in $missingLinks) {
        $template = "o-nas.html"
        
        if ($link -match "kalkulator") {
            if ($existingLinks -contains "kalkulator-raty-kredytu-hipotecznego.html") {
                $template = "kalkulator-raty-kredytu-hipotecznego.html"
            }
        }
        
        if ($link -match "pko") { $template = "pko-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "ing") { $template = "ing-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "mbank") { $template = "mbank-kalkulator-kredytu-hipotecznego.html" }
        if ($link -match "santander") { $template = "santander-kalkulator-kredytu-hipotecznego.html" }
        
        $fixScript += @"

Write-Host "创建 $link..." -ForegroundColor Cyan
Copy-Item "$template" "$link"
"@
    }
    
    $fixScript += @"

Write-Host "修复完成！" -ForegroundColor Green
"@
    
    $fixScript | Out-File -FilePath "fix-404-errors-auto.ps1" -Encoding UTF8
    Write-Host "`n🔧 自动修复脚本已保存到: fix-404-errors-auto.ps1" -ForegroundColor Green
    Write-Host "运行命令: .\fix-404-errors-auto.ps1" -ForegroundColor Yellow
    
} else {
    Write-Host "`n🎉 恭喜！没有发现404错误！" -ForegroundColor Green
}

# 生成JSON报告
$report = @{
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
    summary = @{
        total_html_files = $htmlFiles.Count
        total_unique_links = $allLinks.Count
        existing_links = $existingLinks.Count
        missing_links = $missingLinks.Count
        success_rate = [math]::Round(($existingLinks.Count / $allLinks.Count) * 100, 2)
    }
    existing_links = $existingLinks | Sort-Object
    missing_links = $missingLinks | Sort-Object
    html_files_checked = $htmlFiles | Sort-Object
}

$report | ConvertTo-Json -Depth 3 | Out-File -FilePath "link-check-report.json" -Encoding UTF8
Write-Host "`n报告已保存到: link-check-report.json" -ForegroundColor Green
