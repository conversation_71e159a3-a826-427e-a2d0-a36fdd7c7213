<!DOCTYPE html>
<html>
<head><title>Test 3</title></head>
<body>
<h1>Test 3: Calculator</h1>
<input type="text" id="num1" value="500000">
<input type="text" id="num2" value="20">
<button onclick="calc()">Calculate</button>
<h2 id="result">Result: 0</h2>
<script>
function calc() {
    var a = parseFloat(document.getElementById('num1').value);
    var b = parseFloat(document.getElementById('num2').value);
    var result = a * (b / 100);
    document.getElementById('result').innerHTML = 'Result: ' + result;
}
</script>
</body>
</html>
