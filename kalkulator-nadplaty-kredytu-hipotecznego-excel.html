<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Kalkulator nadpłaty kredytu hipotecznego Excel</title>
	<meta name="description" content="Kalkulator nadpłaty kredytu hipotecznego Excel - pobierz darmowy szablon Excel do obliczania oszczędności z nadpłat kredytu mieszkaniowego, arkusz kalkulacyjny 2025.">
	<meta name="keywords" content="kalkulator nadpłaty kredytu hipotecznego excel, excel nadpłaty kredytu, ark<PERSON>z kalkulacyjny kredyt, szablon excel kredyt">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulator-nadplaty-kredytu-hipotecznego-excel.html" itemprop="item"><span itemprop="name">kalkulator nadpłaty kredytu hipotecznego excel</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Kalkulator nadpłaty kredytu hipotecznego Excel</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Pobierz szablon Excel nadpłaty kredytu"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/kalkulator-nadplaty-kredytu-hipotecznego-excel.html">
<table align="center">
<tbody><tr><td align="right">Kwota kredytu</td><td align="right"><input type="text" name="cloanamount" id="cloanamount" value="400000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczne oprocentowanie kredytu hipotecznego dla szablonu Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwotny okres spłaty kredytu w latach dla arkusza Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Kwota nadpłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota spłacana w arkuszu Excel. Może być miesięczna, roczna lub jednorazowa.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="coverpayment" id="coverpayment" value="500" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Częstotliwość nadpłaty</td><td align="left" colspan="2"><select name="coverpaymentfreq" id="coverpaymentfreq"><option value="monthly" selected="">Miesięcznie</option><option value="yearly">Rocznie</option><option value="once">Jednorazowo</option></select></td></tr>
<tr><td align="right">Szablon Excel</td><td align="left" colspan="2"><select name="cexceltemplate" id="cexceltemplate"><option value="basic" selected="">Podstawowy Excel</option><option value="advanced">Zaawansowany Excel</option><option value="professional">Profesjonalny Excel</option><option value="business">Biznesowy Excel</option></select></td></tr>
<tr><td align="right">Data rozpoczęcia</td><td align="left" colspan="2"><select name="cstartmonth" id="cstartmonth"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char"></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Opcje szablonu Excel</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Funkcje arkusza Excel</td></tr>
	<tr><td align="right">Wykresy w Excel <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Automatyczne wykresy pokazujące oszczędności z nadpłat w arkuszu Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="checkbox" name="cexcelcharts" id="cexcelcharts" checked=""></td><td>&nbsp;</td></tr>
	<tr><td align="right">Tabele przestawne <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Zaawansowane tabele przestawne do analizy nadpłat w Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="checkbox" name="cexcelpivot" id="cexcelpivot" checked=""></td><td>&nbsp;</td></tr>
	<tr><td align="right">Makra Excel <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Automatyczne makra do obliczania scenariuszy nadpłat w Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="checkbox" name="cexcelmacros" id="cexcelmacros"></td><td>&nbsp;</td></tr>
	<tr><td align="right">Walidacja danych <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Automatyczna walidacja wprowadzanych danych w arkuszu Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="checkbox" name="cexcelvalidation" id="cexcelvalidation" checked=""></td><td>&nbsp;</td></tr>
	<tr><td align="right">Formatowanie warunkowe <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Kolorowe formatowanie komórek w zależności od wartości w Excel.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="checkbox" name="cexcelformatting" id="cexcelformatting" checked=""></td><td>&nbsp;</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Szablony Excel</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Dostępne szablony Excel</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Podstawowy Excel</td><td><a href="#" onclick="setExcelTemplate('basic');return false;">Pobierz .xlsx</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Zaawansowany Excel</td><td><a href="#" onclick="setExcelTemplate('advanced');return false;">Pobierz .xlsx</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Profesjonalny Excel</td><td><a href="#" onclick="setExcelTemplate('professional');return false;">Pobierz .xlsx</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Biznesowy Excel</td><td><a href="#" onclick="setExcelTemplate('business');return false;">Pobierz .xlsx</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Excel z makrami</td><td><a href="#" onclick="setExcelTemplate('macros');return false;">Pobierz .xlsm</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Oblicz i generuj Excel" onclick="calculateExcelOverpayment();"><input type="button" value="Pobierz szablon" onclick="downloadExcelTemplate();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Excel oszczędności: &nbsp; 185 500 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBuYWRwxYJhdHkga3JlZHl0dSBoaXBvdGVjem5lZ28gRXhjZWw=', 0, 'S2Fsa3VsYXRvciBuYWRwxYJhdHkga3JlZHl0dSBoaXBvdGVjem5lZ28gRXhjZWw=', 'RXhjZWwgb3N6Y3rEmWRub8WbY2k=', 'MTg1IDUwMCB6xYI=');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Bez nadpłat</b></td><td align="right"><b>Z nadpłatami Excel</b></td><td align="right"><b>Oszczędność</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Czas spłaty</b></td><td align="right"><b>25 lat</b></td><td align="right"><b>18 lat 4 miesiące</b></td><td align="right"><b>6 lat 8 miesięcy</b></td></tr>
<tr><td>Miesięczna rata</td><td align="right">3 485 zł</td><td align="right">3 985 zł</td><td align="right">+500 zł</td></tr>
<tr><td>Łączne spłaty</td><td align="right">1 045 500 zł</td><td align="right">860 000 zł</td><td align="right">185 500 zł</td></tr>
<tr><td>Odsetki łącznie</td><td align="right">645 500 zł</td><td align="right">460 000 zł</td><td align="right">185 500 zł</td></tr>
<tr><td>Łączne nadpłaty</td><td align="right">0 zł</td><td align="right">110 000 zł</td><td align="right">-</td></tr>
<tr><td>Szablon Excel</td><td align="right">-</td><td align="right">Zaawansowany</td><td align="right">Darmowy</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Funkcje szablonu Excel:</h3>
<p><strong>Typ szablonu:</strong> Zaawansowany Excel z wykresami i tabelami</p>
<p><strong>Zawartość arkusza:</strong> Harmonogram spłat, analiza oszczędności, wykresy</p>
<p><strong>Funkcje Excel:</strong> Automatyczne obliczenia, formatowanie warunkowe, walidacja</p>
<p><strong>Kompatybilność:</strong> Excel 2016, 2019, 2021, Office 365</p>
<p><strong>Język:</strong> Polski interfejs, polskie formaty dat i liczb</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Szablony Excel nadpłaty kredytu</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Szablony Excel</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">Podstawowy</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">Zaawansowany</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">Profesjonalny</text>

<!-- Podstawowy -->
<rect x="50" y="125" width="35" height="20" fill="#3498db" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">Podstawowy</text>
<text x="67" y="120" class="mcllabelx" style="fill:#000;">Excel</text>

<!-- Zaawansowany -->
<rect x="95" y="95" width="35" height="50" fill="#2ecc71" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">Zaawansowany</text>
<text x="112" y="90" class="mcllabelx" style="fill:#000;">Excel</text>

<!-- Profesjonalny -->
<rect x="140" y="65" width="35" height="80" fill="#e74c3c" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">Profesjonalny</text>
<text x="157" y="60" class="mcllabelx" style="fill:#000;">Excel</text>

<!-- Biznesowy -->
<rect x="185" y="45" width="35" height="100" fill="#9b59b6" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">Biznesowy</text>
<text x="202" y="40" class="mcllabelx" style="fill:#000;">Excel</text>

<!-- Z makrami -->
<rect x="230" y="75" width="35" height="70" fill="#f39c12" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">Z makrami</text>
<text x="247" y="70" class="mcllabelx" style="fill:#000;">Excel</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Szablony Excel &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Funkcje Excel</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Szablon Excel</th>
<th>Funkcje</th>
<th>Wykresy</th>
<th>Makra</th>
<th>Pobierz</th>
</tr>
<tr>
<td>Podstawowy</td>
<td>Podstawowe obliczenia</td>
<td>Nie</td>
<td>Nie</td>
<td><a href="#" onclick="downloadExcelTemplate('basic');">xlsx</a></td>
</tr>
<tr>
<td>Zaawansowany</td>
<td>Pełne obliczenia + wykresy</td>
<td>Tak</td>
<td>Nie</td>
<td><a href="#" onclick="downloadExcelTemplate('advanced');">xlsx</a></td>
</tr>
<tr>
<td>Profesjonalny</td>
<td>Wszystkie funkcje + analiza</td>
<td>Tak</td>
<td>Nie</td>
<td><a href="#" onclick="downloadExcelTemplate('professional');">xlsx</a></td>
</tr>
<tr>
<td>Biznesowy</td>
<td>Funkcje biznesowe + raporty</td>
<td>Tak</td>
<td>Tak</td>
<td><a href="#" onclick="downloadExcelTemplate('business');">xlsm</a></td>
</tr>
<tr>
<td>Z makrami</td>
<td>Automatyczne scenariusze</td>
<td>Tak</td>
<td>Tak</td>
<td><a href="#" onclick="downloadExcelTemplate('macros');">xlsm</a></td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator nadpłaty kredytu hipotecznego Excel - profesjonalne arkusze kalkulacyjne 2025</h2>
            <p>Kalkulator nadpłaty kredytu hipotecznego Excel to zaawansowane narzędzie w formie arkusza kalkulacyjnego, które pozwala na szczegółową analizę oszczędności z nadpłat kredytu mieszkaniowego. Nasze szablony Excel oferują pełną funkcjonalność z automatycznymi obliczeniami, wykresami i tabelami przestawnymi do profesjonalnej analizy finansowej.</p>

            <h3>Dostępne szablony Excel:</h3>
            <div style="margin: 15px 0;">
                <h4>Podstawowy szablon Excel (.xlsx):</h4>
                <ul>
                    <li><strong>Zawartość:</strong> Podstawowe obliczenia nadpłat kredytu</li>
                    <li><strong>Funkcje:</strong> Automatyczne wzory Excel, formatowanie</li>
                    <li><strong>Wykresy:</strong> Podstawowy wykres oszczędności</li>
                    <li><strong>Kompatybilność:</strong> Excel 2010 i nowsze</li>
                    <li><strong>Rozmiar:</strong> ~50 KB</li>
                    <li><strong>Dla kogo:</strong> Użytkownicy domowi, podstawowa analiza</li>
                </ul>

                <h4>Zaawansowany szablon Excel (.xlsx):</h4>
                <ul>
                    <li><strong>Zawartość:</strong> Pełne obliczenia + harmonogram spłat</li>
                    <li><strong>Funkcje:</strong> Formatowanie warunkowe, walidacja danych</li>
                    <li><strong>Wykresy:</strong> Wielopoziomowe wykresy porównawcze</li>
                    <li><strong>Tabele:</strong> Automatyczne tabele z filtrami</li>
                    <li><strong>Rozmiar:</strong> ~150 KB</li>
                    <li><strong>Dla kogo:</strong> Zaawansowani użytkownicy, doradcy finansowi</li>
                </ul>

                <h4>Profesjonalny szablon Excel (.xlsx):</h4>
                <ul>
                    <li><strong>Zawartość:</strong> Kompleksowa analiza finansowa</li>
                    <li><strong>Funkcje:</strong> Tabele przestawne, zaawansowane wzory</li>
                    <li><strong>Wykresy:</strong> Interaktywne dashboardy</li>
                    <li><strong>Scenariusze:</strong> Analiza "co jeśli"</li>
                    <li><strong>Rozmiar:</strong> ~300 KB</li>
                    <li><strong>Dla kogo:</strong> Profesjonaliści, banki, biura maklerskie</li>
                </ul>

                <h4>Biznesowy szablon Excel (.xlsm):</h4>
                <ul>
                    <li><strong>Zawartość:</strong> Funkcje biznesowe + automatyzacja</li>
                    <li><strong>Makra:</strong> Automatyczne generowanie raportów</li>
                    <li><strong>Integracja:</strong> Połączenie z bazami danych</li>
                    <li><strong>Raporty:</strong> Automatyczne raporty PDF</li>
                    <li><strong>Rozmiar:</strong> ~500 KB</li>
                    <li><strong>Dla kogo:</strong> Firmy, instytucje finansowe</li>
                </ul>

                <h4>Szablon z makrami Excel (.xlsm):</h4>
                <ul>
                    <li><strong>Zawartość:</strong> Pełna automatyzacja obliczeń</li>
                    <li><strong>Makra VBA:</strong> Zaawansowane scenariusze</li>
                    <li><strong>Automatyzacja:</strong> Jednym kliknięciem</li>
                    <li><strong>Personalizacja:</strong> Dostosowanie do potrzeb</li>
                    <li><strong>Rozmiar:</strong> ~400 KB</li>
                    <li><strong>Dla kogo:</strong> Eksperci Excel, analitycy</li>
                </ul>
            </div>

            <h3>Funkcje arkuszy Excel:</h3>
            <div style="margin: 15px 0;">
                <h4>Automatyczne obliczenia:</h4>
                <ul>
                    <li><strong>Wzory Excel:</strong> PMT, IPMT, PPMT dla obliczeń kredytowych</li>
                    <li><strong>Harmonogram spłat:</strong> Miesięczny rozkład kapitału i odsetek</li>
                    <li><strong>Analiza nadpłat:</strong> Automatyczne obliczanie oszczędności</li>
                    <li><strong>Scenariusze:</strong> Porównanie różnych strategii nadpłat</li>
                    <li><strong>RRSO:</strong> Rzeczywista roczna stopa oprocentowania</li>
                </ul>

                <h4>Wykresy i wizualizacje:</h4>
                <ul>
                    <li><strong>Wykres oszczędności:</strong> Porównanie z/bez nadpłat</li>
                    <li><strong>Harmonogram spłat:</strong> Wizualizacja kapitału vs odsetki</li>
                    <li><strong>Analiza czasowa:</strong> Skrócenie okresu kredytowania</li>
                    <li><strong>Dashboard:</strong> Kluczowe wskaźniki na jednym ekranie</li>
                    <li><strong>Trendy:</strong> Analiza długoterminowych oszczędności</li>
                </ul>

                <h4>Zaawansowane funkcje Excel:</h4>
                <ul>
                    <li><strong>Formatowanie warunkowe:</strong> Kolorowe oznaczenia wartości</li>
                    <li><strong>Walidacja danych:</strong> Kontrola poprawności wprowadzanych danych</li>
                    <li><strong>Tabele przestawne:</strong> Dynamiczne analizy danych</li>
                    <li><strong>Filtry:</strong> Szybkie wyszukiwanie i sortowanie</li>
                    <li><strong>Ochrona arkusza:</strong> Zabezpieczenie wzorów przed zmianą</li>
                </ul>
            </div>

            <h3>Instrukcja użytkowania Excel:</h3>
            <div style="margin: 15px 0;">
                <h4>Krok 1: Pobranie i otwarcie</h4>
                <ul>
                    <li>Pobierz wybrany szablon Excel (.xlsx lub .xlsm)</li>
                    <li>Otwórz plik w Microsoft Excel 2016 lub nowszym</li>
                    <li>Włącz makra (jeśli dotyczy) dla pełnej funkcjonalności</li>
                    <li>Sprawdź kompatybilność z wersją Excel</li>
                </ul>

                <h4>Krok 2: Wprowadzenie danych</h4>
                <ul>
                    <li><strong>Kwota kredytu:</strong> Wprowadź w komórce B2</li>
                    <li><strong>Oprocentowanie:</strong> Roczna stopa w komórce B3</li>
                    <li><strong>Okres kredytowania:</strong> Liczba lat w komórce B4</li>
                    <li><strong>Nadpłaty:</strong> Kwoty i częstotliwość w sekcji C</li>
                </ul>

                <h4>Krok 3: Analiza wyników</h4>
                <ul>
                    <li><strong>Harmonogram spłat:</strong> Arkusz "Harmonogram"</li>
                    <li><strong>Porównanie scenariuszy:</strong> Arkusz "Analiza"</li>
                    <li><strong>Wykresy:</strong> Arkusz "Wykresy"</li>
                    <li><strong>Podsumowanie:</strong> Arkusz "Dashboard"</li>
                </ul>

                <h4>Krok 4: Personalizacja</h4>
                <ul>
                    <li>Dostosuj kolory i formatowanie do preferencji</li>
                    <li>Dodaj własne wzory i obliczenia</li>
                    <li>Zapisz jako szablon dla przyszłego użytku</li>
                    <li>Eksportuj wyniki do PDF lub innych formatów</li>
                </ul>
            </div>

            <h3>Zalety arkuszy Excel vs kalkulatory online:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Aspekt</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Arkusz Excel</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Kalkulator online</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Dostępność</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Offline, zawsze dostępny</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wymaga internetu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Personalizacja</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Pełna personalizacja</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ograniczona</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Zapisywanie danych</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Lokalne zapisywanie</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Czasowe</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Analiza zaawansowana</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Nieograniczona</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Podstawowa</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Wykresy</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zaawansowane, interaktywne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Podstawowe</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Eksport danych</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">PDF, CSV, inne formaty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ograniczony</td>
                    </tr>
                </table>
            </div>

            <h3>Wskazówki dla użytkowników Excel:</h3>
            <ul>
                <li><strong>Aktualizuj dane:</strong> Regularnie sprawdzaj aktualne stawki WIBOR</li>
                <li><strong>Twórz kopie:</strong> Zapisuj różne scenariusze w osobnych plikach</li>
                <li><strong>Używaj nazw:</strong> Nadawaj nazwy komórkom dla lepszej czytelności</li>
                <li><strong>Dokumentuj zmiany:</strong> Dodawaj komentarze do modyfikacji</li>
                <li><strong>Sprawdzaj wzory:</strong> Weryfikuj poprawność obliczeń</li>
                <li><strong>Zabezpieczaj dane:</strong> Chroń ważne arkusze hasłem</li>
                <li><strong>Aktualizuj Excel:</strong> Używaj najnowszej wersji dla lepszej funkcjonalności</li>
                <li><strong>Twórz raporty:</strong> Generuj regularne raporty z analizą</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Szablony Excel</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Pobierz szablon Excel:</strong><br>
<a href="#" onclick="return downloadExcelTemplate('basic');">Podstawowy Excel (.xlsx)</a><br>
<a href="#" onclick="return downloadExcelTemplate('advanced');">Zaawansowany Excel (.xlsx)</a><br>
<a href="#" onclick="return downloadExcelTemplate('professional');">Profesjonalny Excel (.xlsx)</a><br>
<a href="#" onclick="return downloadExcelTemplate('business');">Biznesowy Excel (.xlsm)</a><br>
<a href="#" onclick="return downloadExcelTemplate('macros');">Excel z makrami (.xlsm)</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Kalkulator nadpłaty kredytu hipotecznego Excel - pobierz darmowy szablon Excel do obliczania oszczędności.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateExcelOverpayment() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    
    // 获取nadpłata信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;
    var excelTemplate = document.getElementById('cexceltemplate').value;
    
    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 计算没有nadpłata的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;
    
    // 计算有nadpłata的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;
    
    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;
        
        // 添加nadpłata
        var currentOverpayment = 0;
        if (overpaymentFreq === 'monthly' || 
           (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
           (overpaymentFreq === 'once' && monthsPaid === 0)) {
            currentOverpayment = overpaymentAmount;
        }
        
        // 更新余额
        remainingBalance -= (monthlyPrincipal + currentOverpayment);
        totalPaid += monthlyPayment + currentOverpayment;
        totalOverpayments += currentOverpayment;
        monthsPaid++;
        
        if (remainingBalance <= 0) break;
    }
    
    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments);
    
    // 更新显示
    updateExcelOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid, 
                                timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments, 
                                excelTemplate);
}

function updateExcelOverpaymentResults(monthlyPayment, totalWithoutOverpayment, totalPaid, 
                                     timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                     excelTemplate) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Excel oszczędności: &nbsp; ' + formatNumber(moneySaved) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新funkcje szablonu Excel
    var featuresSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (featuresSection) {
        var templateName = getExcelTemplateName(excelTemplate);
        featuresSection.innerHTML = 
            '<h3>Funkcje szablonu Excel:</h3>' +
            '<p><strong>Typ szablonu:</strong> ' + templateName + '</p>' +
            '<p><strong>Zawartość arkusza:</strong> Harmonogram spłat, analiza oszczędności, wykresy</p>' +
            '<p><strong>Funkcje Excel:</strong> ' + getExcelFeatures(excelTemplate) + '</p>' +
            '<p><strong>Kompatybilność:</strong> Excel 2016, 2019, 2021, Office 365</p>' +
            '<p><strong>Język:</strong> Polski interfejs, polskie formaty dat i liczb</p>';
    }
}

function getExcelTemplateName(template) {
    switch(template) {
        case 'basic': return 'Podstawowy Excel';
        case 'advanced': return 'Zaawansowany Excel z wykresami i tabelami';
        case 'professional': return 'Profesjonalny Excel z pełną analizą';
        case 'business': return 'Biznesowy Excel z raportami i makrami';
        default: return 'Zaawansowany Excel z wykresami i tabelami';
    }
}

function getExcelFeatures(template) {
    switch(template) {
        case 'basic': return 'Podstawowe obliczenia, formatowanie';
        case 'advanced': return 'Automatyczne obliczenia, formatowanie warunkowe, walidacja';
        case 'professional': return 'Wszystkie funkcje + tabele przestawne + zaawansowane wykresy';
        case 'business': return 'Funkcje biznesowe + makra + automatyczne raporty';
        default: return 'Automatyczne obliczenia, formatowanie warunkowe, walidacja';
    }
}

function setExcelTemplate(template) {
    document.getElementById('cexceltemplate').value = template;
    calculateExcelOverpayment();
    return false;
}

function downloadExcelTemplate(template) {
    if (!template) {
        template = document.getElementById('cexceltemplate').value;
    }
    
    var templateName = getExcelTemplateName(template);
    var fileExtension = (template === 'business' || template === 'macros') ? '.xlsm' : '.xlsx';
    
    alert('Pobieranie szablonu: ' + templateName + fileExtension + '\n\n' +
          'Szablon zawiera:\n' +
          '- Automatyczne obliczenia nadpłat\n' +
          '- Harmonogram spłat\n' +
          '- Analizę oszczędności\n' +
          '- Wykresy i tabele\n' +
          '- Polski interfejs');
    
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Opcje szablonu Excel';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Opcje szablonu Excel';
    }
    calculateExcelOverpayment();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Szablony Excel</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Szablony Excel</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('cloanamount').value = '400000';
    document.getElementById('cinterestrate').value = '7.25';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('coverpayment').value = '500';
    document.getElementById('coverpaymentfreq').value = 'monthly';
    document.getElementById('cexceltemplate').value = 'advanced';
    
    document.getElementById('caddoptional').checked = true;
    cshtaxcost();
    calculateExcelOverpayment();
}

function saveCalResult() {
    alert('Wyniki kalkulatora Excel zostały zapisane!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Funkcje Excel &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Szablony Excel</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Szablony Excel &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Funkcje Excel</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateExcelOverpayment();
};
</script>

</body></html>
