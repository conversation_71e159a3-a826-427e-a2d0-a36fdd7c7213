<!DOCTYPE html>
<html lang="pl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Nadpłacanie kredytu hipotecznego kalkulator</title>
	<meta name="description" content="Nadpłacanie kredytu hipotecznego kalkulator - oblicz opłacalność nadpłat kredytu mieszkaniowego, sprawdź oszczędności i skrócenie okresu spłaty 2025.">
	<meta name="keywords" content="nadpłacanie kredytu hipotecznego kalkulator, nadpłacanie kredytu, op<PERSON><PERSON><PERSON><PERSON><PERSON> nadpłat, oszczędności kredyt">
	<link rel="stylesheet" href="style.css"><script src="common.js" async=""></script><meta name="viewport" content="width=device-width, initial-scale=1.0">	<link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
	<link rel="manifest" href="manifest.json"></head><body>
<div id="headerout">
	<div id="header">
		<div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
		<div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>	</div>
</div>
<div id="clear"></div><div id="contentout"><div id="content"><div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/" itemprop="item"><span itemprop="name">strona główna</span></a><meta itemprop="position" content="1"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a><meta itemprop="position" content="2"></span> / <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem"><a href="/nadplacanie-kredytu-hipotecznego-kalkulator.html" itemprop="item"><span itemprop="name">nadpłacanie kredytu hipotecznego kalkulator</span></a><meta itemprop="position" content="3"></span></div><div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>		<h1>Nadpłacanie kredytu hipotecznego kalkulator</h1>
<div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Sprawdź opłacalność nadpłacania kredytu"></div><div class="clefthalf">
<div class="panel"><form name="calform" action="/nadplacanie-kredytu-hipotecznego-kalkulator.html">
<table align="center">
<tbody><tr><td align="right">Kwota kredytu</td><td align="right"><input type="text" name="cloanamount" id="cloanamount" value="400000" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Oprocentowanie <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczne oprocentowanie kredytu hipotecznego. Wyższe oprocentowanie = większa opłacalność nadpłat.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cinterestrate" id="cinterestrate" value="7.25" class="inhalf inpct"></td><td>&nbsp;</td></tr>
<tr><td align="right">Okres kredytowania <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Pierwotny okres spłaty kredytu w latach. Dłuższy okres = większa opłacalność nadpłat.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="cloanterm" id="cloanterm" value="25" class="inhalf"></td><td>lat</td></tr>
<tr><td align="right">Kwota nadpłaty <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowa kwota spłacana ponad standardową ratę kredytu. Sprawdź opłacalność różnych kwot.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="coverpayment" id="coverpayment" value="500" class="inhalf indollar"></td><td>&nbsp;</td></tr>
<tr><td align="right">Częstotliwość nadpłaty</td><td align="left" colspan="2"><select name="coverpaymentfreq" id="coverpaymentfreq"><option value="monthly" selected="">Miesięcznie</option><option value="yearly">Rocznie</option><option value="once">Jednorazowo</option><option value="quarterly">Kwartalnie</option></select></td></tr>
<tr><td align="right">Strategia nadpłacania</td><td align="left" colspan="2"><select name="coverpaymentstrategy" id="coverpaymentstrategy"><option value="consistent" selected="">Stała kwota</option><option value="increasing">Rosnąca kwota</option><option value="decreasing">Malejąca kwota</option><option value="seasonal">Sezonowa</option></select></td></tr>
<tr><td align="right">Data rozpoczęcia nadpłat</td><td align="left" colspan="2"><select name="cstartmonth" id="cstartmonth"><option value="1">Sty</option><option value="2">Lut</option><option value="3">Mar</option><option value="4">Kwi</option><option value="5">Maj</option><option value="6">Cze</option><option value="7">Lip</option><option value="8" selected="">Sie</option><option value="9">Wrz</option><option value="10">Paź</option><option value="11">Lis</option><option value="12">Gru</option></select> <input type="text" name="cstartyear" id="cstartyear" value="2025" class="in4char"></td></tr>
<tr><td colspan="3"><br><label for="caddoptional" class="cbcontainer"><input type="checkbox" name="caddoptional" id="caddoptional" value="1" checked="" onclick="cshtaxcost();"><span class="cbmark"></span><b><span id="ctaxcostdesc">Zaawansowane opcje nadpłacania</span></b></label></td></tr>
<tr><td colspan="3" align="center">
<div id="ctaxcost" style="display: block;">
<table>
	<tbody><tr><td>&nbsp;</td><td colspan="2" align="center" valign="bottom">Analiza opłacalności nadpłat</td></tr>
	<tr><td align="right">Wzrost nadpłaty roczny <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Roczny wzrost kwoty nadpłaty (np. wraz ze wzrostem dochodów). Zwiększa opłacalność nadpłacania.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="coverpaymentinc" id="coverpaymentinc" value="3" class="innormal inpct"></td><td>%</td></tr>
	<tr><td align="right">Alternatywna inwestycja <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Oczekiwana stopa zwrotu z alternatywnej inwestycji. Porównanie z opłacalnością nadpłat.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="calternativerate" id="calternativerate" value="5.0" class="innormal inpct"></td><td>%</td></tr>
	<tr><td align="right">Opłata za nadpłatę <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Opłata banku za wcześniejszą spłatę kredytu. Zmniejsza opłacalność nadpłacania.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="coverpaymentfee" id="coverpaymentfee" value="0" class="innormal inpct"></td><td>%</td></tr>
	<tr><td align="right">Ulga podatkowa <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Odsetki kredytu hipotecznego można odliczyć od podatku. Zmniejsza opłacalność nadpłacania.', '');" onmouseout="tooltip.hide();"></td><td align="right"><input type="text" name="ctaxrelief" id="ctaxrelief" value="0" class="innormal inpct"></td><td>%</td></tr>
</tbody></table>

<div style="padding:10px 0px;font-weight:bold;"><span id="cmoreoptionlinks"><a href="#" onclick="cshmoreoption(1);return false;">+ Scenariusze opłacalności</a></span><input type="hidden" name="cmop" id="cmoreoption" value="0"></div>
<table id="cmoreoptioninputs" style="display: none;">
	<tbody><tr><td colspan="2" style="padding-top:5px;font-weight:bold;text-align:left;">Gotowe scenariusze nadpłacania</td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Bardzo opłacalne</td><td><a href="#" onclick="setProfitabilityScenario('very_profitable');return false;">Wysokie oprocentowanie</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Opłacalne</td><td><a href="#" onclick="setProfitabilityScenario('profitable');return false;">Średnie oprocentowanie</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Umiarkowanie opłacalne</td><td><a href="#" onclick="setProfitabilityScenario('moderate');return false;">Niskie oprocentowanie</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Nieopłacalne</td><td><a href="#" onclick="setProfitabilityScenario('unprofitable');return false;">Bardzo niskie oprocentowanie</a></td></tr>
	<tr><td style="padding-left:10px;text-align:left;">Porównanie z inwestycją</td><td><a href="#" onclick="setProfitabilityScenario('investment');return false;">Nadpłaty vs inwestycje</a></td></tr>
</tbody></table>
</div>
</td></tr>
<tr><td colspan="3" align="center"><input name="printit" value="0" type="hidden"><input type="button" name="x" value="Sprawdź opłacalność" onclick="calculateOverpaymentProfitability();"><input type="button" value="Wyczyść" onclick="clearForm(document.calform);"></td></tr>
</tbody></table>
</form></div>
</div>

<div class="crighthalf">
<a name="results"></a>
<div class="espaceforM">&nbsp;</div>
<table align="center">
<tbody><tr><td>
<h2 class="h2result">Opłacalność: &nbsp; 169%<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('TmFkcMWCYWNhbmllIGtyZWR5dHUgaGlwb3RlY3puZWdvIGthbGt1bGF0b3I=', 0, 'TmFkcMWCYWNhbmllIGtyZWR5dHUgaGlwb3RlY3puZWdvIGthbGt1bGF0b3I=', 'T3DEhWNhbG5vxZvEhw==', 'MTY5JQ==');"></h2>
<table cellpadding="3" width="100%">
<tbody><tr><td>&nbsp;</td><td align="right"><b>Bez nadpłat</b></td><td align="right"><b>Z nadpłatami</b></td><td align="right"><b>Różnica</b></td></tr>
<tr bgcolor="#dddddd"><td><b>Opłacalność nadpłat</b></td><td align="right"><b>-</b></td><td align="right"><b>169%</b></td><td align="right"><b>Bardzo opłacalne</b></td></tr>
<tr><td>Czas spłaty</td><td align="right">25 lat</td><td align="right">18 lat 4 miesiące</td><td align="right">-6 lat 8 miesięcy</td></tr>
<tr><td>Miesięczna rata</td><td align="right">3 485 zł</td><td align="right">3 985 zł</td><td align="right">+500 zł</td></tr>
<tr><td>Łączne spłaty</td><td align="right">1 045 500 zł</td><td align="right">860 000 zł</td><td align="right">-185 500 zł</td></tr>
<tr><td>Oszczędność odsetek</td><td align="right">645 500 zł</td><td align="right">460 000 zł</td><td align="right">-185 500 zł</td></tr>
<tr><td>Łączne nadpłaty</td><td align="right">0 zł</td><td align="right">110 000 zł</td><td align="right">+110 000 zł</td></tr>
<tr><td>Efektywność nadpłat</td><td align="right">-</td><td align="right">1.69 zł/zł</td><td align="right">69% zysku</td></tr>
<tr bgcolor="#f0f0f0"><td colspan="4" style="padding-top: 15px;">
<h3>Analiza opłacalności nadpłacania:</h3>
<p><strong>Wskaźnik opłacalności:</strong> 169% (każda złotówka nadpłaty oszczędza 1.69 zł odsetek)</p>
<p><strong>Ocena opłacalności:</strong> Bardzo opłacalne - zdecydowanie zalecane</p>
<p><strong>Porównanie z inwestycją:</strong> Nadpłaty (7.25%) vs Inwestycja (5.0%) = Nadpłaty lepsze o 2.25%</p>
<p><strong>Skrócenie okresu:</strong> 6 lat 8 miesięcy (27% pierwotnego okresu)</p>
<p><strong>Miesięczne oszczędności:</strong> 847 zł (po spłacie kredytu)</p>
</td></tr>
</tbody></table>
</td></tr></tbody></table>
</div>

<div id="clear"></div>

<h2 style="padding-bottom: 10px;">Analiza opłacalności nadpłacania</h2>

<div class="crighthalf">
<div style="text-align:center;">
<svg width="300" height="180">
<style>
.mclgrid{stroke:#999;stroke-width:0.5;} 
.mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
.mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
.mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
.mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
</style>
<text x="165" y="172" class="mcltitle">Opłacalność nadpłacania</text>
<line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
<text x="43" y="145" class="mcllabely">100%</text>
<line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
<text x="43" y="109.181" class="mcllabely">150%</text>
<line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
<text x="43" y="73.361" class="mcllabely">200%</text>

<!-- Bardzo niskie oprocentowanie -->
<rect x="50" y="135" width="35" height="10" fill="#e74c3c" opacity="0.8"></rect>
<text x="67" y="155" class="mcllabelx">2-3%</text>
<text x="67" y="130" class="mcllabelx" style="fill:#000;">110%</text>

<!-- Niskie oprocentowanie -->
<rect x="95" y="125" width="35" height="20" fill="#f39c12" opacity="0.8"></rect>
<text x="112" y="155" class="mcllabelx">4-5%</text>
<text x="112" y="120" class="mcllabelx" style="fill:#000;">130%</text>

<!-- Średnie oprocentowanie -->
<rect x="140" y="105" width="35" height="40" fill="#2ecc71" opacity="0.8"></rect>
<text x="157" y="155" class="mcllabelx">6-7%</text>
<text x="157" y="100" class="mcllabelx" style="fill:#000;">150%</text>

<!-- Wysokie oprocentowanie -->
<rect x="185" y="85" width="35" height="60" fill="#3498db" opacity="0.8"></rect>
<text x="202" y="155" class="mcllabelx">7-8%</text>
<text x="202" y="80" class="mcllabelx" style="fill:#000;">169%</text>

<!-- Bardzo wysokie oprocentowanie -->
<rect x="230" y="65" width="35" height="80" fill="#9b59b6" opacity="0.8"></rect>
<text x="247" y="155" class="mcllabelx">9%+</text>
<text x="247" y="60" class="mcllabelx" style="fill:#000;">190%</text>

<rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
</svg>
</div>
<br>
</div>

<div class="clefthalf">
<div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Opłacalność według oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie strategii</a></div>
<div id="monthlyamo" style="display:none;">
<table class="cinfoT">
<tbody>
<tr align="center">
<th>Strategia</th>
<th>Oprocentowanie</th>
<th>Opłacalność</th>
<th>Rekomendacja</th>
<th>Oszczędność</th>
</tr>
<tr>
<td>Bardzo niskie</td>
<td>2-3%</td>
<td>110%</td>
<td>Rozważ inwestycje</td>
<td>45 000 zł</td>
</tr>
<tr>
<td>Niskie</td>
<td>4-5%</td>
<td>130%</td>
<td>Umiarkowanie opłacalne</td>
<td>85 000 zł</td>
</tr>
<tr>
<td>Średnie</td>
<td>6-7%</td>
<td>150%</td>
<td>Opłacalne</td>
<td>145 000 zł</td>
</tr>
<tr>
<td>Wysokie</td>
<td>7-8%</td>
<td>169%</td>
<td>Bardzo opłacalne</td>
<td>185 000 zł</td>
</tr>
<tr>
<td>Bardzo wysokie</td>
<td>9%+</td>
<td>190%</td>
<td>Zdecydowanie zalecane</td>
<td>245 000 zł</td>
</tr>
</tbody>
</table>
</div>
</div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Nadpłacanie kredytu hipotecznego kalkulator - analiza opłacalności nadpłat 2025</h2>
            <p>Nadpłacanie kredytu hipotecznego kalkulator to zaawansowane narzędzie do analizy opłacalności nadpłat kredytu mieszkaniowego. Nasz kalkulator uwzględnia wszystkie czynniki wpływające na opłacalność nadpłacania: oprocentowanie kredytu, alternatywne inwestycje, opłaty bankowe, ulgi podatkowe i różne strategie nadpłacania.</p>

            <h3>Kiedy nadpłacanie kredytu jest opłacalne:</h3>
            <div style="margin: 15px 0;">
                <h4>Bardzo opłacalne (opłacalność > 180%):</h4>
                <ul>
                    <li><strong>Wysokie oprocentowanie:</strong> 8-10% i więcej</li>
                    <li><strong>Długi okres kredytowania:</strong> 25-30 lat</li>
                    <li><strong>Brak opłat za nadpłaty:</strong> 0% prowizji</li>
                    <li><strong>Niskie stopy inwestycyjne:</strong> Alternatywy < 5%</li>
                    <li><strong>Rekomendacja:</strong> Zdecydowanie zalecane nadpłacanie</li>
                </ul>

                <h4>Opłacalne (opłacalność 150-180%):</h4>
                <ul>
                    <li><strong>Średnie oprocentowanie:</strong> 6-8%</li>
                    <li><strong>Standardowy okres:</strong> 20-25 lat</li>
                    <li><strong>Niskie opłaty:</strong> 0-1% prowizji</li>
                    <li><strong>Umiarkowane alternatywy:</strong> Inwestycje 4-6%</li>
                    <li><strong>Rekomendacja:</strong> Zalecane nadpłacanie</li>
                </ul>

                <h4>Umiarkowanie opłacalne (opłacalność 120-150%):</h4>
                <ul>
                    <li><strong>Niskie oprocentowanie:</strong> 4-6%</li>
                    <li><strong>Krótki okres:</strong> 15-20 lat</li>
                    <li><strong>Średnie opłaty:</strong> 1-2% prowizji</li>
                    <li><strong>Konkurencyjne inwestycje:</strong> 5-7%</li>
                    <li><strong>Rekomendacja:</strong> Rozważ indywidualnie</li>
                </ul>

                <h4>Nieopłacalne (opłacalność < 120%):</h4>
                <ul>
                    <li><strong>Bardzo niskie oprocentowanie:</strong> 2-4%</li>
                    <li><strong>Wysokie opłaty:</strong> 2-3% prowizji</li>
                    <li><strong>Atrakcyjne inwestycje:</strong> > 7%</li>
                    <li><strong>Ulgi podatkowe:</strong> Wysokie odliczenia</li>
                    <li><strong>Rekomendacja:</strong> Lepiej inwestować</li>
                </ul>
            </div>

            <h3>Strategie nadpłacania kredytu:</h3>
            <div style="margin: 15px 0;">
                <h4>Stała kwota nadpłaty:</h4>
                <ul>
                    <li><strong>Zasada:</strong> Miesięcznie ta sama kwota nadpłaty</li>
                    <li><strong>Zalety:</strong> Przewidywalność, łatwość planowania</li>
                    <li><strong>Wady:</strong> Nie uwzględnia wzrostu dochodów</li>
                    <li><strong>Dla kogo:</strong> Osoby o stabilnych dochodach</li>
                    <li><strong>Przykład:</strong> 500 zł miesięcznie przez cały okres</li>
                </ul>

                <h4>Rosnąca kwota nadpłaty:</h4>
                <ul>
                    <li><strong>Zasada:</strong> Stopniowe zwiększanie nadpłat</li>
                    <li><strong>Zalety:</strong> Uwzględnia wzrost dochodów</li>
                    <li><strong>Wady:</strong> Wymaga dyscypliny finansowej</li>
                    <li><strong>Dla kogo:</strong> Osoby o rosnących dochodach</li>
                    <li><strong>Przykład:</strong> Start 300 zł, wzrost 3% rocznie</li>
                </ul>

                <h4>Malejąca kwota nadpłaty:</h4>
                <ul>
                    <li><strong>Zasada:</strong> Wysokie nadpłaty na początku</li>
                    <li><strong>Zalety:</strong> Maksymalne oszczędności odsetek</li>
                    <li><strong>Wady:</strong> Wysokie obciążenie na początku</li>
                    <li><strong>Dla kogo:</strong> Osoby z wysokimi dochodami</li>
                    <li><strong>Przykład:</strong> Start 1000 zł, spadek 5% rocznie</li>
                </ul>

                <h4>Nadpłaty sezonowe:</h4>
                <ul>
                    <li><strong>Zasada:</strong> Nadpłaty w określonych miesiącach</li>
                    <li><strong>Zalety:</strong> Wykorzystanie premii, 13-tek</li>
                    <li><strong>Wady:</strong> Nieregularność</li>
                    <li><strong>Dla kogo:</strong> Osoby z sezonowymi dochodami</li>
                    <li><strong>Przykład:</strong> Podwójne nadpłaty w grudniu</li>
                </ul>
            </div>

            <h3>Czynniki wpływające na opłacalność:</h3>
            <div style="margin: 15px 0;">
                <h4>Oprocentowanie kredytu:</h4>
                <ul>
                    <li><strong>Bardzo wysokie (9%+):</strong> Opłacalność 190%+</li>
                    <li><strong>Wysokie (7-8%):</strong> Opłacalność 160-190%</li>
                    <li><strong>Średnie (5-7%):</strong> Opłacalność 130-160%</li>
                    <li><strong>Niskie (3-5%):</strong> Opłacalność 110-130%</li>
                    <li><strong>Bardzo niskie (2-3%):</strong> Opłacalność 100-110%</li>
                </ul>

                <h4>Alternatywne inwestycje:</h4>
                <ul>
                    <li><strong>Lokaty bankowe:</strong> 3-5% rocznie (bezpieczne)</li>
                    <li><strong>Obligacje skarbowe:</strong> 4-6% rocznie (bezpieczne)</li>
                    <li><strong>Fundusze obligacji:</strong> 5-7% rocznie (niskie ryzyko)</li>
                    <li><strong>Fundusze akcji:</strong> 6-10% rocznie (średnie ryzyko)</li>
                    <li><strong>Akcje:</strong> 8-12% rocznie (wysokie ryzyko)</li>
                    <li><strong>Nieruchomości:</strong> 5-8% rocznie (średnie ryzyko)</li>
                </ul>

                <h4>Opłaty i koszty:</h4>
                <ul>
                    <li><strong>Prowizja za nadpłatę:</strong> 0-3% nadpłacanej kwoty</li>
                    <li><strong>Opłata administracyjna:</strong> 50-200 zł za operację</li>
                    <li><strong>Koszt alternatywny:</strong> Utracone zyski z inwestycji</li>
                    <li><strong>Inflacja:</strong> Zmniejsza realną wartość oszczędności</li>
                </ul>

                <h4>Ulgi podatkowe:</h4>
                <ul>
                    <li><strong>Odliczenie odsetek:</strong> Do 8 000 zł rocznie</li>
                    <li><strong>Efektywne oprocentowanie:</strong> Niższe o ulgę podatkową</li>
                    <li><strong>Wpływ na opłacalność:</strong> Zmniejsza korzyści z nadpłat</li>
                    <li><strong>Przykład:</strong> 7% - 18% ulga = 5.74% efektywnie</li>
                </ul>
            </div>

            <h3>Porównanie strategii nadpłacania:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Strategia</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oprocentowanie</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Opłacalność</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Oszczędność</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Rekomendacja</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Bardzo wysokie</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">9%+</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">190%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">245 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zdecydowanie zalecane</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Wysokie</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">7-8%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">169%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">185 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Bardzo opłacalne</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Średnie</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">5-7%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">150%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">145 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Opłacalne</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Niskie</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">3-5%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">130%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">85 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Umiarkowanie opłacalne</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;"><strong>Bardzo niskie</strong></td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2-3%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">110%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">45 000 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rozważ inwestycje</td>
                    </tr>
                </table>
            </div>

            <h3>Wskazówki dla nadpłacających kredyt:</h3>
            <ul>
                <li><strong>Sprawdź warunki umowy:</strong> Czy bank pobiera opłaty za nadpłaty</li>
                <li><strong>Zachowaj rezerwę:</strong> Nie nadpłacaj wszystkich oszczędności</li>
                <li><strong>Porównaj z inwestycjami:</strong> Sprawdź alternatywne możliwości</li>
                <li><strong>Uwzględnij ulgi podatkowe:</strong> Odliczenia zmniejszają opłacalność</li>
                <li><strong>Planuj długoterminowo:</strong> Nadpłaty to strategia na lata</li>
                <li><strong>Monitoruj oprocentowanie:</strong> Zmiany wpływają na opłacalność</li>
                <li><strong>Dokumentuj nadpłaty:</strong> Prowadź ewidencję dla banku</li>
                <li><strong>Rozważ refinansowanie:</strong> Czasem lepsze niż nadpłaty</li>
            </ul>
        </div>
</div>

<div id="right">
<div id="othercalc">
<div id="octitle">
<a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
</div>
<div id="occontent">
<a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
<a href="/kalkulator-nadplaty-kredytu-hipotecznego.html">Kalkulator nadpłaty kredytu</a>
<a href="/kalkulator-odsetek-kredytu-hipotecznego.html">Kalkulator odsetek kredytu</a>
<a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
<a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
<a href="/refinansowanie-kredytu-hipotecznego-kalkulator.html">Refinansowanie kredytu</a>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Scenariusze opłacalności</div>
<div id="occontent" style="padding: 10px;">
<div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
<strong>Sprawdź opłacalność:</strong><br>
<a href="#" onclick="return setProfitabilityScenario('very_profitable');">Bardzo opłacalne</a><br>
<a href="#" onclick="return setProfitabilityScenario('profitable');">Opłacalne</a><br>
<a href="#" onclick="return setProfitabilityScenario('moderate');">Umiarkowanie opłacalne</a><br>
<a href="#" onclick="return setProfitabilityScenario('unprofitable');">Nieopłacalne</a><br>
<a href="#" onclick="return setProfitabilityScenario('investment');">Porównanie z inwestycją</a>
</div>
</div>
</div>
</div>

<div style="padding-top: 20px;">
<div id="othercalc">
<div id="octitle">Wyszukaj kalkulator</div>
<div id="calcSearchOut">
<div>
<input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
<input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
</div>
</div>
</div>
</div>
</div>

<div id="footer">
<div id="footerin">
<div id="footernav">
<a href="/o-nas.html">O nas</a> | 
<a href="/kontakt.html">Kontakt</a> | 
<a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
<a href="/regulamin.html">Regulamin</a> | 
<a href="/mapa-strony.html">Mapa strony</a>
</div>
<p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
<p>Nadpłacanie kredytu hipotecznego kalkulator - sprawdź opłacalność nadpłat kredytu mieszkaniowego.</p>
</div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function calculateOverpaymentProfitability() {
    // 获取基本贷款信息
    var loanAmount = parseFloat(document.getElementById('cloanamount').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    
    // 获取nadpłata信息
    var overpaymentAmount = parseFloat(document.getElementById('coverpayment').value) || 0;
    var overpaymentFreq = document.getElementById('coverpaymentfreq').value;
    var overpaymentStrategy = document.getElementById('coverpaymentstrategy').value;
    
    // 获取高级选项
    var overpaymentInc = parseFloat(document.getElementById('coverpaymentinc').value) || 0;
    var alternativeRate = parseFloat(document.getElementById('calternativerate').value) || 0;
    var overpaymentFee = parseFloat(document.getElementById('coverpaymentfee').value) || 0;
    var taxRelief = parseFloat(document.getElementById('ctaxrelief').value) || 0;
    
    // 计算基本月供
    var monthlyRate = interestRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;
    
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    // 计算没有nadpłata的总成本
    var totalWithoutOverpayment = monthlyPayment * numPayments;
    var interestWithoutOverpayment = totalWithoutOverpayment - loanAmount;
    
    // 计算有nadpłata的情况
    var remainingBalance = loanAmount;
    var totalPaid = 0;
    var monthsPaid = 0;
    var totalOverpayments = 0;
    var totalFees = 0;
    var currentOverpayment = overpaymentAmount;
    
    while (remainingBalance > 0.01 && monthsPaid < numPayments) {
        // 计算当月利息
        var monthlyInterest = remainingBalance * monthlyRate;
        var monthlyPrincipal = monthlyPayment - monthlyInterest;
        
        // 添加nadpłata
        var thisMonthOverpayment = 0;
        if (overpaymentFreq === 'monthly' || 
           (overpaymentFreq === 'yearly' && monthsPaid % 12 === 0) ||
           (overpaymentFreq === 'quarterly' && monthsPaid % 3 === 0) ||
           (overpaymentFreq === 'once' && monthsPaid === 0)) {
            
            // 应用策略
            switch(overpaymentStrategy) {
                case 'consistent':
                    thisMonthOverpayment = currentOverpayment;
                    break;
                case 'increasing':
                    thisMonthOverpayment = currentOverpayment * (1 + (monthsPaid * 0.01));
                    break;
                case 'decreasing':
                    thisMonthOverpayment = currentOverpayment * (1 - (monthsPaid * 0.005));
                    break;
                case 'seasonal':
                    var month = monthsPaid % 12;
                    thisMonthOverpayment = (month === 11 || month === 0) ? currentOverpayment * 2 : currentOverpayment;
                    break;
            }
            
            // 计算费用
            var fee = thisMonthOverpayment * (overpaymentFee / 100);
            totalFees += fee;
            
            // 年度增长
            if (overpaymentFreq === 'monthly' && monthsPaid % 12 === 11) {
                currentOverpayment *= (1 + overpaymentInc / 100);
            }
        }
        
        // 更新余额
        remainingBalance -= (monthlyPrincipal + thisMonthOverpayment);
        totalPaid += monthlyPayment + thisMonthOverpayment + fee;
        totalOverpayments += thisMonthOverpayment;
        monthsPaid++;
        
        if (remainingBalance <= 0) break;
    }
    
    // 计算节省
    var timeSaved = numPayments - monthsPaid;
    var moneySaved = totalWithoutOverpayment - totalPaid;
    var interestSaved = interestWithoutOverpayment - (totalPaid - loanAmount - totalOverpayments - totalFees);
    
    // 计算opłacalność (考虑税收减免)
    var effectiveInterestRate = interestRate * (1 - taxRelief / 100);
    var profitability = totalOverpayments > 0 ? (moneySaved / totalOverpayments) * 100 : 0;
    
    // 与替代投资比较
    var alternativeGain = totalOverpayments * Math.pow(1 + alternativeRate / 100, loanTerm) - totalOverpayments;
    var comparisonWithInvestment = moneySaved - alternativeGain;
    
    // 更新显示
    updateProfitabilityResults(monthlyPayment, totalWithoutOverpayment, totalPaid, 
                             timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments, 
                             totalFees, profitability, alternativeRate, comparisonWithInvestment, loanTerm);
}

function updateProfitabilityResults(monthlyPayment, totalWithoutOverpayment, totalPaid, 
                                  timeSaved, moneySaved, interestSaved, monthsPaid, totalOverpayments,
                                  totalFees, profitability, alternativeRate, comparisonWithInvestment, loanTerm) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }
    
    function formatTime(months) {
        var years = Math.floor(months / 12);
        var remainingMonths = months % 12;
        if (years > 0 && remainingMonths > 0) {
            return years + ' lat ' + remainingMonths + ' miesięcy';
        } else if (years > 0) {
            return years + ' lat';
        } else {
            return remainingMonths + ' miesięcy';
        }
    }
    
    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML = 
            'Opłacalność: &nbsp; ' + Math.round(profitability) + '%' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }
    
    // 更新analiza opłacalności
    var analysisSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (analysisSection) {
        var monthlyAfterPayoff = monthlyPayment + (totalOverpayments/monthsPaid);
        var recommendation = getProfitabilityRecommendation(profitability, comparisonWithInvestment);
        
        analysisSection.innerHTML = 
            '<h3>Analiza opłacalności nadpłacania:</h3>' +
            '<p><strong>Wskaźnik opłacalności:</strong> ' + Math.round(profitability) + '% (każda złotówka nadpłaty oszczędza ' + (profitability/100).toFixed(2) + ' zł odsetek)</p>' +
            '<p><strong>Ocena opłacalności:</strong> ' + recommendation.status + '</p>' +
            '<p><strong>Porównanie z inwestycją:</strong> Nadpłaty (' + document.getElementById('cinterestrate').value + '%) vs Inwestycja (' + alternativeRate + '%) = ' + 
            (comparisonWithInvestment > 0 ? 'Nadpłaty lepsze o ' + formatNumber(comparisonWithInvestment) + ' zł' : 'Inwestycja lepsza o ' + formatNumber(-comparisonWithInvestment) + ' zł') + '</p>' +
            '<p><strong>Skrócenie okresu:</strong> ' + formatTime(timeSaved) + ' (' + Math.round((timeSaved/(loanTerm*12))*100) + '% pierwotnego okresu)</p>' +
            '<p><strong>Miesięczne oszczędności:</strong> ' + formatNumber(monthlyAfterPayoff) + ' zł (po spłacie kredytu)</p>';
    }
}

function getProfitabilityRecommendation(profitability, comparisonWithInvestment) {
    if (profitability > 180) {
        return {status: 'Bardzo opłacalne - zdecydowanie zalecane', color: '#2ecc71'};
    } else if (profitability > 150) {
        return {status: 'Opłacalne - zalecane', color: '#27ae60'};
    } else if (profitability > 120) {
        return {status: 'Umiarkowanie opłacalne', color: '#f39c12'};
    } else if (comparisonWithInvestment > 0) {
        return {status: 'Lepsze niż inwestycje - rozważ', color: '#e67e22'};
    } else {
        return {status: 'Rozważ alternatywne inwestycje', color: '#e74c3c'};
    }
}

function setProfitabilityScenario(scenario) {
    switch(scenario) {
        case 'very_profitable':
            document.getElementById('cinterestrate').value = '8.5';
            document.getElementById('calternativerate').value = '4.0';
            document.getElementById('coverpaymentfee').value = '0';
            break;
        case 'profitable':
            document.getElementById('cinterestrate').value = '7.0';
            document.getElementById('calternativerate').value = '5.0';
            document.getElementById('coverpaymentfee').value = '0';
            break;
        case 'moderate':
            document.getElementById('cinterestrate').value = '5.5';
            document.getElementById('calternativerate').value = '5.0';
            document.getElementById('coverpaymentfee').value = '1';
            break;
        case 'unprofitable':
            document.getElementById('cinterestrate').value = '3.0';
            document.getElementById('calternativerate').value = '6.0';
            document.getElementById('coverpaymentfee').value = '2';
            break;
        case 'investment':
            document.getElementById('cinterestrate').value = '6.0';
            document.getElementById('calternativerate').value = '7.0';
            document.getElementById('coverpaymentfee').value = '0';
            break;
    }
    calculateOverpaymentProfitability();
    return false;
}

function cshtaxcost() {
    var checkbox = document.getElementById('caddoptional');
    var taxcostDiv = document.getElementById('ctaxcost');
    if (checkbox.checked) {
        taxcostDiv.style.display = 'block';
        document.getElementById('ctaxcostdesc').innerHTML = 'Zaawansowane opcje nadpłacania';
    } else {
        taxcostDiv.style.display = 'none';
        document.getElementById('ctaxcostdesc').innerHTML = 'Zaawansowane opcje nadpłacania';
    }
    calculateOverpaymentProfitability();
}

function cshmoreoption(show) {
    var moreDiv = document.getElementById('cmoreoptioninputs');
    var linkSpan = document.getElementById('cmoreoptionlinks');
    var hiddenField = document.getElementById('cmoreoption');
    
    if (show) {
        moreDiv.style.display = 'block';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(0);return false;">- Scenariusze opłacalności</a>';
        hiddenField.value = '1';
    } else {
        moreDiv.style.display = 'none';
        linkSpan.innerHTML = '<a href="#" onclick="cshmoreoption(1);return false;">+ Scenariusze opłacalności</a>';
        hiddenField.value = '0';
    }
}

function clearForm(form) {
    document.getElementById('cloanamount').value = '400000';
    document.getElementById('cinterestrate').value = '7.25';
    document.getElementById('cloanterm').value = '25';
    document.getElementById('coverpayment').value = '500';
    document.getElementById('coverpaymentfreq').value = 'monthly';
    document.getElementById('coverpaymentstrategy').value = 'consistent';
    
    document.getElementById('coverpaymentinc').value = '3';
    document.getElementById('calternativerate').value = '5.0';
    document.getElementById('coverpaymentfee').value = '0';
    document.getElementById('ctaxrelief').value = '0';
    
    document.getElementById('caddoptional').checked = true;
    cshtaxcost();
    calculateOverpaymentProfitability();
}

function saveCalResult() {
    alert('Analiza opłacalności nadpłacania została zapisana!');
    return false;
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Porównanie strategii &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Opłacalność według oprocentowania</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Opłacalność według oprocentowania &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Porównanie strategii</a>';
    }
    return false;
}

function calcSearch() {
    var term = document.getElementById('calcSearchTerm').value;
    if (term) {
        window.location.href = '/search.php?q=' + encodeURIComponent(term);
    }
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateOverpaymentProfitability();
};
</script>

</body></html>
