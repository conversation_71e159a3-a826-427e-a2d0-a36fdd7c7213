<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Test Calculator Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        input, select { margin: 5px; padding: 5px; }
        button { padding: 10px 20px; margin: 10px; }
    </style>
</head>
<body>
    <h1>Test Calculator Fixes</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Mortgage Calculator</h2>
        <label>House Price: <input type="text" id="housePrice" value="500000"></label><br>
        <label>Down Payment: <input type="text" id="downPayment" value="20"></label>
        <select id="downPaymentUnit">
            <option value="p">%</option>
            <option value="d">zł</option>
        </select><br>
        <label>Loan Term: <input type="text" id="loanTerm" value="25"> years</label><br>
        <label>Interest Rate: <input type="text" id="interestRate" value="7.25">%</label><br>
        <button onclick="testBasicCalculation()">Calculate</button>
        <div id="result1" class="result">Click Calculate to see result</div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Element ID Test</h2>
        <p>Testing if all elements can be found by JavaScript:</p>
        <button onclick="testElementAccess()">Test Element Access</button>
        <div id="result2" class="result">Click Test to see if elements are accessible</div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Unit Conversion Test</h2>
        <p>Testing percentage vs złoty conversion:</p>
        <label>Value: <input type="text" id="testValue" value="100000"></label>
        <label>Percentage: <input type="text" id="testPercent" value="20"></label><br>
        <button onclick="testUnitConversion()">Test Conversion</button>
        <div id="result3" class="result">Click Test to see conversion results</div>
    </div>

    <script>
        function testBasicCalculation() {
            try {
                // Get input values
                var housePrice = parseFloat(document.getElementById('housePrice').value) || 0;
                var downPayment = parseFloat(document.getElementById('downPayment').value) || 0;
                var downPaymentUnit = document.getElementById('downPaymentUnit').value;
                var loanTerm = parseFloat(document.getElementById('loanTerm').value) || 0;
                var interestRate = parseFloat(document.getElementById('interestRate').value) || 0;
                
                // Calculate down payment amount
                var downPaymentAmount;
                if (downPaymentUnit === 'p') {
                    downPaymentAmount = housePrice * (downPayment / 100);
                } else if (downPaymentUnit === 'd') {
                    downPaymentAmount = downPayment;
                } else {
                    downPaymentAmount = downPayment;
                }
                
                var loanAmount = housePrice - downPaymentAmount;
                
                // Calculate monthly payment
                var monthlyRate = interestRate / 100 / 12;
                var numPayments = loanTerm * 12;
                var monthlyPayment = 0;
                
                if (monthlyRate > 0) {
                    monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / 
                                    (Math.pow(1 + monthlyRate, numPayments) - 1);
                } else {
                    monthlyPayment = loanAmount / numPayments;
                }
                
                // Display result
                document.getElementById('result1').innerHTML = 
                    '<strong>SUCCESS!</strong><br>' +
                    'House Price: ' + housePrice.toLocaleString('pl-PL') + ' zł<br>' +
                    'Down Payment: ' + downPaymentAmount.toLocaleString('pl-PL') + ' zł (' + downPayment + (downPaymentUnit === 'p' ? '%' : ' zł') + ')<br>' +
                    'Loan Amount: ' + loanAmount.toLocaleString('pl-PL') + ' zł<br>' +
                    'Monthly Payment: ' + Math.round(monthlyPayment).toLocaleString('pl-PL') + ' zł<br>' +
                    'Interest Rate: ' + interestRate + '%<br>' +
                    'Loan Term: ' + loanTerm + ' years';
                    
            } catch (error) {
                document.getElementById('result1').innerHTML = '<strong>ERROR:</strong> ' + error.message;
            }
        }
        
        function testElementAccess() {
            var results = [];
            var elements = ['housePrice', 'downPayment', 'downPaymentUnit', 'loanTerm', 'interestRate'];
            
            elements.forEach(function(id) {
                var element = document.getElementById(id);
                if (element) {
                    results.push('✓ ' + id + ': Found (value: ' + element.value + ')');
                } else {
                    results.push('✗ ' + id + ': NOT FOUND');
                }
            });
            
            document.getElementById('result2').innerHTML = 
                '<strong>Element Access Test:</strong><br>' + results.join('<br>');
        }
        
        function testUnitConversion() {
            var value = parseFloat(document.getElementById('testValue').value) || 0;
            var percent = parseFloat(document.getElementById('testPercent').value) || 0;
            
            var percentResult = value * (percent / 100);
            var directResult = percent;
            
            document.getElementById('result3').innerHTML = 
                '<strong>Unit Conversion Test:</strong><br>' +
                'Base Value: ' + value.toLocaleString('pl-PL') + ' zł<br>' +
                'Percentage (' + percent + '%): ' + percentResult.toLocaleString('pl-PL') + ' zł<br>' +
                'Direct Value: ' + directResult.toLocaleString('pl-PL') + ' zł<br>' +
                '<br><strong>Unit Logic:</strong><br>' +
                'if (unit === "p") → use percentage calculation<br>' +
                'if (unit === "d") → use direct value<br>' +
                'if (unit === "z") → use direct value';
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            testElementAccess();
            testUnitConversion();
        };
    </script>
</body>
</html>
