const fs = require('fs');
const path = require('path');

const adsenseScript = `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2205593928173688"
     crossorigin="anonymous"></script>`;

function addAdsenseToFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Check if AdSense script already exists
        if (content.includes('ca-pub-2205593928173688')) {
            console.log(`AdSense already exists in: ${filePath}`);
            return;
        }
        
        // Add AdSense script before closing </head> tag
        if (content.includes('</head>')) {
            content = content.replace('</head>', `${adsenseScript}\n</head>`);
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`Added AdSense to: ${filePath}`);
        } else {
            console.log(`No </head> tag found in: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

function processDirectory(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            processDirectory(fullPath);
        } else if (file.endsWith('.html')) {
            addAdsenseToFile(fullPath);
        }
    });
}

// Process current directory
processDirectory('.');
console.log('AdSense script addition completed!');