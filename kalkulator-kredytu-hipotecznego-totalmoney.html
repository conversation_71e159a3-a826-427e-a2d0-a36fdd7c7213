<!DOCTYPE html>
<html lang="pl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Kalkulator kredytu hipotecznego TotalMoney</title>
    <meta name="description" content="Kalkulator kredytu hipotecznego TotalMoney - oblicz ratę kredytu mieszkaniowego w TotalMoney.pl, sprawdź warunki i promocje TotalMoney na kredyty hipoteczne 2025.">
    <meta name="keywords" content="kalkulator kredytu hipotecznego totalmoney, totalmoney kredyt hipoteczny, rata kredytu totalmoney, warunki kredytu totalmoney">
    <link rel="stylesheet" href="style.css">
    <script src="common.js" async=""></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="manifest" href="manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="/"><img src="calculator-white.svg" width="208" height="22" alt="Kalkulator.pl"></a></div>
        <div id="login"><a href="/konto/logowanie.php">zaloguj się</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/" itemprop="item"><span itemprop="name">strona główna</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulatory-finansowe.html" itemprop="item"><span itemprop="name">finansowe</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="/kalkulator-kredytu-hipotecznego-totalmoney.html" itemprop="item"><span itemprop="name">kalkulator kredytu hipotecznego totalmoney</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="document.calform.printit.value=1;document.calform.submit();return false;">Drukuj</a></div>
        <h1>Kalkulator kredytu hipotecznego TotalMoney</h1>
        <div id="insmdc"><img src="insm.svg" width="630" height="35" alt="Oblicz kredyt hipoteczny w TotalMoney.pl"></div>
        
        <div class="clefthalf">
            <div class="panel">
                <form name="calform" action="/kalkulator-kredytu-hipotecznego-totalmoney.html">
                    <table align="center">
                        <tbody>
                            <tr>
                                <td align="right">Wartość nieruchomości TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Całkowita wartość nieruchomości w TotalMoney.pl. Na podstawie tej kwoty TotalMoney obliczy maksymalną kwotę kredytu.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ctotalmoneypropertyvalue" id="ctotalmoneypropertyvalue" value="650000" class="inhalf indollar"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Wkład własny TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Wkład własny w TotalMoney.pl. Minimum 10% wartości nieruchomości. Wyższy wkład = lepsza marża TotalMoney.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right" valign="bottom"><input type="text" name="ctotalmoneydownpayment" id="ctotalmoneydownpayment" value="20" class="inhalf inpct"></td>
                                <td><select name="ctotalmoneydownpaymentunit" id="ctotalmoneydownpaymentunit" onchange="cunitchangetotalmoney('ctotalmoneydownpayment', this.value);"><option value="p" selected="">%</option><option value="d">zł</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">Okres kredytowania TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Okres spłaty kredytu w TotalMoney.pl. Maksymalnie 35 lat. Dłuższy okres = niższa rata TotalMoney.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ctotalmoneyloanterm" id="ctotalmoneyloanterm" value="25" class="inhalf"></td>
                                <td>lat</td>
                            </tr>
                            <tr>
                                <td align="right">Oprocentowanie TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Aktualne oprocentowanie kredytu hipotecznego w TotalMoney.pl. WIBOR 3M + marża TotalMoney.', '');" onmouseout="tooltip.hide();"></td>
                                <td align="right"><input type="text" name="ctotalmoneyinterestrate" id="ctotalmoneyinterestrate" value="7.85" class="inhalf inpct"></td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="right">Profil klienta TotalMoney</td>
                                <td align="left" colspan="2">
                                    <select name="ctotalmoneyclientprofile" id="ctotalmoneyclientprofile" onchange="updateTotalMoneyRate();">
                                        <option value="new_totalmoney" selected="">Nowy klient TotalMoney</option>
                                        <option value="existing_totalmoney">Obecny klient TotalMoney</option>
                                        <option value="premium_totalmoney">TotalMoney Premium</option>
                                        <option value="vip_totalmoney">TotalMoney VIP</option>
                                        <option value="business_totalmoney">TotalMoney Business</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">Typ oprocentowania TotalMoney</td>
                                <td align="left" colspan="2">
                                    <select name="ctotalmoneyinteresttype" id="ctotalmoneyinteresttype" onchange="updateTotalMoneyInterestType();">
                                        <option value="variable_totalmoney" selected="">Zmienne TotalMoney (WIBOR + marża)</option>
                                        <option value="fixed_3y_totalmoney">Stałe na 3 lata TotalMoney</option>
                                        <option value="fixed_5y_totalmoney">Stałe na 5 lat TotalMoney</option>
                                        <option value="fixed_10y_totalmoney">Stałe na 10 lat TotalMoney</option>
                                        <option value="mixed_totalmoney">Mieszane TotalMoney</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddtotalmoneyfees" class="cbcontainer">
                                        <input type="checkbox" name="caddtotalmoneyfees" id="caddtotalmoneyfees" value="1" checked="" onclick="cshtotalmoneyfees();">
                                        <span class="cbmark"></span>
                                        <b><span id="ctotalmoneyfeesdesc">Opłaty TotalMoney</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ctotalmoneyfees" style="display: block;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td colspan="2" align="center" valign="bottom">Opłaty i koszty TotalMoney</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Prowizja przygotowawcza TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Prowizja TotalMoney.pl za przygotowanie kredytu. 0-3% kwoty kredytu w zależności od profilu klienta TotalMoney.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ctotalmoneyprovision" id="ctotalmoneyprovision" value="2.2" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Rachunek kredytowy TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Miesięczna opłata za prowadzenie rachunku kredytowego w TotalMoney.pl. 0-30 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ctotalmoneyaccount" id="ctotalmoneyaccount" value="20" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Ubezpieczenie TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Ubezpieczenie spłaty kredytu w TotalMoney.pl. 0.25-0.45% kwoty kredytu rocznie.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ctotalmoneyinsurance" id="ctotalmoneyinsurance" value="0.35" class="innormal inpct"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Wycena nieruchomości TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Koszt wyceny nieruchomości przez rzeczoznawcę TotalMoney.pl. 1200-2200 zł.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ctotalmoneyvaluation" id="ctotalmoneyvaluation" value="1600" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Inne opłaty TotalMoney <img class="ttimg" alt="?" src="help.svg" onmouseover="tooltip.show('Dodatkowe opłaty TotalMoney.pl: notariusz, wpis hipoteki, ubezpieczenie nieruchomości.', '');" onmouseout="tooltip.hide();"></td>
                                                    <td align="right"><input type="text" name="ctotalmoneyother" id="ctotalmoneyother" value="4200" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3"><br>
                                    <label for="caddtotalmoneypromo" class="cbcontainer">
                                        <input type="checkbox" name="caddtotalmoneypromo" id="caddtotalmoneypromo" value="1" onclick="cshtotalmoneypromo();">
                                        <span class="cbmark"></span>
                                        <b><span id="ctotalmoneypromodesc">Promocje TotalMoney 2025</span></b>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <div id="ctotalmoneypromo" style="display: none;">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td align="right">Cashback TotalMoney</td>
                                                    <td align="right"><input type="text" name="ctotalmoneycashback" id="ctotalmoneycashback" value="2000" class="innormal indollar"></td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Zwolnienie z prowizji TotalMoney</td>
                                                    <td align="left" colspan="2">
                                                        <select name="ctotalmoneyprovisionwaiver" id="ctotalmoneyprovisionwaiver">
                                                            <option value="0" selected="">Brak zwolnienia</option>
                                                            <option value="30">30% zwolnienia</option>
                                                            <option value="50">50% zwolnienia</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">Promocyjna marża TotalMoney</td>
                                                    <td align="left" colspan="2">
                                                        <select name="ctotalmoneypromomargin" id="ctotalmoneypromomargin">
                                                            <option value="0" selected="">Standardowa marża</option>
                                                            <option value="0.20">-0.20% przez 12 miesięcy</option>
                                                            <option value="0.30">-0.30% przez 6 miesięcy</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <input name="printit" value="0" type="hidden">
                                    <input type="button" name="x" value="Oblicz kredyt TotalMoney" onclick="calculateTotalMoneyMortgage();">
                                    <input type="button" value="Wyczyść" onclick="clearForm(document.calform);">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
        
        <div class="crighthalf">
            <a name="results"></a>
            <div class="espaceforM">&nbsp;</div>
            <table align="center">
                <tbody>
                    <tr>
                        <td>
                            <h2 class="h2result">Rata TotalMoney: &nbsp; 3 985 zł<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult('S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBUb3RhbE1vbmV5', 0, 'S2Fsa3VsYXRvciBrcmVkeXR1IGhpcG90ZWN6bmVnbyBUb3RhbE1vbmV5', 'UmF0YSBUb3RhbE1vbmV5', 'MyA5ODUgekw=');"></h2>
                            <table cellpadding="3" width="100%">
                                <tbody>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td align="right"><b>TotalMoney</b></td>
                                        <td align="right"><b>Miesięcznie</b></td>
                                        <td align="right"><b>Łącznie</b></td>
                                    </tr>
                                    <tr bgcolor="#dddddd">
                                        <td><b>Rata TotalMoney</b></td>
                                        <td align="right"><b>-</b></td>
                                        <td align="right"><b>3 985 zł</b></td>
                                        <td align="right"><b>1 195 500 zł</b></td>
                                    </tr>
                                    <tr>
                                        <td>Kwota kredytu TotalMoney</td>
                                        <td align="right">520 000 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">520 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Kapitał + odsetki TotalMoney</td>
                                        <td align="right">-</td>
                                        <td align="right">3 815 zł</td>
                                        <td align="right">1 144 500 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Ubezpieczenie TotalMoney</td>
                                        <td align="right">-</td>
                                        <td align="right">152 zł</td>
                                        <td align="right">45 600 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Rachunek kredytowy TotalMoney</td>
                                        <td align="right">-</td>
                                        <td align="right">20 zł</td>
                                        <td align="right">6 000 zł</td>
                                    </tr>
                                    <tr>
                                        <td>Opłaty jednorazowe TotalMoney</td>
                                        <td align="right">17 240 zł</td>
                                        <td align="right">-</td>
                                        <td align="right">17 240 zł</td>
                                    </tr>
                                    <tr bgcolor="#f0f0f0">
                                        <td colspan="4" style="padding-top: 15px;">
                                            <h3>Szczegóły kredytu TotalMoney:</h3>
                                            <p><strong>RRSO TotalMoney:</strong> 8.95% (z wszystkimi opłatami TotalMoney)</p>
                                            <p><strong>Status klienta:</strong> Nowy klient TotalMoney.pl (marża standardowa)</p>
                                            <p><strong>Typ oprocentowania:</strong> Zmienne WIBOR + marża TotalMoney</p>
                                            <p><strong>Całkowity koszt TotalMoney:</strong> 1 213 340 zł</p>
                                            <p><strong>Przepłacone odsetki:</strong> 693 340 zł (133% kwoty kredytu)</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="clear"></div>
        
        <h2 style="padding-bottom: 10px;">Porównanie profili klientów TotalMoney</h2>
        
        <div class="crighthalf">
            <div style="text-align:center;">
                <svg width="300" height="180">
                    <style>
                        .mclgrid{stroke:#999;stroke-width:0.5;} 
                        .mcllegend{font-size:13;font-family:arial,helvetica,sans-serif;fill:#0d233a;} 
                        .mcltitle{fill: #000; font-family:arial,helvetica,sans-serif; font-size: 15px; dominant-baseline:middle;text-anchor:middle;} 
                        .mcllabely{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:middle; text-anchor:end;} 
                        .mcllabelx{fill: #666; font-family:arial,helvetica,sans-serif; font-size: 12px; dominant-baseline:hanging; text-anchor:middle;}
                    </style>
                    <text x="165" y="172" class="mcltitle">Profil TotalMoney</text>
                    <line x1="45" y1="145" x2="292" y2="145" class="mclgrid"></line>
                    <text x="43" y="145" class="mcllabely">3600 zł</text>
                    <line x1="45" y1="109.181" x2="292" y2="109.181" class="mclgrid"></line>
                    <text x="43" y="109.181" class="mcllabely">3800 zł</text>
                    <line x1="45" y1="73.361" x2="292" y2="73.361" class="mclgrid"></line>
                    <text x="43" y="73.361" class="mcllabely">4000 zł</text>
                    
                    <!-- TotalMoney VIP -->
                    <rect x="50" y="125" width="35" height="20" fill="#2e8b57" opacity="0.8"></rect>
                    <text x="67" y="155" class="mcllabelx">VIP</text>
                    <text x="67" y="120" class="mcllabelx" style="fill:#000;">3685 zł</text>
                    
                    <!-- TotalMoney Premium -->
                    <rect x="95" y="115" width="35" height="30" fill="#3cb371" opacity="0.8"></rect>
                    <text x="112" y="155" class="mcllabelx">Premium</text>
                    <text x="112" y="110" class="mcllabelx" style="fill:#000;">3785 zł</text>
                    
                    <!-- TotalMoney Business -->
                    <rect x="140" y="105" width="35" height="40" fill="#66cdaa" opacity="0.8"></rect>
                    <text x="157" y="155" class="mcllabelx">Business</text>
                    <text x="157" y="100" class="mcllabelx" style="fill:#000;">3885 zł</text>
                    
                    <!-- Obecny klient -->
                    <rect x="185" y="95" width="35" height="50" fill="#98fb98" opacity="0.8"></rect>
                    <text x="202" y="155" class="mcllabelx">Obecny</text>
                    <text x="202" y="90" class="mcllabelx" style="fill:#000;">3935 zł</text>
                    
                    <!-- Nowy klient -->
                    <rect x="230" y="85" width="35" height="60" fill="#f0fff0" opacity="0.8"></rect>
                    <text x="247" y="155" class="mcllabelx">Nowy</text>
                    <text x="247" y="80" class="mcllabelx" style="fill:#000;">3985 zł</text>
                    
                    <rect x="48" y="8" width="244" height="137" style="stroke:#666;stroke-width:0.5;fill:none;"></rect>
                </svg>
            </div>
            <br>
        </div>
        
        <div class="clefthalf">
            <div id="amoselect" style="padding-bottom:5px;font-weight:bold;">Profile klientów TotalMoney &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania TotalMoney</a></div>
            <div id="monthlyamo" style="display:none;">
                <table class="cinfoT">
                    <tbody>
                        <tr align="center">
                            <th>Typ oprocentowania TotalMoney</th>
                            <th>Oprocentowanie</th>
                            <th>Miesięczna rata</th>
                            <th>Całkowity koszt</th>
                            <th>Różnica</th>
                        </tr>
                        <tr>
                            <td>Zmienne WIBOR + marża</td>
                            <td>7.85%</td>
                            <td>3 985 zł</td>
                            <td>1 195 500 zł</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>Stałe na 3 lata TotalMoney</td>
                            <td>8.25%</td>
                            <td>4 185 zł</td>
                            <td>1 255 500 zł</td>
                            <td>+60 000 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 5 lat TotalMoney</td>
                            <td>8.55%</td>
                            <td>4 385 zł</td>
                            <td>1 315 500 zł</td>
                            <td>+120 000 zł</td>
                        </tr>
                        <tr>
                            <td>Stałe na 10 lat TotalMoney</td>
                            <td>8.95%</td>
                            <td>4 585 zł</td>
                            <td>1 375 500 zł</td>
                            <td>+180 000 zł</td>
                        </tr>
                        <tr>
                            <td>Mieszane TotalMoney</td>
                            <td>8.05%</td>
                            <td>4 085 zł</td>
                            <td>1 225 500 zł</td>
                            <td>+30 000 zł</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="clear"></div>

        <div style="padding: 20px 0;">
            <h2>Kalkulator kredytu hipotecznego TotalMoney - nowoczesna platforma finansowa 2025</h2>
            <p>TotalMoney.pl to innowacyjna platforma finansowa oferująca nowoczesne rozwiązania kredytowe z wykorzystaniem najnowszych technologii. Nasz kalkulator kredytu hipotecznego TotalMoney pomoże Ci precyzyjnie obliczyć ratę kredytu mieszkaniowego, sprawdzić warunki i porównać profile klientów TotalMoney.pl - platformy łączącej tradycyjne bankowość z nowoczesnymi rozwiązaniami fintech.</p>

            <h3>Profile klientów TotalMoney.pl:</h3>
            <div style="margin: 15px 0;">
                <h4>TotalMoney VIP:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.35% (najniższa marża TotalMoney)</li>
                    <li><strong>Wymagania:</strong> Aktywa minimum 500 000 zł, dochody 25 000+ zł netto</li>
                    <li><strong>Korzyści:</strong> Dedykowany doradca, ekskluzywna obsługa VIP TotalMoney</li>
                    <li><strong>Prowizja:</strong> 0.5% - 1.5% (preferencyjne stawki TotalMoney)</li>
                    <li><strong>Dodatkowe usługi:</strong> Zarządzanie portfelem, inwestycje, concierge TotalMoney</li>
                    <li><strong>Dostępność:</strong> Ograniczona, tylko najbogatsi klienci TotalMoney</li>
                </ul>

                <h4>TotalMoney Premium:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.50% (preferencyjne warunki TotalMoney)</li>
                    <li><strong>Wymagania:</strong> Dochody 15 000+ zł netto, pakiet Premium TotalMoney</li>
                    <li><strong>Korzyści:</strong> Obniżone opłaty, priorytetowa obsługa TotalMoney</li>
                    <li><strong>Prowizja:</strong> 1% - 2% (preferencyjne stawki TotalMoney)</li>
                    <li><strong>Dodatkowe usługi:</strong> Ubezpieczenia, karty premium TotalMoney</li>
                    <li><strong>Dostępność:</strong> Dobra, dla klientów o wysokich dochodach</li>
                </ul>

                <h4>TotalMoney Business:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.60% (oferta dla przedsiębiorców TotalMoney)</li>
                    <li><strong>Wymagania:</strong> Działalność gospodarcza, współpraca biznesowa z TotalMoney</li>
                    <li><strong>Korzyści:</strong> Powiązanie z kredytami biznesowymi, kompleksowa obsługa</li>
                    <li><strong>Prowizja:</strong> 1.5% - 2.5% (negocjowalna dla dużych firm)</li>
                    <li><strong>Dodatkowe usługi:</strong> Kredyty biznesowe, faktoring, leasing TotalMoney</li>
                    <li><strong>Dostępność:</strong> Dobra, dla przedsiębiorców współpracujących z TotalMoney</li>
                </ul>

                <h4>Obecny klient TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.70% (preferencje dla stałych klientów TotalMoney)</li>
                    <li><strong>Wymagania:</strong> Minimum 6 miesięcy jako klient TotalMoney</li>
                    <li><strong>Korzyści:</strong> Obniżona marża, uproszczona procedura TotalMoney</li>
                    <li><strong>Prowizja:</strong> 1.8% - 2.5% (preferencyjne stawki TotalMoney)</li>
                    <li><strong>Dodatkowe usługi:</strong> Możliwość negocjacji warunków TotalMoney</li>
                    <li><strong>Dostępność:</strong> Bardzo dobra dla klientów TotalMoney</li>
                </ul>

                <h4>Nowy klient TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> 7.85% (standardowe warunki TotalMoney)</li>
                    <li><strong>Wymagania:</strong> Standardowe wymagania kredytowe</li>
                    <li><strong>Korzyści:</strong> Dostęp do pełnej oferty TotalMoney</li>
                    <li><strong>Prowizja:</strong> 2.2% - 3% (standardowe stawki TotalMoney)</li>
                    <li><strong>Dodatkowe usługi:</strong> Pakiety startowe TotalMoney</li>
                    <li><strong>Dostępność:</strong> Powszechna, dla wszystkich klientów</li>
                </ul>
            </div>

            <h3>Typy oprocentowania w TotalMoney.pl:</h3>
            <div style="margin: 15px 0;">
                <h4>Zmienne oprocentowanie TotalMoney (WIBOR + marża):</h4>
                <ul>
                    <li><strong>Bazowe oprocentowanie:</strong> WIBOR 3M + marża TotalMoney</li>
                    <li><strong>Aktualna marża:</strong> 3.65% - 4.65% w zależności od profilu TotalMoney</li>
                    <li><strong>Korzyści:</strong> Konkurencyjne oprocentowanie na starcie</li>
                    <li><strong>Ryzyko:</strong> Zmienność wraz z WIBOR</li>
                    <li><strong>Rekomendacja TotalMoney:</strong> Dla klientów akceptujących ryzyko</li>
                </ul>

                <h4>Stałe oprocentowanie na 3 lata TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.40% do zmiennego TotalMoney</li>
                    <li><strong>Okres stały:</strong> 36 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne TotalMoney</li>
                    <li><strong>Korzyści:</strong> Krótkoterminowa stabilność w TotalMoney</li>
                    <li><strong>Rekomendacja TotalMoney:</strong> Dla ostrożnych na początku</li>
                </ul>

                <h4>Stałe oprocentowanie na 5 lat TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.70% do zmiennego TotalMoney</li>
                    <li><strong>Okres stały:</strong> 60 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne TotalMoney</li>
                    <li><strong>Korzyści:</strong> Średnioterminowa przewidywalność w TotalMoney</li>
                    <li><strong>Rekomendacja TotalMoney:</strong> Dla planujących stabilność</li>
                </ul>

                <h4>Stałe oprocentowanie na 10 lat TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +1.10% do zmiennego TotalMoney</li>
                    <li><strong>Okres stały:</strong> 120 miesięcy</li>
                    <li><strong>Po okresie stałym:</strong> Przejście na zmienne TotalMoney</li>
                    <li><strong>Korzyści:</strong> Długoterminowa stabilność w TotalMoney</li>
                    <li><strong>Rekomendacja TotalMoney:</strong> Dla maksymalnej przewidywalności</li>
                </ul>

                <h4>Mieszane oprocentowanie TotalMoney:</h4>
                <ul>
                    <li><strong>Oprocentowanie:</strong> +0.20% do zmiennego TotalMoney</li>
                    <li><strong>Mechanizm:</strong> Część stała, część zmienna</li>
                    <li><strong>Elastyczność:</strong> Kompromis między stabilnością a kosztem</li>
                    <li><strong>Korzyści:</strong> Ograniczone ryzyko zmian w TotalMoney</li>
                    <li><strong>Rekomendacja TotalMoney:</strong> Dla umiarkowanie konserwatywnych</li>
                </ul>
            </div>

            <h3>Opłaty i koszty TotalMoney.pl:</h3>
            <div style="margin: 15px 0;">
                <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                    <tr style="background: #f0f0f0;">
                        <th style="border: 1px solid #ccc; padding: 8px;">Rodzaj opłaty TotalMoney</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Wysokość</th>
                        <th style="border: 1px solid #ccc; padding: 8px;">Uwagi TotalMoney</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Prowizja przygotowawcza</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0% - 3%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zależna od profilu klienta TotalMoney</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rachunek kredytowy</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0 - 30 zł/miesiąc</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Darmowy dla Premium TotalMoney</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie spłaty</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">0.25% - 0.45%</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Rocznie od kwoty kredytu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wycena nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">1200 - 2200 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Przez rzeczoznawcę TotalMoney</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Ubezpieczenie nieruchomości</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">600 - 1800 zł/rok</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Obowiązkowe w TotalMoney</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Koszty notarialne</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">2800 - 5200 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Zależne od kwoty kredytu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ccc; padding: 8px;">Wpis hipoteki</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">200 - 700 zł</td>
                        <td style="border: 1px solid #ccc; padding: 8px;">Opłata sądowa</td>
                    </tr>
                </table>
            </div>

            <h3>Promocje TotalMoney.pl 2025:</h3>
            <ul>
                <li><strong>Cashback TotalMoney:</strong> Do 2 000 zł zwrotu dla nowych klientów</li>
                <li><strong>Zwolnienie z prowizji:</strong> 30% - 50% dla wybranych profili TotalMoney</li>
                <li><strong>Promocyjna marża:</strong> -0.20% przez 12 miesięcy lub -0.30% przez 6 miesięcy</li>
                <li><strong>Darmowa wycena:</strong> Dla klientów VIP i Premium TotalMoney</li>
                <li><strong>Pakiet startowy:</strong> Darmowe prowadzenie konta przez 3 miesiące</li>
                <li><strong>Ubezpieczenia grupowe:</strong> Preferencyjne stawki dla klientów TotalMoney</li>
                <li><strong>Program lojalnościowy:</strong> Punkty za aktywność finansową</li>
            </ul>

            <h3>Zalety kredytu hipotecznego w TotalMoney.pl:</h3>
            <ul>
                <li><strong>Nowoczesna technologia:</strong> Zaawansowane algorytmy oceny kredytowej</li>
                <li><strong>Szybka obsługa:</strong> Proces online z szybką decyzją kredytową</li>
                <li><strong>Elastyczne warunki:</strong> Możliwość personalizacji parametrów kredytu</li>
                <li><strong>Konkurencyjne oprocentowanie:</strong> Atrakcyjne marże dzięki technologii</li>
                <li><strong>Transparentność:</strong> Jasne i przejrzyste warunki TotalMoney</li>
                <li><strong>Mobilna aplikacja:</strong> Pełna obsługa kredytu przez smartfon</li>
                <li><strong>Profesjonalna obsługa:</strong> Doświadczeni doradcy kredytowi TotalMoney</li>
                <li><strong>Innowacyjne rozwiązania:</strong> Fintech łączący z tradycyjną bankowością</li>
            </ul>
        </div>
    </div>

    <div id="right">
        <div id="othercalc">
            <div id="octitle">
                <a href="/kalkulatory-finansowe.html">Kalkulatory finansowe</a>
            </div>
            <div id="occontent">
                <a href="/kalkulator-kredytu-hipotecznego.html">Kalkulator kredytu hipotecznego</a>
                <a href="/santander-kalkulator-kredytu-hipotecznego.html">Santander kalkulator kredytu</a>
                <a href="/pko-bp-kalkulator-kredytu-hipotecznego.html">PKO BP kalkulator kredytu</a>
                <a href="/kalkulator-rat-kredytu-hipotecznego.html">Kalkulator rat kredytu</a>
                <a href="/kalkulator-zdolnosci-kredytu-hipotecznego.html">Kalkulator zdolności kredytowej</a>
                <a href="/mbank-kalkulator-kredytu-hipotecznego.html">mBank kalkulator kredytu</a>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Profile klientów TotalMoney</div>
                <div id="occontent" style="padding: 10px;">
                    <div style="border: 1px solid #ccc; padding: 8px; margin: 5px 0; background: #f9f9f9;">
                        <strong>Wybierz profil TotalMoney:</strong><br>
                        <a href="#" onclick="return setTotalMoneyProfile('vip_totalmoney');">TotalMoney VIP</a><br>
                        <a href="#" onclick="return setTotalMoneyProfile('premium_totalmoney');">TotalMoney Premium</a><br>
                        <a href="#" onclick="return setTotalMoneyProfile('business_totalmoney');">TotalMoney Business</a><br>
                        <a href="#" onclick="return setTotalMoneyProfile('existing_totalmoney');">Obecny klient TotalMoney</a><br>
                        <a href="#" onclick="return setTotalMoneyProfile('new_totalmoney');">Nowy klient TotalMoney</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="padding-top: 20px;">
            <div id="othercalc">
                <div id="octitle">Wyszukaj kalkulator</div>
                <div id="calcSearchOut">
                    <div>
                        <input type="text" id="calcSearchTerm" placeholder="Wpisz nazwę kalkulatora..." style="width: 200px; padding: 5px;">
                        <input type="button" value="Szukaj" onclick="calcSearch();" style="padding: 5px 10px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="/o-nas.html">O nas</a> | 
            <a href="/kontakt.html">Kontakt</a> | 
            <a href="/polityka-prywatnosci.html">Polityka prywatności</a> | 
            <a href="/regulamin.html">Regulamin</a> | 
            <a href="/mapa-strony.html">Mapa strony</a>
        </div>
        <p>&copy; 2025 Kalkulator.pl. Wszystkie prawa zastrzeżone.</p>
        <p>Kalkulator kredytu hipotecznego TotalMoney - oblicz ratę kredytu mieszkaniowego w TotalMoney.pl, sprawdź warunki i promocje.</p>
    </div>
</div>

<div id="tt" style="position:absolute;display:none;background-color: #474747;color: #fff;padding: 8px;border: 1px solid #000;text-align:left;"></div>

<script>
function updateTotalMoneyRate() {
    var profile = document.getElementById('ctotalmoneyclientprofile').value;
    var rateField = document.getElementById('ctotalmoneyinterestrate');
    var baseRate = 7.85;
    
    switch(profile) {
        case 'vip_totalmoney':
            rateField.value = (baseRate - 0.50).toFixed(2);
            break;
        case 'premium_totalmoney':
            rateField.value = (baseRate - 0.35).toFixed(2);
            break;
        case 'business_totalmoney':
            rateField.value = (baseRate - 0.25).toFixed(2);
            break;
        case 'existing_totalmoney':
            rateField.value = (baseRate - 0.15).toFixed(2);
            break;
        case 'new_totalmoney':
            rateField.value = baseRate.toFixed(2);
            break;
    }
}

function updateTotalMoneyInterestType() {
    var type = document.getElementById('ctotalmoneyinteresttype').value;
    var rateField = document.getElementById('ctotalmoneyinterestrate');
    var baseRate = 7.85;
    
    switch(type) {
        case 'variable_totalmoney':
            rateField.value = baseRate.toFixed(2);
            break;
        case 'fixed_3y_totalmoney':
            rateField.value = (baseRate + 0.40).toFixed(2);
            break;
        case 'fixed_5y_totalmoney':
            rateField.value = (baseRate + 0.70).toFixed(2);
            break;
        case 'fixed_10y_totalmoney':
            rateField.value = (baseRate + 1.10).toFixed(2);
            break;
        case 'mixed_totalmoney':
            rateField.value = (baseRate + 0.20).toFixed(2);
            break;
    }
}

function cshtotalmoneyfees() {
    var checkbox = document.getElementById('caddtotalmoneyfees');
    var feesDiv = document.getElementById('ctotalmoneyfees');
    if (checkbox.checked) {
        feesDiv.style.display = 'block';
    } else {
        feesDiv.style.display = 'none';
    }
}

function cshtotalmoneypromo() {
    var checkbox = document.getElementById('caddtotalmoneypromo');
    var promoDiv = document.getElementById('ctotalmoneypromo');
    if (checkbox.checked) {
        promoDiv.style.display = 'block';
    } else {
        promoDiv.style.display = 'none';
    }
}

function setTotalMoneyProfile(profile) {
    document.getElementById('ctotalmoneyclientprofile').value = profile;
    updateTotalMoneyRate();
    return false;
}

function cunitchangetotalmoney(fieldName, unit) {
    var field = document.getElementById(fieldName);
    if (unit === 'p') {
        field.className = field.className.replace('indollar', 'inpct');
    } else {
        field.className = field.className.replace('inpct', 'indollar');
    }
}

function amoChange(type) {
    var monthlyDiv = document.getElementById('monthlyamo');
    var selectDiv = document.getElementById('amoselect');
    
    if (type === 1) {
        monthlyDiv.style.display = 'block';
        selectDiv.innerHTML = 'Typy oprocentowania TotalMoney &nbsp; &nbsp; <a href="#" onclick="amoChange(0);return false;">Profile klientów TotalMoney</a>';
    } else {
        monthlyDiv.style.display = 'none';
        selectDiv.innerHTML = 'Profile klientów TotalMoney &nbsp; &nbsp; <a href="#" onclick="amoChange(1);return false;">Typy oprocentowania TotalMoney</a>';
    }
    return false;
}
</script>

<script>
function calculateTotalMoneyMortgage() {
    // 获取输入值
    var housePrice = parseFloat(document.getElementById('chouseprice').value) || 0;
    var downPayment = parseFloat(document.getElementById('cdownpayment').value) || 0;
    var downPaymentUnit = document.getElementById('cdownpaymentunit').value;
    var loanTerm = parseFloat(document.getElementById('cloanterm').value) || 0;
    var interestRate = parseFloat(document.getElementById('cinterestrate').value) || 0;

    // 获取TotalMoney特殊信息
    var totalMoneyProfile = document.getElementById('ctotalmoneyprofile').value;
    var totalMoneyProgram = document.getElementById('ctotalmoneyprogram').value;
    var totalMoneyService = document.getElementById('ctotalmoneyservice').value;

    // 计算首付和贷款金额
    var downPaymentAmount;
    if (downPaymentUnit === 'p') {
        downPaymentAmount = housePrice * (downPayment / 100);
    } else if (downPaymentUnit === 'd') {
        downPaymentAmount = downPayment;
    } else {
        downPaymentAmount = downPayment;
    }
    var loanAmount = housePrice - downPaymentAmount;

    // 调整利率基于TotalMoney程序
    var adjustedRate = adjustTotalMoneyRate(interestRate, totalMoneyProfile, totalMoneyProgram, totalMoneyService);

    // 计算月供
    var monthlyRate = adjustedRate / 100 / 12;
    var numPayments = loanTerm * 12;
    var monthlyPayment = 0;

    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                        (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }

    // 获取TotalMoney费用
    var costs = {};
    if (document.getElementById('caddoptional') && document.getElementById('caddoptional').checked) {
        costs.provision = calculateCost('cprovision', 'cprovisionunit', loanAmount);
        costs.homeInsurance = calculateCost('chomeins', 'chomeinsunit', loanAmount);
        costs.lifeInsurance = calculateCost('cpmi', 'cpmiunit', loanAmount);
        costs.accountFee = calculateCost('choa', 'choaunit', loanAmount);
        costs.valuation = parseFloat(document.getElementById('cvaluation').value) || 0;
        costs.otherCosts = parseFloat(document.getElementById('cothercost').value) || 0;
    } else {
        costs = {provision: 0, homeInsurance: 0, lifeInsurance: 0, accountFee: 0, valuation: 0, otherCosts: 0};
    }

    // TotalMoney特殊korzyści
    var totalMoneyBenefits = getTotalMoneyBenefits(totalMoneyProfile, totalMoneyProgram, totalMoneyService);

    // 应用TotalMoney korzyści
    if (totalMoneyBenefits.onlineDiscount) costs.provision *= 0.8;
    if (totalMoneyBenefits.digitalAccount) costs.accountFee *= 0.5;
    if (totalMoneyBenefits.compareDiscount) costs.lifeInsurance *= 0.9;
    if (totalMoneyBenefits.freeValuation) costs.valuation = 0;

    // 计算总成本
    var totalInterest = (monthlyPayment * numPayments) - loanAmount;
    var totalHomeInsurance = costs.homeInsurance * loanTerm;
    var totalLifeInsurance = costs.lifeInsurance * loanTerm;
    var totalAccountFees = (costs.accountFee * 12) * loanTerm;

    var totalCost = loanAmount + totalInterest + costs.provision +
                   totalHomeInsurance + totalLifeInsurance + totalAccountFees +
                   costs.valuation + costs.otherCosts;

    var monthlyTotal = monthlyPayment + (costs.homeInsurance/12) + (costs.lifeInsurance/12) + costs.accountFee;

    // 计算RRSO
    var rrso = ((totalCost / loanAmount) - 1) * (12 / loanTerm) * 100;

    // TotalMoney比较分析
    var comparisonAnalysis = getTotalMoneyComparison(adjustedRate, rrso, totalMoneyProfile);

    // 更新显示
    updateTotalMoneyResults(loanAmount, monthlyPayment, totalInterest, costs,
                           totalCost, monthlyTotal, rrso, loanTerm, totalMoneyProfile,
                           totalMoneyProgram, totalMoneyBenefits, adjustedRate, comparisonAnalysis);
}

function adjustTotalMoneyRate(baseRate, profile, program, service) {
    var adjustment = 0;

    // 基于客户档案调整
    switch(profile) {
        case 'online': adjustment -= 0.2; break; // TotalMoney在线客户
        case 'premium': adjustment -= 0.25; break;
        case 'compare': adjustment -= 0.15; break; // 通过比较网站
        case 'existing': adjustment -= 0.1; break;
        case 'referral': adjustment -= 0.3; break; // 推荐客户
    }

    // 基于程序调整
    switch(program) {
        case 'first': adjustment -= 0.2; break;
        case 'family': adjustment -= 0.15; break;
        case 'green': adjustment -= 0.3; break;
        case 'investment': adjustment += 0.25; break;
        case 'quick': adjustment -= 0.1; break; // TotalMoney快速审批
    }

    // 基于服务调整
    switch(service) {
        case 'digital': adjustment -= 0.1; break;
        case 'advisor': adjustment -= 0.05; break;
        case 'compare': adjustment -= 0.15; break;
    }

    return Math.max(baseRate + adjustment, 2.2); // TotalMoney最低2.2%
}

function getTotalMoneyBenefits(profile, program, service) {
    var benefits = {
        onlineDiscount: false,
        digitalAccount: false,
        compareDiscount: false,
        freeValuation: false,
        quickApproval: false,
        marketComparison: false,
        referralBonus: false
    };

    // Online和digital korzyści
    if (profile === 'online' || service === 'digital') {
        benefits.onlineDiscount = true;
        benefits.digitalAccount = true;
        benefits.quickApproval = true;
    }

    // Compare korzyści
    if (profile === 'compare' || service === 'compare') {
        benefits.compareDiscount = true;
        benefits.marketComparison = true;
    }

    // Premium korzyści
    if (profile === 'premium') {
        benefits.freeValuation = true;
        benefits.quickApproval = true;
        benefits.marketComparison = true;
    }

    // Referral korzyści
    if (profile === 'referral') {
        benefits.referralBonus = true;
        benefits.onlineDiscount = true;
    }

    // Program korzyści
    if (program === 'quick') {
        benefits.quickApproval = true;
    }

    return benefits;
}

function getTotalMoneyComparison(rate, rrso, profile) {
    var comparison = {
        marketPosition: '',
        competitiveness: '',
        recommendation: '',
        savings: ''
    };

    // Pozycja rynkowa
    if (rate < 6.5) {
        comparison.marketPosition = 'Bardzo konkurencyjne oprocentowanie';
        comparison.competitiveness = 'Top 10% rynku';
    } else if (rate < 7.5) {
        comparison.marketPosition = 'Konkurencyjne oprocentowanie';
        comparison.competitiveness = 'Top 25% rynku';
    } else if (rate < 8.5) {
        comparison.marketPosition = 'Średnie oprocentowanie rynkowe';
        comparison.competitiveness = 'Średnia rynkowa';
    } else {
        comparison.marketPosition = 'Wysokie oprocentowanie';
        comparison.competitiveness = 'Poniżej średniej';
    }

    // Rekomendacja
    if (rrso < 7.5) {
        comparison.recommendation = 'Zdecydowanie zalecane przez TotalMoney';
        comparison.savings = 'Znaczne oszczędności vs średnia rynkowa';
    } else if (rrso < 8.5) {
        comparison.recommendation = 'Zalecane przez TotalMoney';
        comparison.savings = 'Umiarkowane oszczędności';
    } else if (rrso < 9.5) {
        comparison.recommendation = 'Akceptowalne warunki';
        comparison.savings = 'Minimalne oszczędności';
    } else {
        comparison.recommendation = 'Sprawdź inne opcje';
        comparison.savings = 'Brak oszczędności';
    }

    return comparison;
}

function calculateCost(valueId, unitId, loanAmount) {
    var valueElement = document.getElementById(valueId);
    var unitElement = document.getElementById(unitId);
    if (!valueElement || !unitElement) return 0;

    var value = parseFloat(valueElement.value) || 0;
    var unit = unitElement.value;

    if (unit === 'p') {
        return loanAmount * (value / 100);
    } else {
        return value;
    }
}

function updateTotalMoneyResults(loanAmount, monthlyPayment, totalInterest, costs,
                                totalCost, monthlyTotal, rrso, loanTerm, totalMoneyProfile,
                                totalMoneyProgram, totalMoneyBenefits, adjustedRate, comparisonAnalysis) {
    function formatNumber(num) {
        return Math.round(num).toLocaleString('pl-PL');
    }

    // 更新主要结果
    var resultHeader = document.querySelector('.h2result');
    if (resultHeader) {
        resultHeader.innerHTML =
            'Kredyt TotalMoney: &nbsp; ' + formatNumber(monthlyTotal) + ' zł' +
            '<img src="save.svg" width="19" height="20" title="Zapisz to obliczenie" style="float:right;padding-top:3px;cursor: pointer;" onclick="saveCalResult();">';
    }

    // 更新szczegóły kredytu TotalMoney
    var detailsSection = document.querySelector('tr[bgcolor="#f0f0f0"] td');
    if (detailsSection) {
        var benefitsText = '';
        if (totalMoneyBenefits.onlineDiscount) benefitsText += '• 20% zniżka online<br>';
        if (totalMoneyBenefits.digitalAccount) benefitsText += '• 50% zniżka na prowadzenie rachunku<br>';
        if (totalMoneyBenefits.compareDiscount) benefitsText += '• 10% zniżka za porównanie<br>';
        if (totalMoneyBenefits.freeValuation) benefitsText += '• Bezpłatna wycena nieruchomości<br>';
        if (totalMoneyBenefits.quickApproval) benefitsText += '• Szybka decyzja kredytowa<br>';
        if (totalMoneyBenefits.marketComparison) benefitsText += '• Porównanie z rynkiem<br>';
        if (totalMoneyBenefits.referralBonus) benefitsText += '• Bonus za polecenie<br>';

        detailsSection.innerHTML =
            '<h3>Analiza kredytu TotalMoney:</h3>' +
            '<p><strong>RRSO TotalMoney:</strong> ' + rrso.toFixed(2) + '% (analiza porównawcza)</p>' +
            '<p><strong>Pozycja rynkowa:</strong> ' + comparisonAnalysis.marketPosition + '</p>' +
            '<p><strong>Konkurencyjność:</strong> ' + comparisonAnalysis.competitiveness + '</p>' +
            '<p><strong>Status klienta:</strong> ' + getTotalMoneyClientStatus(totalMoneyProfile) + '</p>' +
            '<p><strong>Program kredytowy:</strong> ' + getTotalMoneyProgramName(totalMoneyProgram) + '</p>' +
            '<p><strong>Korzyści TotalMoney:</strong><br>' + (benefitsText || 'Standardowe warunki') + '</p>' +
            '<p><strong>Rekomendacja:</strong> ' + comparisonAnalysis.recommendation + '</p>' +
            '<p><strong>Oszczędności:</strong> ' + comparisonAnalysis.savings + '</p>';
    }
}

function getTotalMoneyClientStatus(profile) {
    switch(profile) {
        case 'online': return 'Klient online TotalMoney (marża cyfrowa -0.2%)';
        case 'premium': return 'Klient premium TotalMoney (marża preferencyjna -0.25%)';
        case 'compare': return 'Klient z porównania (marża porównawcza -0.15%)';
        case 'existing': return 'Obecny klient TotalMoney (marża preferencyjna -0.1%)';
        case 'referral': return 'Klient polecony (najlepsza marża -0.3%)';
        default: return 'Nowy klient TotalMoney';
    }
}

function getTotalMoneyProgramName(program) {
    switch(program) {
        case 'first': return 'Pierwszy kredyt TotalMoney (dla młodych)';
        case 'family': return 'Kredyt rodzinny TotalMoney (z dopłatami)';
        case 'green': return 'Kredyt zielony TotalMoney (energooszczędny)';
        case 'investment': return 'Kredyt inwestycyjny TotalMoney';
        case 'quick': return 'TotalMoney Quick (szybka decyzja)';
        default: return 'Kredyt hipoteczny standardowy TotalMoney';
    }
}

function clearForm(form) {
    if (document.getElementById('chouseprice')) document.getElementById('chouseprice').value = '500000';
    if (document.getElementById('cdownpayment')) document.getElementById('cdownpayment').value = '20';
    if (document.getElementById('cloanterm')) document.getElementById('cloanterm').value = '25';
    if (document.getElementById('cinterestrate')) document.getElementById('cinterestrate').value = '7.4';
    if (document.getElementById('ctotalmoneyprofile')) document.getElementById('ctotalmoneyprofile').value = 'new';
    if (document.getElementById('ctotalmoneyprogram')) document.getElementById('ctotalmoneyprogram').value = 'standard';
    if (document.getElementById('ctotalmoneyservice')) document.getElementById('ctotalmoneyservice').value = 'standard';
    calculateTotalMoneyMortgage();
}

function saveCalResult() {
    alert('Wyniki kalkulatora TotalMoney zostały zapisane!');
    return false;
}

// 页面加载时执行初始计算
window.onload = function() {
    calculateTotalMoneyMortgage();
};
</script>

</body>
</html>
