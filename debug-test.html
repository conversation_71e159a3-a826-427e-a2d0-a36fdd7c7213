<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    
    <button onclick="testFunction()">Test Function Call</button>
    <button onclick="alert('Direct alert works!')">Test Direct Alert</button>
    
    <div id="result">Click buttons to test</div>

    <script>
        function testFunction() {
            document.getElementById('result').innerHTML = "Function called successfully!";
            alert("Function works!");
        }
        
        // Test if script loads
        console.log("Script loaded successfully");
        alert("Page loaded - script is working");
    </script>
</body>
</html>
