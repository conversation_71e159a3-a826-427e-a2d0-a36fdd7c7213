<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title>FINAL WORKING CALCULATOR</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .left { float: left; width: 45%; margin-right: 5%; }
        .right { float: right; width: 45%; }
        .clear { clear: both; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        td { padding: 10px; border: 1px solid #ddd; }
        input, select { padding: 8px; font-size: 16px; border: 1px solid #ccc; border-radius: 4px; }
        input[type="button"] { padding: 12px 24px; background: #28a745; color: white; border: none; cursor: pointer; margin: 5px; border-radius: 5px; font-size: 16px; }
        input[type="button"]:hover { background: #218838; }
        .result-title { font-size: 24px; font-weight: bold; color: #28a745; margin: 15px 0; text-align: center; background: #f8f9fa; padding: 15px; border-radius: 5px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h2 { color: #555; border-bottom: 2px solid #28a745; padding-bottom: 10px; }
        .highlight { background: #e8f5e9; font-weight: bold; }
        .details { margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 KALKULATOR KREDYTU HIPOTECZNEGO 🏠</h1>
        <p style="text-align: center; color: #666; font-size: 18px;">Oblicz swoją miesięczną ratę kredytu hipotecznego</p>
        
        <div class="left">
            <h2>📊 Dane kredytu</h2>
            <table>
                <tr>
                    <td><strong>Cena nieruchomości:</strong></td>
                    <td><input type="number" id="price" value="500000" style="width: 120px;"> zł</td>
                </tr>
                <tr>
                    <td><strong>Wkład własny:</strong></td>
                    <td><input type="number" id="down" value="20" style="width: 80px;"> %</td>
                </tr>
                <tr>
                    <td><strong>Okres kredytowania:</strong></td>
                    <td><input type="number" id="years" value="25" style="width: 80px;"> lat</td>
                </tr>
                <tr>
                    <td><strong>Oprocentowanie:</strong></td>
                    <td><input type="number" id="rate" value="7.25" step="0.01" style="width: 80px;"> %</td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center; padding: 20px;">
                        <input type="button" value="🧮 OBLICZ RATĘ" onclick="calculate()" style="font-size: 18px; padding: 15px 30px;">
                        <input type="button" value="🔄 WYCZYŚĆ" onclick="reset()" style="background: #6c757d;">
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="right">
            <div class="result-title" id="result">Miesięczna rata: 0 zł</div>
            
            <table style="border: 2px solid #28a745;">
                <tr style="background: #28a745; color: white;">
                    <td><strong>Pozycja</strong></td>
                    <td><strong>Miesięcznie</strong></td>
                    <td><strong>Łącznie</strong></td>
                </tr>
                <tr class="highlight">
                    <td><strong>💰 Rata kredytu</strong></td>
                    <td id="monthly"><strong>0 zł</strong></td>
                    <td id="total"><strong>0 zł</strong></td>
                </tr>
                <tr>
                    <td>🏛️ Podatek od nieruchomości</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>🏠 Ubezpieczenie domu</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>🛡️ Ubezpieczenie kredytu</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr>
                    <td>📋 Opłaty administracyjne</td>
                    <td>0 zł</td>
                    <td>0 zł</td>
                </tr>
                <tr class="highlight" style="border-top: 2px solid #28a745;">
                    <td><strong>💵 ŁĄCZNIE</strong></td>
                    <td id="monthlyTotal"><strong>0 zł</strong></td>
                    <td id="totalCost"><strong>0 zł</strong></td>
                </tr>
            </table>
            
            <div class="details">
                <h3>📈 Szczegóły kredytu:</h3>
                <div id="details">Wprowadź dane i kliknij "OBLICZ RATĘ"</div>
            </div>
        </div>
        
        <div class="clear"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f5e9; border-radius: 10px; text-align: center;">
            <h3>✅ Jeśli widzisz tę stronę i możesz kliknąć przyciski, JavaScript działa poprawnie!</h3>
            <p>Kliknij "OBLICZ RATĘ" aby zobaczyć wyniki obliczeń.</p>
        </div>
    </div>

    <script>
        function calculate() {
            try {
                // Pobierz wartości
                var price = parseFloat(document.getElementById('price').value) || 500000;
                var downPercent = parseFloat(document.getElementById('down').value) || 20;
                var years = parseFloat(document.getElementById('years').value) || 25;
                var rate = parseFloat(document.getElementById('rate').value) || 7.25;
                
                // Oblicz
                var downAmount = price * (downPercent / 100);
                var loanAmount = price - downAmount;
                var monthlyRate = rate / 100 / 12;
                var numPayments = years * 12;
                var monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) / (Math.pow(1 + monthlyRate, numPayments) - 1);
                var totalPayment = monthlyPayment * numPayments;
                var totalInterest = totalPayment - loanAmount;
                
                // Formatuj
                function fmt(n) { 
                    return Math.round(n).toLocaleString('pl-PL') + ' zł'; 
                }
                
                // Aktualizuj wyniki
                document.getElementById('result').innerHTML = '💰 Miesięczna rata: ' + fmt(monthlyPayment);
                document.getElementById('monthly').innerHTML = '<strong>' + fmt(monthlyPayment) + '</strong>';
                document.getElementById('total').innerHTML = '<strong>' + fmt(totalPayment) + '</strong>';
                document.getElementById('monthlyTotal').innerHTML = '<strong>' + fmt(monthlyPayment) + '</strong>';
                document.getElementById('totalCost').innerHTML = '<strong>' + fmt(totalPayment) + '</strong>';
                
                // Szczegóły
                document.getElementById('details').innerHTML = 
                    '<p><strong>🏠 Cena nieruchomości:</strong> ' + fmt(price) + '</p>' +
                    '<p><strong>💵 Kwota kredytu:</strong> ' + fmt(loanAmount) + '</p>' +
                    '<p><strong>💰 Wkład własny:</strong> ' + fmt(downAmount) + ' (' + downPercent + '%)</p>' +
                    '<p><strong>⏰ Okres kredytowania:</strong> ' + years + ' lat (' + numPayments + ' miesięcy)</p>' +
                    '<p><strong>📈 Oprocentowanie:</strong> ' + rate + '% rocznie</p>' +
                    '<p><strong>💸 Przepłacone odsetki:</strong> ' + fmt(totalInterest) + '</p>' +
                    '<p><strong>📊 Stosunek odsetek do kredytu:</strong> ' + Math.round((totalInterest / loanAmount) * 100) + '%</p>';
                
                console.log('Obliczenia zakończone pomyślnie');
                
            } catch (error) {
                alert('Błąd obliczeń: ' + error.message);
                console.error('Błąd:', error);
            }
        }
        
        function reset() {
            document.getElementById('price').value = '500000';
            document.getElementById('down').value = '20';
            document.getElementById('years').value = '25';
            document.getElementById('rate').value = '7.25';
            calculate();
        }
        
        // Automatyczne obliczenie przy załadowaniu
        window.onload = function() {
            console.log('Strona załadowana, uruchamiam obliczenia...');
            calculate();
        };
        
        // Test JavaScript
        console.log('JavaScript załadowany pomyślnie!');
    </script>
</body>
</html>
